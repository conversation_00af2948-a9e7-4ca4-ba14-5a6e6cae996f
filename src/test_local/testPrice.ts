import { PairExtend } from "../lib/type/Pair";
import tools from "../lib/tools";

let FEE = 0.997;
// (x - dx)/(y+dy) = tx / ty
// x * ty - dx * ty = y * tx + dy * tx
// x * ty - y * tx = dy * tx + dx * ty
// x * ty - y * tx = ty * dx + tx * ((dx * y / (x-dx) * 0.997)+1) //getAmountIn

function getEaEb_old(ra : number, rb:number, rb1:number, rc:number, fee1:number, fee2:number= 0.997){
    const Ea = ra * rb1 / (rb1 + rb * fee2);
    const Eb = fee2 * rb * rc / (rb1 + rb * fee2);
    const fee = fee1;
    return [Ea, Eb, fee];
}

function getOptimalAmount(tokenIn:string, pairs:PairExtend[]){
    //pairs >= 2
    let Ea = 0, Eb = 0, ra = 0, rb = 0, rb1 = 0, rc = 0, Efee = 0, fee = 0, tokenOut = "";
    const p0 = pairs[0];
    const p1 = pairs[1];
    
    if(p0.token0 == tokenIn) {
        ra = p0.reserve0;
        rb = p0.reserve1;
        tokenOut = p0.token1;
        Efee = p0.fee0;   
    } else {
        ra = p0.reserve1;
        rb = p0.reserve0;
        tokenOut = p0.token0;
        Efee = p0.fee1;
    }

    if(p1.token0 == tokenOut){
        rb1 = p1.reserve0;
        rc  = p1.reserve1;
        tokenOut = p1.token1;
        fee = p1.fee0;
    } else {
        rb1 = p1.reserve1;
        rc  = p1.reserve0;
        tokenOut = p1.token0;
        fee = p1.fee1;
    }

    Ea = ra * rb1 / (rb1 + rb * fee);
    Eb = fee * rb * rc / (rb1 + rb * fee);

    for(let i = 2 ; i < pairs.length; i++){
        ra = Ea;
        rb = Eb;
        const pn = pairs[i];
        if(pn.token0 == tokenOut){
            rb1 = pn.reserve0;
            rc  = pn.reserve1;
            tokenOut = pn.token1;
            fee = pn.fee0;
        } else {
            rb1 = pn.reserve1;
            rc  = pn.reserve0;
            tokenOut = pn.token0;
            fee = pn.fee1;
        }
        Ea = ra * rb1 / (rb1 + rb * fee);
        Eb = fee * rb * rc / (rb1 + rb * fee);
    }

    //return [Ea, Eb, Efee];
    return (Math.sqrt(Ea * Eb * Efee) - Ea) / Efee;
}

let Ea = 500, Eb = 1000;
let Da = (Math.sqrt(Ea * Eb * FEE) - Ea) / FEE;
let Oa = tools.getAmountOut(Da, Ea, Eb);
console.log(`${Da} - ${Oa}`);

/*
let r0 = 100, r1 = 50, fee1=0.997,
    r1r = 200, r2 = 400, fee2=0.997,
    r2r = 100, r3 = 500, fee3=0.99;

let x0 = 100, y0 = 300,   //y单价0.33
    x1 = 200, y1 = 500;   //y单价0.4

//let x0 = 105.78376222807462, y0 = 283.6439256374527, 
//    x1 = 193.68321330311176, y1 = 516.3560743625474;
console.log(`--- test single pairs ----`);
let [Ea, Eb] = getEaEb_old(x0, y0, y1, x1, FEE);
console.log(`Ea:${Ea}, Eb:${Eb}`);
let isProfit = Ea < Eb;
console.log(`is Profit: ${isProfit}`)
let Da = (Math.sqrt(Ea * Eb * FEE) - Ea) / FEE;

console.log(`Da:${Da}`);

let Db = tools.getAmountOut(Da, x0, y0);
console.log(`Db:${Db}`);
console.log(`x0: ${x0+Da}, y0: ${y0-Db}, price:${(x0+Da)/(y0-Db)}`);

let Dc = tools.getAmountOut(Db, y1, x1);
console.log(`Dc:${Dc}, profit:${Dc-Da}`);
console.log(`x1: ${x1-Dc}, y1: ${y1+Db}, price:${(x1-Dc)/(y1+Db)}`);

console.log(`--- test long pairs ----`);

let [tEa, tEb] = getEaEb_old(r0, r1, r1r, r2, FEE);
let [Ea2, Eb2] = getEaEb_old(tEa, tEb, r2r, r3, FEE);
console.log(`Ea:${Ea2}, Eb:${Eb2}`);
let Da2 = (Math.sqrt(Ea2 * Eb2 * FEE) - Ea2) / FEE;
console.log(`Da:${Da2}`);
let d1 = tools.getAmountOut(Da2, 100, 50);
let d2 = tools.getAmountIn(d1, 200, 400);
let d3 = tools.getAmountIn(d2,100, 500);
console.log("d3: ", d3);
*/