import { ethers, utils } from "ethers";
import { utimes } from "fs";
import path from "path/posix";

/*
0x6192c2a6
000000000000000000000000865bfde337c8afbfff144ff4c29f9404ebb22b15
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50
0000000000000000000000000000000000000000000000c47421ae9891590000
00000000000000000000000000000000000000000000000000000000000000a0
0000000000000000000000007e1667a633ab674d1d3ca82c1ffce55e5ed5ea6f
0000000000000000000000000000000000000000000000000000000000000002
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50
000000000000000000000000173c1374e7c4c5ab3eb90cf6b32668b5dcbaa3fd
*/

/*
0x6192c2a6
000000000000000000000000865bfde337c8afbfff144ff4c29f9404ebb22b15
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50
00000000000000000000000000000000000000000000014542ba12a337c00000
00000000000000000000000000000000000000000000000000000000000000a0
000000000000000000000000769a76a57f8c136faba7cd8de92ab751a1ebeacf
0000000000000000000000000000000000000000000000000000000000000002
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50
000000000000000000000000173c1374e7c4c5ab3eb90cf6b32668b5dcbaa3fd
 */

/*
https://www.oklink.com/oec/tx/0x675fc28e1072533b721fefa5854ae89b60a35297da5828888ef00e50da53a908
0x6c5c91a8 (buy wave)
000000000000000000000000865bfde337c8afbfff144ff4c29f9404ebb22b15 //address
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50 //address
00000000000000000000000000000000000000000000007ff1694c3ed390d367 //uint256
00000000000000000000000000000000000000000000000000000000000000e0 //address[]
0000000000000000000000000000000000000000000000000000000000000140 //uint256
000000000000000000000000173c1374e7c4c5ab3eb90cf6b32668b5dcbaa3fd //address
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50 //address
0000000000000000000000000000000000000000000000000000000000000002
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50
000000000000000000000000173c1374e7c4c5ab3eb90cf6b32668b5dcbaa3fd
0000000000000000000000000000000000000000000000000000000000000000 //uint256
*/

/*
https://www.oklink.com/oec/tx/0x675fc28e1072533b721fefa5854ae89b60a35297da5828888ef00e50da53a908
0x6c5c91a8 (buy jf)
000000000000000000000000069a306a638ac9d3a68a6bd8be898774c073dcb3 //address
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50 //address
0000000000000000000000000000000000000000000000000de0b6b3a7640000 //uint256
00000000000000000000000000000000000000000000000000000000000000e0 //address[]
0000000000000000000000000000000000000000000000000000000000000140 //uint256
0000000000000000000000005fac926bf1e638944bb16fb5b787b5ba4bc85b0a //address
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50 //address
0000000000000000000000000000000000000000000000000000000000000002
000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50
0000000000000000000000005fac926bf1e638944bb16fb5b787b5ba4bc85b0a
0000000000000000000000000000000000000000000000000000000000000000 //uint256
*/

let dataBuyback = '0x6192c2a6000000000000000000000000865bfde337c8afbfff144ff4c29f9404ebb22b15000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c500000000000000000000000000000000000000000000000c47421ae989159000000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000007e1667a633ab674d1d3ca82c1ffce55e5ed5ea6f0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c50000000000000000000000000173c1374e7c4c5ab3eb90cf6b32668b5dcbaa3fd';
//let iface = new ethers.utils.Interface(["buyback(address router, address inToken, uint256 amount, uint256 channle, address to, address[] path)"]);
//const decode = iface.decodeFunctionData("buyback", data);
//console.log(decode);


let deBuyback = ethers.utils.defaultAbiCoder.decode(
    ['address', 'address', 'uint256', 'address[]', 'address'],
    ethers.utils.hexDataSlice(dataBuyback, 4));

console.log(deBuyback);
console.log(utils.formatUnits(deBuyback[2], 18));
//console.log(de[5])

const dataZap = '0x6c5c91a8000000000000000000000000069a306a638ac9d3a68a6bd8be898774c073dcb3000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c500000000000000000000000000000000000000000000000000de0b6b3a764000000000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000001400000000000000000000000005fac926bf1e638944bb16fb5b787b5ba4bc85b0a000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c500000000000000000000000000000000000000000000000000000000000000002000000000000000000000000382bb369d343125bfb2117af9c149795c6c65c500000000000000000000000005fac926bf1e638944bb16fb5b787b5ba4bc85b0a0000000000000000000000000000000000000000000000000000000000000000';
let deZap = ethers.utils.defaultAbiCoder.decode(
    ['address', 'address', 'uint256', 'address[]', 'uint256', 'address', 'address', 'uint256'],
    ethers.utils.hexDataSlice(dataZap, 4));

console.log(deZap);
const [router,  tokenIn,  amountIn,  pathR, channel, fromToken, toToken, delta] = deZap;
console.log(tokenIn);
console.log(pathR[0])