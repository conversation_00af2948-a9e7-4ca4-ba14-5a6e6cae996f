import { BigNumber, utils } from "ethers";
import bot from "../bot";
import { macro } from "../lib/macro";
import tools from "../lib/tools";

async function main(){
    await bot.init(macro.CHAIN.ONE);
    const {bull, bear} = bot.client;
    bull.getOperator();
}

function test(){
    let [r0, r1] = [1000, 1000];
    console.log(`price: ${r0}/${r1} ${r0/r1}`);

    let amountIn = 5000000;
    let amountOut = tools.getAmountOut(amountIn, 1000, 1000);
    console.log(amountOut);
    r0 += amountIn;
    r1 -= amountOut;
    console.log(`price: ${r0}/${r1} ${r0/r1}`);
    let backOut = tools.getAmountOut(amountOut * 1.003009027081244, r1, r0);
    console.log(backOut, amountOut)
    r0 -= backOut;
    r1 += amountOut * 1.003009027081244;
    console.log(`price: ${r0}/${r1} ${r0/r1}`);
}

function test1(){
    let [a0, a1] = [1000000, 1000000];
    let amountIn = 1000;
    let slippageOut = 350;
    let amountOut = tools.getAmountOut(amountIn, a0, a1);
    console.log("amountIn: ", amountIn, " amountOut:", amountOut, " slippageOut:", slippageOut);
    //方案1
    //let swIn = amountIn - tools.getAmountIn(slippageOut, a0, a1);
    //let swOut = tools.getAmountOut(swIn, a0, a1);
    
    //方案2
    // (a0+d0)/(a1-d1) = amountIn/slippageOut
    // a0 * slippageOut + d0 * slippageOut = a1 * amountIn - d1 * amountIn
    // d0 * slippageOut + d1 * amountIn = a1 * amountIn - a0 * slippageOut
    // --> d1 = d0 * 0.997 * a1 / a0 + d0 * 0.997
    // d0 * slippageOut + d0 * a1 * amountIn * 0.997/a0 + a1 * amountIn / a0 = a1 * amountIn - a0 * slippageOut
    //d0 * (slippageOut + a1 * amountIn * 0.997/a0) = a1 * amountIn - a0 * slippageOut - a1 * amountIn / a0;
    let swIn = (a1 * amountIn - a0 * slippageOut - a1 * amountIn / a0) / (slippageOut + a1 * amountIn * 0.997/a0);
    let swOut = tools.getAmountOut(swIn, a0, a1);

    //方案3
    //let swOut = amountOut - slippageOut;
    //let swIn = tools.getAmountIn(swOut, a0, a1);

    console.log("swIn: ", swIn, " swOut: ", swOut);

    let [sw0,sw1] = [a0+swIn, a1-swOut];
    console.log(sw0,sw1);
    //console.log(a0*a1, sw0*sw1);
    console.log(`-----------------`);
    let afterOut = tools.getAmountOut(amountIn, sw0, sw1);
    console.log(amountIn, afterOut);
}


function loss(x:number, path : Array<{r0:number, r1:number,f0:number,f1:number}>) {
    return Math.abs(calc(x, path) - x);
}

function gradient(x:number, path : Array<{r0:number, r1:number,f0:number,f1:number}>) {
    const epsilon = 1e-6;
    return (loss(x + epsilon, path) - loss(x - epsilon, path)) / (2 * epsilon);
}

//其中，learningRate 是学习率，控制每次更新的步长大小；maxIterations 是最大迭代次数；tolerance 是收敛容忍度，当梯度的绝对值小于该值时，认为已经达到了最优解。
function optimize(x:number, path : Array<{r0:number, r1:number,f0:number,f1:number}>, learningRate = 1e-4, maxIterations = 1000, tolerance = 1e-6) {
    let i = 0;
    while (i < maxIterations) {
      const delta = learningRate * gradient(x, path);
      if (Math.abs(delta) < tolerance) {
        break;
      }
      x += delta;
      i++;
    }
    console.log("calc time:", i);
    return x;
}


function findOptimalX(path : Array<{r0:number, r1:number,f0:number,f1:number}>, learningRate = 1, maxIterations = 10000, tolerance = 1e-6) {
    let x = 1;
    return optimize(x, path, learningRate, maxIterations, tolerance);
}

function getAmountOut(amountIn : number, reserveIn:number, reserveOut:number, fee = 0.997){
    const amountInWithFee = amountIn * fee;
    const numerator = amountInWithFee * reserveOut;
    const denominator = reserveIn + amountInWithFee;
    const amountOut = numerator / denominator;
    return amountOut;
}

function calc(input:number, path:Array<{r0:number, r1:number,f0:number}>){
    let x = input, y = 0;
    for(let i = 0 ; i < path.length; i++){
        y = getAmountOut(x, path[i].r0, path[i].r1, path[i].f0);
        x = y;
    }
    //console.log(y);
    return y;
}

test1();



const a1 = {r0 : 1000, r1 : 1000, f0:0.997};
const a2 = {r0 : 1000, r1 : 2000, f0:0.997};
const a3 = {r0 : 500, r1 : 400, f0:0.997};
const path = [a1,a2,a3];

let x = 0;
const xOut = calc(x, path);
console.log(`x = ${x}, xOut = ${xOut}`);

function hexToNum(){
    //let bnToHex = BigNumber.from('32');
    //console.log(bnToHex.toHexString());
    const l1 = BigNumber.from("0x00000000000000000000000000000000000000000000003635c9adc5dea00000");
    console.log(utils.formatEther(l1));
    const l2 = BigNumber.from("0x000000000000000000000000000000000000000000000043c33c193756480000");
    console.log(utils.formatEther(l2));
}

//test();
//test1();
//hexToNum()

//main();