//已知条件
//const init = amountIn;
//let amountOut = getAmountOut(amountIn, p[0].r0, p[0].r1, p[0].fee);
//满足条件
//(p[0].r0 - p[0].fee * amountIn) * (p[0].r1 - amountOut) == p[0].r0 * p[0].r1;
//amountIn = amountOut;
//amountOut = getAmountOut(amountIn, p[1].r0, p[1].r1, p[1].fee);
//满足条件
//(p[1].r0 - p[1].fee * amountIn) * (p[1].r1 - amountOut) == p[1].r0 * p[1].r1;
//amountIn = amountOut;
//amountOut = getAmountOut(amountIn, p[2].r0, p[2].r1, p[2].fee);
//满足条件
//(p[2].r0 - p[2].fee * amountIn) * (p[2].r1 - amountOut) == p[2].r0 * p[2].r1;



type Pair = {
    r0: number;
    r1: number;
    fee: number;
};

const p : Pair[] = [
    {
        r0 : 100,
        r1 : 200,
        fee: 0.997
    },
    {
        r0 : 20000,
        r1 : 500000,
        fee: 0.998
    },
    {
        r0 : 50000,
        r1 : 10000,
        fee: 0.997
    }
];

function getAmountOut(amountIn : number, reserveIn:number, reserveOut:number, fee = 0.997){
    const amountInWithFee = amountIn * fee;
    const numerator = amountInWithFee * reserveOut;
    const denominator = reserveIn + amountInWithFee;
    const amountOut = numerator / denominator;
    return amountOut;
}

let amountIn = 0;

function calc(amountIn:number, pairs:Pair[]){
    let amountOut = 0;
    for(let pair of pairs){
        amountOut = getAmountOut(amountIn, pair.r0, pair.r1, pair.fee);
        amountIn = amountOut;
    }
    return amountOut;
}

//使用typescript完成下面的函数，根据pairs的值，计算出一个amountIn，使得calc(amountIn, pairs) - amountIn 得到最大值，如果不存在这个值则返回0，分别使用黄金分割法和二分法计算
//function findMaxAmountInGoldenSection(pairs: Pair[]){}
//function findMaxAmountInBisecion(pairs: Pair[]){}

//Golden Section Search algorithm
function findMaxAmountInGoldenSection(pairs: Pair[]): number {
    console.time("GoldenSection");
    let low = 0.005;
    let high = 20000;
    const tolerance = low * 300
    let step = 0;
    const stepMax = 40;
    const phi = 1.618034;

    let x1 = high - ((high - low) / phi);
    let x2 = low  + ((high - low) / phi);

    let amountOut1 = calc(x1, pairs);
    let amountOut2 = calc(x2, pairs);

    let f1 = amountOut1 - x1;
    let f2 = amountOut2 - x2;

    while(high > tolerance + low){
        if(f1 > f2){
            high = x2;
            x2 = x1;
            f2 = f1;
            x1 = high - ((high - low) / phi);
            amountOut1 = calc(x1, pairs);
            f1 = amountOut1 - x1;
        } else if (f1 < f2){
            low = x1;
            x1 = x2;
            f1 = f2;
            x2 = low  + ((high - low) / phi);
            amountOut2 = calc(x2, pairs);
            f2 = amountOut2 - x2;
        } else {
            low = x1;
            high = x2;
            x1 = high - ((high - low) / phi);
            x2 = low  + ((high - low) / phi);
            amountOut1 = calc(x1, pairs);
            amountOut2 = calc(x2, pairs);
            f1 = amountOut1 - x1;
            f2 = amountOut2 - x2;
        }
        step++;
        console.log(`[${step.toString().padStart(3, " ")}] fx1:${f1} fx2:${f2} lower:${low} upper:${high}`);
    }
    console.log("GoldenSection step:",step);
    console.timeEnd("GoldenSection");
    return (high + low) / 2;
}

function findMaxAmountInBisecion(pairs: Pair[]): number {
    console.time("Bisecion");
    let calcTime = 0;
    const tolerance = 0.01;
    let lowerBound = 0.005;
    let upperBound = 20000; // Choose a large enough upper bound for searching

    while (upperBound > lowerBound + tolerance) {
        const mid1 = lowerBound + (upperBound - lowerBound) / 3;
        const mid2 = upperBound - (upperBound - lowerBound) / 3;

        const fmid1 = calc(mid1, pairs) - mid1;
        const fmid2 = calc(mid2, pairs) - mid2;

        if (fmid1 < fmid2) {
            lowerBound = mid1;
        } else {
            upperBound = mid2;
        }
        calcTime++;
    }
    console.log("Bisection calcTime:",calcTime);
    console.timeEnd("Bisecion");
    return (lowerBound + upperBound) / 2;
}


let optimalAmountIn = findMaxAmountInGoldenSection(p);
//const optimalAmountIn = 44;

console.log("GoldenSection Max Amount In:", optimalAmountIn);
console.log("GoldenSection Max Amount Out:", calc(optimalAmountIn, p));
console.log("GoldenSectionreward:", calc(optimalAmountIn, p) - optimalAmountIn);


const optimalAmountInB = findMaxAmountInBisecion(p);
//const optimalAmountIn = 44;

console.log("Bisection Max Amount In:", optimalAmountInB);
console.log("Bisection Max Amount Out:", calc(optimalAmountInB, p));
console.log("Bisection reward:", calc(optimalAmountInB, p) - optimalAmountInB);
