import { ethers } from "ethers";
import fs from "fs";
const file = "./src/test_local/bevm.json";

function createAccounts(){
    let data : {[id:string]:{address:string,key:string}} = {};
    for(let i = 0; i < 1000 ; i++){
        let wallet = ethers.Wallet.createRandom(); //.connect(provider)
        console.log(`[${i}] address: `, wallet.address);
        console.log("key: ", wallet.privateKey);
        data[i.toString()] = {
            address : wallet.address,
            key : wallet.privateKey
        }
    }
    console.log(JSON.stringify(data));
}

function readId(from:number, to:number){
    let res = JSON.parse(fs.readFileSync(file, "utf-8"));
    let outPut = [];
    for(const [k,v] of Object.entries(res)){
        if(Number(k) >= from && Number(k) <= to){
            outPut.push({
                id : k,
                address : (v as any).address
            })
        }
    }
    //console.log(JSON.stringify(outPut, undefined, 4));
    console.log(JSON.stringify(outPut));
}

//createAccounts();
readId(0, 999);