import tools from "../lib/tools";


let usdt = 911984.058319222;
let celt = 8440876.227933113;
let buyUsdt = 4343.731;
let amountOutMax = tools.getAmountOut(buyUsdt,usdt,celt);

console.log(buyUsdt, amountOutMax);
//usdt += buyUsdt;
//celt -= amountOutMax;
console.log("usdt:",usdt, ", che:",celt, 'totla:', usdt * celt);

let expetAmout = 37025.337;
let snipAmount = amountOutMax - expetAmout;
//let snipUsdt = Oracle.getAmountIn(snipAmount,usdt,celt);
let snipToken = ((amountOutMax-expetAmout)/amountOutMax) * celt;// / 2 / 2;
let snipUsdt = tools.getAmountIn(snipToken,usdt,celt);
console.log(snipUsdt, snipToken, "snipAmout: ", snipAmount);

/*let bot = 9300;
let botAmountOut = Oracle.getAmountOut(bot,usdt,celt);
console.log("bot: ", bot, botAmountOut);*/

usdt += snipUsdt;
celt -= snipToken;
console.log("usdt:",usdt, ", che:",celt, 'totla:', usdt * celt);
console.log(tools.getAmountOut(buyUsdt,usdt,celt));

/*
10/5/2021, 8:21:15 PM 0xCd0b28214018Da0a3c88Cd4539b36aA586173ef6  [buy*] crystal : (usdt) 500.0 -> 6990.803/6930.661 -0.868%
 *** big buy (crystal) *** tx: 0x5e66f388a31a90af92331ea076def37ed504995828e4788e690e2488b65a71b0
snip: $ -3.1797391159434945  -0.868%
 SUPER BUY !!!! 500/13210.909775078184
 ... reach max holding*/
// tx: 0x5e66f388a31a90af92331ea076def37ed504995828e4788e690e2488b65a71b0
// usdt: 500.0, crystal: 6990.803

//snip807: usdt:54.4


//tx: 0x51155be6541411cff387f3c8d7a4899fae89c693da389af31a8026b5ff9fd86a
//*** big buy (crystal) *** tx: 0x51155be6541411cff387f3c8d7a4899fae89c693da389af31a8026b5ff9fd86a
//snip: $ 3.239360836512614  2.816%
//snip807: usdt -589.15
//Crystal: 191525.0369192553
//USDT: 13182.900545143246 

//https://www.oklink.com/okexchain/block/5987789
//[buy*]  celt : (usdt) 4343.731 -> 37025.337/42536.681 12.957%
//snip807: 9,300.875826608639

//usdt: 911984.058319222
//celt: 8440876.227933113


class Foo {
    name:string="";
    age? = 10;
    constructor(name: string, age: number, group?: string) {}
 }
let foo = new Foo("test", 2);

console.log(foo.name);