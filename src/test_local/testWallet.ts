//from bard and chatgpt

import { ethers } from "ethers";

const keys = [
    "0x45a915e4d060149eb4365960e6a7a45f334393093061116b197e3240065ff2d8",
    "0xb01e17991613f52009552213f49e753e746d9c11348451d20419815b854f14a2",
    "0x2c69a013d99f411e9437669c0654c3d89f4e9b945b83c19127024141c81c0f5f",
    "0x46682172f2095960689707778724962a42797534828081a1805a1208f8739785",
    "0xb1369f64050c130022493774775c985974866004678359451534a02488525309",
    "0x3967598b8578633a017372840703568071887025274547387919920977105274",
    "0x7334192806077826210087330742057067258349611242394242332813642662",
    "0x3026651105241139490446385243772536440424700270379419914372053369",
    "0x8151797737129798510262638969390071202700356172537377027904871726",
    "0x7887021471796636896029107789183511976844854158230628205110457277",
    "0x6703752909295561674994744298504457829391972724394456046280836228",
    "0x0614653711455222706657393268242612448331113789226595670231828994",
    "0x8100161797192113863582957410703209827608459758817262349862858801",
    "0x0815778916832211155367561412459781129234358255151276005025422272",
    "0x5945134924651104034011116498632305534420964925874454279322096790",
    "0x5428951280102127922692371554970384127718510041198846870533040817",
    "0x6474210066257982879795264054401822447891335030253475668884265271",
    "0x5794325361016934714084243922982029024403505919840110505227760455",
    "0x3492046287301034768079274846790812643852304005118542408432831589",
    "0x2298613886391704130404109415270928153499899321794321658077711972",
    "0x1012103337349002330548158424880532183254264548803881138354429667",
    "0x6546452615100177255547111349869525771447737784738568090398235016",
    "0x6311508658010100256793245752144941346919171970246435001464649344",
    "0x8947790230881753684382272444191229978829325132295182131621164233",
    "0x3550488670500391170842680873184304817634396175511169561250174156",
    "0x5536342150114112976356182683051519146687293822291133185929379640",
    "0x4202913281331253457302688677333538860215408477236779707526073911",
    "0x3221636270424704288435436426720188168982800727062986238669995364",
    "0x6611999987826257332607141003745464948225253079491366913511428483",
    "0x4591609663960768623264345944027696458562837897152716295166398781",
    "0x7491861631266282615480345134039590915622096551689235668411117762",
    "0x2545218404203535500410648915315013141140200609887921549261358315",
    "0x0505678431741573290600913983664668569262080626612068945493435988",
    "0x4459694556636616544387761627948072105075750471848271051230264785",
    "0x7784510875405064460752204981088008245272236077641506465286453725",
    "0x5594453986108462286101965015100699103255800893830518125362076366",
    "0x7209150746064713041755246252107225172873513884115007842208250685",
    "0x5660287746714649093850337588446381607067415643910537993746255996",
    "0x5410595811390795622465961649071096080322164695188210807441101474",
    "0x0941894855459148943944795944045893089811114847303154607334064639",
    "0x1651312938436695417056589563025833404094692441196311684910623902",
    "0x6281983105363524681288903838712865994300651850352300258721346074",
];

const wallets = keys.map((k)=>{
    return new ethers.Wallet(k.slice(2));
})

wallets.forEach(w => {
    console.log(w.address);
});