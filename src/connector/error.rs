use std::{collections::HashSet, fmt};

use crate::vira::errors::{DEXError, EventLogError};

use alloy::{network::{Ethereum, Network}, primitives::Address, rpc::types::Log, transports::TransportError};

use thiserror::Error;


// Define newtype wrappers to distinguish between the SendErrors
#[derive(Debug)]
pub struct StateChangeSendErrorWrapper(pub tokio::sync::mpsc::error::SendError<Vec<Address>>);

#[derive(Debug)]
pub struct HeaderSendErrorWrapper(pub tokio::sync::mpsc::error::SendError<<Ethereum as Network>::HeaderResponse>);

#[derive(Debug)]
pub struct LogsSendErrorWrapper(pub tokio::sync::mpsc::error::SendError<Vec<Log>>);

#[derive(Debug)]
pub struct UpdatedPoolsSendErrorWrapper(pub tokio::sync::mpsc::error::SendError<HashSet<Address>>);

// Implement Display for StateChangeSendErrorWrapper
impl fmt::Display for StateChangeSendErrorWrapper {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "StateChange send error: {}", self.0)
    }
}
// Implement Error for StateChangeSendErrorWrapper
impl std::error::Error for StateChangeSendErrorWrapper {}


// Implement Display for BlockSendErrorWrapper
impl fmt::Display for HeaderSendErrorWrapper {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Block send error: {}", self.0)
    }
}

// Implement Error for BlockSendErrorWrapper
impl std::error::Error for HeaderSendErrorWrapper {}

// Implement Display for LogsSendErrorWrapper
impl fmt::Display for LogsSendErrorWrapper {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Logs send error: {}", self.0)
    }
}
// Implement Error for LogsSendErrorWrapper
impl std::error::Error for LogsSendErrorWrapper {}

// Implement Display for UpdatedPoolsSendErrorWrapper
impl fmt::Display for UpdatedPoolsSendErrorWrapper {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "UpdatedPools send error: {}", self.0)
    }
}
// Implement Error for UpdatedPoolsSendErrorWrapper
impl std::error::Error for UpdatedPoolsSendErrorWrapper {}

#[derive(Error, Debug)]
pub enum ConnectorError {
    #[error(transparent)]
    TransportError(#[from] TransportError),
    #[error(transparent)]
    ContractError(#[from] alloy::contract::Error),
    #[error(transparent)]
    ABICodecError(#[from] alloy::dyn_abi::Error),
    #[error(transparent)]
    EthABIError(#[from] alloy::sol_types::Error),
    #[error(transparent)]
    AMMError(#[from] DEXError),
    #[error(transparent)]
    WalletError(#[from] alloy::signers::local::LocalSignerError),
    #[error("Insufficient wallet funds for execution")]
    _InsufficientWalletFunds(),
    #[error(transparent)]
    EventLogError(#[from] EventLogError),
    #[error("Block number not found")]
    _BlockNumberNotFound,
    #[error(transparent)]
    StateChangeSendError(#[from] StateChangeSendErrorWrapper),
    #[error(transparent)]
    BlockSendError(#[from] HeaderSendErrorWrapper),
    #[error(transparent)]
    LogsSendError(#[from] LogsSendErrorWrapper),
    #[error(transparent)]
    UpdatedPoolsSendError(#[from] UpdatedPoolsSendErrorWrapper),
    #[error("Already listening for state changes")]
    _AlreadyListeningForStateChanges,
    #[error(transparent)]
    JoinError(#[from] tokio::task::JoinError),
}
