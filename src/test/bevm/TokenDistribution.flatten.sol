// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20 ^0.8.23;

// lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol

// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/draft-IERC6093.sol)

/**
 * @dev Standard ERC20 Errors
 * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC20 tokens.
 */
interface IERC20Errors {
    /**
     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.
     * @param sender Address whose tokens are being transferred.
     * @param balance Current balance for the interacting account.
     * @param needed Minimum amount required to perform a transfer.
     */
    error ERC20InsufficientBalance(address sender, uint256 balance, uint256 needed);

    /**
     * @dev Indicates a failure with the token `sender`. Used in transfers.
     * @param sender Address whose tokens are being transferred.
     */
    error ERC20InvalidSender(address sender);

    /**
     * @dev Indicates a failure with the token `receiver`. Used in transfers.
     * @param receiver Address to which tokens are being transferred.
     */
    error ERC20InvalidReceiver(address receiver);

    /**
     * @dev Indicates a failure with the `spender`’s `allowance`. Used in transfers.
     * @param spender Address that may be allowed to operate on tokens without being their owner.
     * @param allowance Amount of tokens a `spender` is allowed to operate with.
     * @param needed Minimum amount required to perform a transfer.
     */
    error ERC20InsufficientAllowance(address spender, uint256 allowance, uint256 needed);

    /**
     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.
     * @param approver Address initiating an approval operation.
     */
    error ERC20InvalidApprover(address approver);

    /**
     * @dev Indicates a failure with the `spender` to be approved. Used in approvals.
     * @param spender Address that may be allowed to operate on tokens without being their owner.
     */
    error ERC20InvalidSpender(address spender);
}

/**
 * @dev Standard ERC721 Errors
 * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC721 tokens.
 */
interface IERC721Errors {
    /**
     * @dev Indicates that an address can't be an owner. For example, `address(0)` is a forbidden owner in EIP-20.
     * Used in balance queries.
     * @param owner Address of the current owner of a token.
     */
    error ERC721InvalidOwner(address owner);

    /**
     * @dev Indicates a `tokenId` whose `owner` is the zero address.
     * @param tokenId Identifier number of a token.
     */
    error ERC721NonexistentToken(uint256 tokenId);

    /**
     * @dev Indicates an error related to the ownership over a particular token. Used in transfers.
     * @param sender Address whose tokens are being transferred.
     * @param tokenId Identifier number of a token.
     * @param owner Address of the current owner of a token.
     */
    error ERC721IncorrectOwner(address sender, uint256 tokenId, address owner);

    /**
     * @dev Indicates a failure with the token `sender`. Used in transfers.
     * @param sender Address whose tokens are being transferred.
     */
    error ERC721InvalidSender(address sender);

    /**
     * @dev Indicates a failure with the token `receiver`. Used in transfers.
     * @param receiver Address to which tokens are being transferred.
     */
    error ERC721InvalidReceiver(address receiver);

    /**
     * @dev Indicates a failure with the `operator`’s approval. Used in transfers.
     * @param operator Address that may be allowed to operate on tokens without being their owner.
     * @param tokenId Identifier number of a token.
     */
    error ERC721InsufficientApproval(address operator, uint256 tokenId);

    /**
     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.
     * @param approver Address initiating an approval operation.
     */
    error ERC721InvalidApprover(address approver);

    /**
     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.
     * @param operator Address that may be allowed to operate on tokens without being their owner.
     */
    error ERC721InvalidOperator(address operator);
}

/**
 * @dev Standard ERC1155 Errors
 * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC1155 tokens.
 */
interface IERC1155Errors {
    /**
     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.
     * @param sender Address whose tokens are being transferred.
     * @param balance Current balance for the interacting account.
     * @param needed Minimum amount required to perform a transfer.
     * @param tokenId Identifier number of a token.
     */
    error ERC1155InsufficientBalance(address sender, uint256 balance, uint256 needed, uint256 tokenId);

    /**
     * @dev Indicates a failure with the token `sender`. Used in transfers.
     * @param sender Address whose tokens are being transferred.
     */
    error ERC1155InvalidSender(address sender);

    /**
     * @dev Indicates a failure with the token `receiver`. Used in transfers.
     * @param receiver Address to which tokens are being transferred.
     */
    error ERC1155InvalidReceiver(address receiver);

    /**
     * @dev Indicates a failure with the `operator`’s approval. Used in transfers.
     * @param operator Address that may be allowed to operate on tokens without being their owner.
     * @param owner Address of the current owner of a token.
     */
    error ERC1155MissingApprovalForAll(address operator, address owner);

    /**
     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.
     * @param approver Address initiating an approval operation.
     */
    error ERC1155InvalidApprover(address approver);

    /**
     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.
     * @param operator Address that may be allowed to operate on tokens without being their owner.
     */
    error ERC1155InvalidOperator(address operator);

    /**
     * @dev Indicates an array length mismatch between ids and values in a safeBatchTransferFrom operation.
     * Used in batch transfers.
     * @param idsLength Length of the array of token identifiers
     * @param valuesLength Length of the array of token amounts
     */
    error ERC1155InvalidArrayLength(uint256 idsLength, uint256 valuesLength);
}

// lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol

// OpenZeppelin Contracts (last updated v5.0.0) (token/ERC20/IERC20.sol)

/**
 * @dev Interface of the ERC20 standard as defined in the EIP.
 */
interface IERC20 {
    /**
     * @dev Emitted when `value` tokens are moved from one account (`from`) to
     * another (`to`).
     *
     * Note that `value` may be zero.
     */
    event Transfer(address indexed from, address indexed to, uint256 value);

    /**
     * @dev Emitted when the allowance of a `spender` for an `owner` is set by
     * a call to {approve}. `value` is the new allowance.
     */
    event Approval(address indexed owner, address indexed spender, uint256 value);

    /**
     * @dev Returns the value of tokens in existence.
     */
    function totalSupply() external view returns (uint256);

    /**
     * @dev Returns the value of tokens owned by `account`.
     */
    function balanceOf(address account) external view returns (uint256);

    /**
     * @dev Moves a `value` amount of tokens from the caller's account to `to`.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transfer(address to, uint256 value) external returns (bool);

    /**
     * @dev Returns the remaining number of tokens that `spender` will be
     * allowed to spend on behalf of `owner` through {transferFrom}. This is
     * zero by default.
     *
     * This value changes when {approve} or {transferFrom} are called.
     */
    function allowance(address owner, address spender) external view returns (uint256);

    /**
     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the
     * caller's tokens.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * IMPORTANT: Beware that changing an allowance with this method brings the risk
     * that someone may use both the old and the new allowance by unfortunate
     * transaction ordering. One possible solution to mitigate this race
     * condition is to first reduce the spender's allowance to 0 and set the
     * desired value afterwards:
     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729
     *
     * Emits an {Approval} event.
     */
    function approve(address spender, uint256 value) external returns (bool);

    /**
     * @dev Moves a `value` amount of tokens from `from` to `to` using the
     * allowance mechanism. `value` is then deducted from the caller's
     * allowance.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transferFrom(address from, address to, uint256 value) external returns (bool);
}

// lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol

// OpenZeppelin Contracts (last updated v5.0.0) (token/ERC20/extensions/IERC20Permit.sol)

/**
 * @dev Interface of the ERC20 Permit extension allowing approvals to be made via signatures, as defined in
 * https://eips.ethereum.org/EIPS/eip-2612[EIP-2612].
 *
 * Adds the {permit} method, which can be used to change an account's ERC20 allowance (see {IERC20-allowance}) by
 * presenting a message signed by the account. By not relying on {IERC20-approve}, the token holder account doesn't
 * need to send a transaction, and thus is not required to hold Ether at all.
 *
 * ==== Security Considerations
 *
 * There are two important considerations concerning the use of `permit`. The first is that a valid permit signature
 * expresses an allowance, and it should not be assumed to convey additional meaning. In particular, it should not be
 * considered as an intention to spend the allowance in any specific way. The second is that because permits have
 * built-in replay protection and can be submitted by anyone, they can be frontrun. A protocol that uses permits should
 * take this into consideration and allow a `permit` call to fail. Combining these two aspects, a pattern that may be
 * generally recommended is:
 *
 * ```solidity
 * function doThingWithPermit(..., uint256 value, uint256 deadline, uint8 v, bytes32 r, bytes32 s) public {
 *     try token.permit(msg.sender, address(this), value, deadline, v, r, s) {} catch {}
 *     doThing(..., value);
 * }
 *
 * function doThing(..., uint256 value) public {
 *     token.safeTransferFrom(msg.sender, address(this), value);
 *     ...
 * }
 * ```
 *
 * Observe that: 1) `msg.sender` is used as the owner, leaving no ambiguity as to the signer intent, and 2) the use of
 * `try/catch` allows the permit to fail and makes the code tolerant to frontrunning. (See also
 * {SafeERC20-safeTransferFrom}).
 *
 * Additionally, note that smart contract wallets (such as Argent or Safe) are not able to produce permit signatures, so
 * contracts should have entry points that don't rely on permit.
 */
interface IERC20Permit {
    /**
     * @dev Sets `value` as the allowance of `spender` over ``owner``'s tokens,
     * given ``owner``'s signed approval.
     *
     * IMPORTANT: The same issues {IERC20-approve} has related to transaction
     * ordering also apply here.
     *
     * Emits an {Approval} event.
     *
     * Requirements:
     *
     * - `spender` cannot be the zero address.
     * - `deadline` must be a timestamp in the future.
     * - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner`
     * over the EIP712-formatted function arguments.
     * - the signature must use ``owner``'s current nonce (see {nonces}).
     *
     * For more information on the signature format, see the
     * https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP
     * section].
     *
     * CAUTION: See Security Considerations above.
     */
    function permit(
        address owner,
        address spender,
        uint256 value,
        uint256 deadline,
        uint8 v,
        bytes32 r,
        bytes32 s
    ) external;

    /**
     * @dev Returns the current nonce for `owner`. This value must be
     * included whenever a signature is generated for {permit}.
     *
     * Every successful call to {permit} increases ``owner``'s nonce by one. This
     * prevents a signature from being used multiple times.
     */
    function nonces(address owner) external view returns (uint256);

    /**
     * @dev Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}.
     */
    // solhint-disable-next-line func-name-mixedcase
    function DOMAIN_SEPARATOR() external view returns (bytes32);
}

// lib/openzeppelin-contracts/contracts/utils/Address.sol

// OpenZeppelin Contracts (last updated v5.0.0) (utils/Address.sol)

/**
 * @dev Collection of functions related to the address type
 */
library Address {
    /**
     * @dev The ETH balance of the account is not enough to perform the operation.
     */
    error AddressInsufficientBalance(address account);

    /**
     * @dev There's no code at `target` (it is not a contract).
     */
    error AddressEmptyCode(address target);

    /**
     * @dev A call to an address target failed. The target may have reverted.
     */
    error FailedInnerCall();

    /**
     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to
     * `recipient`, forwarding all available gas and reverting on errors.
     *
     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost
     * of certain opcodes, possibly making contracts go over the 2300 gas limit
     * imposed by `transfer`, making them unable to receive funds via
     * `transfer`. {sendValue} removes this limitation.
     *
     * https://consensys.net/diligence/blog/2019/09/stop-using-soliditys-transfer-now/[Learn more].
     *
     * IMPORTANT: because control is transferred to `recipient`, care must be
     * taken to not create reentrancy vulnerabilities. Consider using
     * {ReentrancyGuard} or the
     * https://solidity.readthedocs.io/en/v0.8.20/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].
     */
    function sendValue(address payable recipient, uint256 amount) internal {
        if (address(this).balance < amount) {
            revert AddressInsufficientBalance(address(this));
        }

        (bool success, ) = recipient.call{value: amount}("");
        if (!success) {
            revert FailedInnerCall();
        }
    }

    /**
     * @dev Performs a Solidity function call using a low level `call`. A
     * plain `call` is an unsafe replacement for a function call: use this
     * function instead.
     *
     * If `target` reverts with a revert reason or custom error, it is bubbled
     * up by this function (like regular Solidity function calls). However, if
     * the call reverted with no returned reason, this function reverts with a
     * {FailedInnerCall} error.
     *
     * Returns the raw returned data. To convert to the expected return value,
     * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].
     *
     * Requirements:
     *
     * - `target` must be a contract.
     * - calling `target` with `data` must not revert.
     */
    function functionCall(address target, bytes memory data) internal returns (bytes memory) {
        return functionCallWithValue(target, data, 0);
    }

    /**
     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],
     * but also transferring `value` wei to `target`.
     *
     * Requirements:
     *
     * - the calling contract must have an ETH balance of at least `value`.
     * - the called Solidity function must be `payable`.
     */
    function functionCallWithValue(address target, bytes memory data, uint256 value) internal returns (bytes memory) {
        if (address(this).balance < value) {
            revert AddressInsufficientBalance(address(this));
        }
        (bool success, bytes memory returndata) = target.call{value: value}(data);
        return verifyCallResultFromTarget(target, success, returndata);
    }

    /**
     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],
     * but performing a static call.
     */
    function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {
        (bool success, bytes memory returndata) = target.staticcall(data);
        return verifyCallResultFromTarget(target, success, returndata);
    }

    /**
     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],
     * but performing a delegate call.
     */
    function functionDelegateCall(address target, bytes memory data) internal returns (bytes memory) {
        (bool success, bytes memory returndata) = target.delegatecall(data);
        return verifyCallResultFromTarget(target, success, returndata);
    }

    /**
     * @dev Tool to verify that a low level call to smart-contract was successful, and reverts if the target
     * was not a contract or bubbling up the revert reason (falling back to {FailedInnerCall}) in case of an
     * unsuccessful call.
     */
    function verifyCallResultFromTarget(
        address target,
        bool success,
        bytes memory returndata
    ) internal view returns (bytes memory) {
        if (!success) {
            _revert(returndata);
        } else {
            // only check if target is a contract if the call was successful and the return data is empty
            // otherwise we already know that it was a contract
            if (returndata.length == 0 && target.code.length == 0) {
                revert AddressEmptyCode(target);
            }
            return returndata;
        }
    }

    /**
     * @dev Tool to verify that a low level call was successful, and reverts if it wasn't, either by bubbling the
     * revert reason or with a default {FailedInnerCall} error.
     */
    function verifyCallResult(bool success, bytes memory returndata) internal pure returns (bytes memory) {
        if (!success) {
            _revert(returndata);
        } else {
            return returndata;
        }
    }

    /**
     * @dev Reverts with returndata if present. Otherwise reverts with {FailedInnerCall}.
     */
    function _revert(bytes memory returndata) private pure {
        // Look for revert reason and bubble it up if present
        if (returndata.length > 0) {
            // The easiest way to bubble the revert reason is using memory via assembly
            /// @solidity memory-safe-assembly
            assembly {
                let returndata_size := mload(returndata)
                revert(add(32, returndata), returndata_size)
            }
        } else {
            revert FailedInnerCall();
        }
    }
}

// lib/openzeppelin-contracts/contracts/utils/Context.sol

// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)

/**
 * @dev Provides information about the current execution context, including the
 * sender of the transaction and its data. While these are generally available
 * via msg.sender and msg.data, they should not be accessed in such a direct
 * manner, since when dealing with meta-transactions the account sending and
 * paying for execution may not be the actual sender (as far as an application
 * is concerned).
 *
 * This contract is only required for intermediate, library-like contracts.
 */
abstract contract Context {
    function _msgSender() internal view virtual returns (address) {
        return msg.sender;
    }

    function _msgData() internal view virtual returns (bytes calldata) {
        return msg.data;
    }

    function _contextSuffixLength() internal view virtual returns (uint256) {
        return 0;
    }
}

// lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol

// OpenZeppelin Contracts (last updated v5.0.0) (token/ERC20/extensions/IERC20Metadata.sol)

/**
 * @dev Interface for the optional metadata functions from the ERC20 standard.
 */
interface IERC20Metadata is IERC20 {
    /**
     * @dev Returns the name of the token.
     */
    function name() external view returns (string memory);

    /**
     * @dev Returns the symbol of the token.
     */
    function symbol() external view returns (string memory);

    /**
     * @dev Returns the decimals places of the token.
     */
    function decimals() external view returns (uint8);
}

// lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol

// OpenZeppelin Contracts (last updated v5.0.0) (token/ERC20/utils/SafeERC20.sol)

/**
 * @title SafeERC20
 * @dev Wrappers around ERC20 operations that throw on failure (when the token
 * contract returns false). Tokens that return no value (and instead revert or
 * throw on failure) are also supported, non-reverting calls are assumed to be
 * successful.
 * To use this library you can add a `using SafeERC20 for IERC20;` statement to your contract,
 * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.
 */
library SafeERC20 {
    using Address for address;

    /**
     * @dev An operation with an ERC20 token failed.
     */
    error SafeERC20FailedOperation(address token);

    /**
     * @dev Indicates a failed `decreaseAllowance` request.
     */
    error SafeERC20FailedDecreaseAllowance(address spender, uint256 currentAllowance, uint256 requestedDecrease);

    /**
     * @dev Transfer `value` amount of `token` from the calling contract to `to`. If `token` returns no value,
     * non-reverting calls are assumed to be successful.
     */
    function safeTransfer(IERC20 token, address to, uint256 value) internal {
        _callOptionalReturn(token, abi.encodeCall(token.transfer, (to, value)));
    }

    /**
     * @dev Transfer `value` amount of `token` from `from` to `to`, spending the approval given by `from` to the
     * calling contract. If `token` returns no value, non-reverting calls are assumed to be successful.
     */
    function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {
        _callOptionalReturn(token, abi.encodeCall(token.transferFrom, (from, to, value)));
    }

    /**
     * @dev Increase the calling contract's allowance toward `spender` by `value`. If `token` returns no value,
     * non-reverting calls are assumed to be successful.
     */
    function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {
        uint256 oldAllowance = token.allowance(address(this), spender);
        forceApprove(token, spender, oldAllowance + value);
    }

    /**
     * @dev Decrease the calling contract's allowance toward `spender` by `requestedDecrease`. If `token` returns no
     * value, non-reverting calls are assumed to be successful.
     */
    function safeDecreaseAllowance(IERC20 token, address spender, uint256 requestedDecrease) internal {
        unchecked {
            uint256 currentAllowance = token.allowance(address(this), spender);
            if (currentAllowance < requestedDecrease) {
                revert SafeERC20FailedDecreaseAllowance(spender, currentAllowance, requestedDecrease);
            }
            forceApprove(token, spender, currentAllowance - requestedDecrease);
        }
    }

    /**
     * @dev Set the calling contract's allowance toward `spender` to `value`. If `token` returns no value,
     * non-reverting calls are assumed to be successful. Meant to be used with tokens that require the approval
     * to be set to zero before setting it to a non-zero value, such as USDT.
     */
    function forceApprove(IERC20 token, address spender, uint256 value) internal {
        bytes memory approvalCall = abi.encodeCall(token.approve, (spender, value));

        if (!_callOptionalReturnBool(token, approvalCall)) {
            _callOptionalReturn(token, abi.encodeCall(token.approve, (spender, 0)));
            _callOptionalReturn(token, approvalCall);
        }
    }

    /**
     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement
     * on the return value: the return value is optional (but if data is returned, it must not be false).
     * @param token The token targeted by the call.
     * @param data The call data (encoded using abi.encode or one of its variants).
     */
    function _callOptionalReturn(IERC20 token, bytes memory data) private {
        // We need to perform a low level call here, to bypass Solidity's return data size checking mechanism, since
        // we're implementing it ourselves. We use {Address-functionCall} to perform this call, which verifies that
        // the target address contains contract code and also asserts for success in the low-level call.

        bytes memory returndata = address(token).functionCall(data);
        if (returndata.length != 0 && !abi.decode(returndata, (bool))) {
            revert SafeERC20FailedOperation(address(token));
        }
    }

    /**
     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement
     * on the return value: the return value is optional (but if data is returned, it must not be false).
     * @param token The token targeted by the call.
     * @param data The call data (encoded using abi.encode or one of its variants).
     *
     * This is a variant of {_callOptionalReturn} that silents catches all reverts and returns a bool instead.
     */
    function _callOptionalReturnBool(IERC20 token, bytes memory data) private returns (bool) {
        // We need to perform a low level call here, to bypass Solidity's return data size checking mechanism, since
        // we're implementing it ourselves. We cannot use {Address-functionCall} here since this should return false
        // and not revert is the subcall reverts.

        (bool success, bytes memory returndata) = address(token).call(data);
        return success && (returndata.length == 0 || abi.decode(returndata, (bool))) && address(token).code.length > 0;
    }
}

// lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol

// OpenZeppelin Contracts (last updated v5.0.0) (token/ERC20/ERC20.sol)

/**
 * @dev Implementation of the {IERC20} interface.
 *
 * This implementation is agnostic to the way tokens are created. This means
 * that a supply mechanism has to be added in a derived contract using {_mint}.
 *
 * TIP: For a detailed writeup see our guide
 * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How
 * to implement supply mechanisms].
 *
 * The default value of {decimals} is 18. To change this, you should override
 * this function so it returns a different value.
 *
 * We have followed general OpenZeppelin Contracts guidelines: functions revert
 * instead returning `false` on failure. This behavior is nonetheless
 * conventional and does not conflict with the expectations of ERC20
 * applications.
 *
 * Additionally, an {Approval} event is emitted on calls to {transferFrom}.
 * This allows applications to reconstruct the allowance for all accounts just
 * by listening to said events. Other implementations of the EIP may not emit
 * these events, as it isn't required by the specification.
 */
abstract contract ERC20 is Context, IERC20, IERC20Metadata, IERC20Errors {
    mapping(address account => uint256) private _balances;

    mapping(address account => mapping(address spender => uint256)) private _allowances;

    uint256 private _totalSupply;

    string private _name;
    string private _symbol;

    /**
     * @dev Sets the values for {name} and {symbol}.
     *
     * All two of these values are immutable: they can only be set once during
     * construction.
     */
    constructor(string memory name_, string memory symbol_) {
        _name = name_;
        _symbol = symbol_;
    }

    /**
     * @dev Returns the name of the token.
     */
    function name() public view virtual returns (string memory) {
        return _name;
    }

    /**
     * @dev Returns the symbol of the token, usually a shorter version of the
     * name.
     */
    function symbol() public view virtual returns (string memory) {
        return _symbol;
    }

    /**
     * @dev Returns the number of decimals used to get its user representation.
     * For example, if `decimals` equals `2`, a balance of `505` tokens should
     * be displayed to a user as `5.05` (`505 / 10 ** 2`).
     *
     * Tokens usually opt for a value of 18, imitating the relationship between
     * Ether and Wei. This is the default value returned by this function, unless
     * it's overridden.
     *
     * NOTE: This information is only used for _display_ purposes: it in
     * no way affects any of the arithmetic of the contract, including
     * {IERC20-balanceOf} and {IERC20-transfer}.
     */
    function decimals() public view virtual returns (uint8) {
        return 18;
    }

    /**
     * @dev See {IERC20-totalSupply}.
     */
    function totalSupply() public view virtual returns (uint256) {
        return _totalSupply;
    }

    /**
     * @dev See {IERC20-balanceOf}.
     */
    function balanceOf(address account) public view virtual returns (uint256) {
        return _balances[account];
    }

    /**
     * @dev See {IERC20-transfer}.
     *
     * Requirements:
     *
     * - `to` cannot be the zero address.
     * - the caller must have a balance of at least `value`.
     */
    function transfer(address to, uint256 value) public virtual returns (bool) {
        address owner = _msgSender();
        _transfer(owner, to, value);
        return true;
    }

    /**
     * @dev See {IERC20-allowance}.
     */
    function allowance(address owner, address spender) public view virtual returns (uint256) {
        return _allowances[owner][spender];
    }

    /**
     * @dev See {IERC20-approve}.
     *
     * NOTE: If `value` is the maximum `uint256`, the allowance is not updated on
     * `transferFrom`. This is semantically equivalent to an infinite approval.
     *
     * Requirements:
     *
     * - `spender` cannot be the zero address.
     */
    function approve(address spender, uint256 value) public virtual returns (bool) {
        address owner = _msgSender();
        _approve(owner, spender, value);
        return true;
    }

    /**
     * @dev See {IERC20-transferFrom}.
     *
     * Emits an {Approval} event indicating the updated allowance. This is not
     * required by the EIP. See the note at the beginning of {ERC20}.
     *
     * NOTE: Does not update the allowance if the current allowance
     * is the maximum `uint256`.
     *
     * Requirements:
     *
     * - `from` and `to` cannot be the zero address.
     * - `from` must have a balance of at least `value`.
     * - the caller must have allowance for ``from``'s tokens of at least
     * `value`.
     */
    function transferFrom(address from, address to, uint256 value) public virtual returns (bool) {
        address spender = _msgSender();
        _spendAllowance(from, spender, value);
        _transfer(from, to, value);
        return true;
    }

    /**
     * @dev Moves a `value` amount of tokens from `from` to `to`.
     *
     * This internal function is equivalent to {transfer}, and can be used to
     * e.g. implement automatic token fees, slashing mechanisms, etc.
     *
     * Emits a {Transfer} event.
     *
     * NOTE: This function is not virtual, {_update} should be overridden instead.
     */
    function _transfer(address from, address to, uint256 value) internal {
        if (from == address(0)) {
            revert ERC20InvalidSender(address(0));
        }
        if (to == address(0)) {
            revert ERC20InvalidReceiver(address(0));
        }
        _update(from, to, value);
    }

    /**
     * @dev Transfers a `value` amount of tokens from `from` to `to`, or alternatively mints (or burns) if `from`
     * (or `to`) is the zero address. All customizations to transfers, mints, and burns should be done by overriding
     * this function.
     *
     * Emits a {Transfer} event.
     */
    function _update(address from, address to, uint256 value) internal virtual {
        if (from == address(0)) {
            // Overflow check required: The rest of the code assumes that totalSupply never overflows
            _totalSupply += value;
        } else {
            uint256 fromBalance = _balances[from];
            if (fromBalance < value) {
                revert ERC20InsufficientBalance(from, fromBalance, value);
            }
            unchecked {
                // Overflow not possible: value <= fromBalance <= totalSupply.
                _balances[from] = fromBalance - value;
            }
        }

        if (to == address(0)) {
            unchecked {
                // Overflow not possible: value <= totalSupply or value <= fromBalance <= totalSupply.
                _totalSupply -= value;
            }
        } else {
            unchecked {
                // Overflow not possible: balance + value is at most totalSupply, which we know fits into a uint256.
                _balances[to] += value;
            }
        }

        emit Transfer(from, to, value);
    }

    /**
     * @dev Creates a `value` amount of tokens and assigns them to `account`, by transferring it from address(0).
     * Relies on the `_update` mechanism
     *
     * Emits a {Transfer} event with `from` set to the zero address.
     *
     * NOTE: This function is not virtual, {_update} should be overridden instead.
     */
    function _mint(address account, uint256 value) internal {
        if (account == address(0)) {
            revert ERC20InvalidReceiver(address(0));
        }
        _update(address(0), account, value);
    }

    /**
     * @dev Destroys a `value` amount of tokens from `account`, lowering the total supply.
     * Relies on the `_update` mechanism.
     *
     * Emits a {Transfer} event with `to` set to the zero address.
     *
     * NOTE: This function is not virtual, {_update} should be overridden instead
     */
    function _burn(address account, uint256 value) internal {
        if (account == address(0)) {
            revert ERC20InvalidSender(address(0));
        }
        _update(account, address(0), value);
    }

    /**
     * @dev Sets `value` as the allowance of `spender` over the `owner` s tokens.
     *
     * This internal function is equivalent to `approve`, and can be used to
     * e.g. set automatic allowances for certain subsystems, etc.
     *
     * Emits an {Approval} event.
     *
     * Requirements:
     *
     * - `owner` cannot be the zero address.
     * - `spender` cannot be the zero address.
     *
     * Overrides to this logic should be done to the variant with an additional `bool emitEvent` argument.
     */
    function _approve(address owner, address spender, uint256 value) internal {
        _approve(owner, spender, value, true);
    }

    /**
     * @dev Variant of {_approve} with an optional flag to enable or disable the {Approval} event.
     *
     * By default (when calling {_approve}) the flag is set to true. On the other hand, approval changes made by
     * `_spendAllowance` during the `transferFrom` operation set the flag to false. This saves gas by not emitting any
     * `Approval` event during `transferFrom` operations.
     *
     * Anyone who wishes to continue emitting `Approval` events on the`transferFrom` operation can force the flag to
     * true using the following override:
     * ```
     * function _approve(address owner, address spender, uint256 value, bool) internal virtual override {
     *     super._approve(owner, spender, value, true);
     * }
     * ```
     *
     * Requirements are the same as {_approve}.
     */
    function _approve(address owner, address spender, uint256 value, bool emitEvent) internal virtual {
        if (owner == address(0)) {
            revert ERC20InvalidApprover(address(0));
        }
        if (spender == address(0)) {
            revert ERC20InvalidSpender(address(0));
        }
        _allowances[owner][spender] = value;
        if (emitEvent) {
            emit Approval(owner, spender, value);
        }
    }

    /**
     * @dev Updates `owner` s allowance for `spender` based on spent `value`.
     *
     * Does not update the allowance value in case of infinite allowance.
     * Revert if not enough allowance is available.
     *
     * Does not emit an {Approval} event.
     */
    function _spendAllowance(address owner, address spender, uint256 value) internal virtual {
        uint256 currentAllowance = allowance(owner, spender);
        if (currentAllowance != type(uint256).max) {
            if (currentAllowance < value) {
                revert ERC20InsufficientAllowance(spender, currentAllowance, value);
            }
            unchecked {
                _approve(owner, spender, currentAllowance - value, false);
            }
        }
    }
}

// src/TokenDistribution.sol

// Native: lock-mint-burn-unlock
interface IGovernanceTokenMint {
    function mint_into(address account, uint256 amount) external returns (bool);
}

contract TokenDistribution {
    using SafeERC20 for ERC20;

    // ERC20 token contract address
    ERC20 public immutable token;

    // Index accumulation precision
    uint256 public constant RAY = 1e18;

    // Token decimals
    uint256 public immutable tokenDecimals;

    // System address
    address private constant _system = 0x1111111111111111111111111111111111111111;

    // Role for managing pool addresses
    mapping(address => bool) public poolAddressManagers;

    // Pool structure to hold the weight and claimed reward per address
    struct AddressInfo {
        uint256 weight; // Address weight for reward distribution
        uint256 hasClaimedReward; // Already claimed reward
    }

    // Pool information
    struct Pool {
        uint256 totalWeight; // Sum of weights for all addresses in the pool
        uint256 accIndexPerWeight; // Accumulated reward index per weight
        mapping(address => AddressInfo) addresses; // Address-specific info
        address[] addressList; // List of addresses for iteration
        uint256 addressCount; // Total number of addresses added to this pool
    }

    // Stage information
    struct Stage {
        uint256 tokenLimit; // Token threshold for the stage
        uint256[15] poolRatios; // Ratios for each pool in the stage
    }

    // Total tokens distributed so far
    uint256 public totalDistributedTokens;

    // Total number of reward distributions
    uint256 public totalRewardDistributions;

    // Total amount of rewards added
    uint256 public totalRewardsAdded;

    // All stages
    Stage[] public stages;

    // Pools shared across all stages
    Pool[15] public pools;

    struct AddRewardItem {
        uint8 poolIndex;
        uint256 addIndexPerWeight;
        uint256 blockTimestamp;
        uint256 blockNumber;
    }

    AddRewardItem[] public addRewardHistory;

    struct AddRewardItemForAddress {
        uint8 poolIndex;
        uint256 reward;
        uint256 blockTimestamp;
        uint256 blockNumber;
    }

    // Reward claimed event
    event RewardClaimed(address indexed user, uint8 pool, uint256 amount);

    // New reward distribution event
    event RewardDistribution(uint256 distributionNumber, uint256 amount, uint256 totalAmount);

    constructor(address tokenAddress) {
        poolAddressManagers[_system] = true;
        poolAddressManagers[tx.origin] = true;
        require(tokenAddress != address(0), "Token address cannot be zero");
        token = ERC20(tokenAddress);
        tokenDecimals = 10 ** token.decimals();

        // Pool Ratios
        // Seed Round 5%
        // Series A Round 5%
        // Mainnet GAS BVB 3.6%
        // Diamond Platform 1.8%
        // Diamond Platform Airdrop 1.2%
        // Pioneer Network GAS 1.7%
        // Binance Wallet 0.5%
        // Runes 0.4847%
        // White Paper 0.3%
        // Helsinki 0.2%
        // Pioneer Network Node 0.12%
        // Galxe 0.04%
        // Bitget 0.03%
        // Bybit 0.0213%
        // KOL 0.004%

        // Initialize the stages
        stages.push(
            Stage({
                tokenLimit: 59500000 * tokenDecimals,
                poolRatios: [
                    uint256(0),
                    uint256(2916666_667),
                    uint256(2100000_000),
                    uint256(1050000_000),
                    uint256(700000_000),
                    uint256(1225000_000),
                    uint256(875000_000),
                    uint256(323225_000),
                    uint256(175000_000),
                    uint256(175000_000),
                    uint256(210000_000),
                    uint256(70000_000),
                    uint256(52500_000),
                    uint256(37275_000),
                    uint256(7000_000)
                ]
            })
        );

        stages.push(
            Stage({
                tokenLimit: 59500000 * tokenDecimals + 72625000 * tokenDecimals,
                poolRatios: [
                    uint256(2187500_000),
                    uint256(2916666_667),
                    uint256(2100000_000),
                    uint256(1050000_000),
                    uint256(700000_000),
                    uint256(1225000_000),
                    uint256(875000_000),
                    uint256(323225_000),
                    uint256(175000_000),
                    uint256(175000_000),
                    uint256(210000_000),
                    uint256(70000_000),
                    uint256(52500_000),
                    uint256(37275_000),
                    uint256(7000_000)
                ]
            })
        );

        stages.push(
            Stage({
                tokenLimit: 59500000 * tokenDecimals + 72625000 * tokenDecimals + 185500000 * tokenDecimals,
                poolRatios: [
                    uint256(2187500_000),
                    uint256(2916666_667),
                    uint256(1050000_000),
                    uint256(525000_000),
                    uint256(350000_000),
                    uint256(437500_000),
                    uint256(0),
                    uint256(131250_000),
                    uint256(87500_000),
                    uint256(43750_000),
                    uint256(0),
                    uint256(0),
                    uint256(0),
                    uint256(0),
                    uint256(0)
                ]
            })
        );

        stages.push(
            Stage({
                tokenLimit: 59500000 * tokenDecimals + 72625000 * tokenDecimals + 185500000 * tokenDecimals
                    + 86625000 * tokenDecimals,
                poolRatios: [
                    uint256(2187500_000),
                    uint256(0),
                    uint256(1050000_000),
                    uint256(525000_000),
                    uint256(350000_000),
                    uint256(437500_000),
                    uint256(0),
                    uint256(131250_000),
                    uint256(87500_000),
                    uint256(43750_000),
                    uint256(0),
                    uint256(0),
                    uint256(0),
                    uint256(0),
                    uint256(0)
                ]
            })
        );

        stages.push(
            Stage({
                tokenLimit: 59500000 * tokenDecimals + 72625000 * tokenDecimals + 185500000 * tokenDecimals
                    + 86625000 * tokenDecimals + 15750000 * tokenDecimals,
                poolRatios: [
                    uint256(0),
                    uint256(0),
                    uint256(1050000_000),
                    uint256(525000_000),
                    uint256(350000_000),
                    uint256(437500_000),
                    uint256(0),
                    uint256(131250_000),
                    uint256(87500_000),
                    uint256(43750_000),
                    uint256(0),
                    uint256(0),
                    uint256(0),
                    uint256(0),
                    uint256(0)
                ]
            })
        );
    }

    // Add reward to the current stage
    function addRewards(uint256 amount) external {
        require(tx.origin == _system, "Not authorized");
        bool success = IGovernanceTokenMint(address(token)).mint_into(address(this), amount);
        require(success, "Minting failed");

        (uint8 stageIndex, uint256 distributedMaxAmount) = getCurrentStage();

        amount = amount < distributedMaxAmount ? amount : distributedMaxAmount;

        require(stageIndex < stages.length, "All stages completed");
        require(amount > 0, "Amount must be greater than 0");

        Stage storage stage = stages[stageIndex];

        uint256 totalRatio = getTotalRatio(stage.poolRatios);

        for (uint8 i = 0; i < 15; i++) {
            uint256 accIndex;
            if (stage.poolRatios[i] > 0) {
                uint256 poolAmount = (amount * stage.poolRatios[i]) / totalRatio;
                Pool storage pool = pools[i];
                if (i == 0 || i == 1) {
                    // Special handling for Seed and A rounds
                    accIndex = distributeToAddresses(pool, poolAmount);
                } else {
                    accIndex = (poolAmount * RAY) / pool.totalWeight;
                    pool.accIndexPerWeight += accIndex;
                }
            }
            addRewardHistory.push(
                AddRewardItem({
                    poolIndex: i,
                    addIndexPerWeight: accIndex,
                    blockTimestamp: block.timestamp,
                    blockNumber: block.number
                })
            );
        }
        totalDistributedTokens += amount;
        totalRewardsAdded += amount;
        totalRewardDistributions++;

        emit RewardDistribution(totalRewardDistributions, amount, totalRewardsAdded);
    }

    // Distribute rewards to addresses directly for Seed and A rounds
    function distributeToAddresses(Pool storage pool, uint256 amount) internal returns (uint256) {
        uint256 accIndex = (amount * RAY) / pool.totalWeight;
        pool.accIndexPerWeight += accIndex;
        for (uint256 i = 0; i < pool.addressList.length; i++) {
            address addr = pool.addressList[i];
            AddressInfo storage info = pool.addresses[addr];
            uint256 reward = (info.weight * accIndex) / RAY;
            token.safeTransfer(addr, reward);
            info.hasClaimedReward += reward;
        }
        return accIndex;
    }

    // Add or remove pool address manager role
    function setPoolAddressManager(address manager, bool isManager) external {
        require(poolAddressManagers[msg.sender], "Not authorized");
        poolAddressManagers[manager] = isManager;
    }

    // Add addresses and their weights to a specific pool
    // weight must be greater than 0
    function addPoolAddresses(uint8 poolIndex, address[] memory addresses, uint256[] memory weights) external {
        require(poolAddressManagers[msg.sender], "Not authorized");
        require(addresses.length == weights.length, "Invalid input lengths");
        Pool storage pool = pools[poolIndex];

        uint256 newAddressCount;
        for (uint256 i = 0; i < addresses.length; i++) {
            address addr = addresses[i];
            require(weights[i] > 0, "Weight must be greater than 0");
            uint256 oldWeight = pool.addresses[addr].weight;

            if (oldWeight == 0) {
                newAddressCount++;
                if (poolIndex == 0 || poolIndex == 1) {
                    pool.addressList.push(addr);
                }
                pool.totalWeight += weights[i];
                pool.addresses[addr] = AddressInfo({weight: weights[i], hasClaimedReward: 0});
            } else {
                pool.totalWeight = pool.totalWeight - oldWeight + weights[i];
                pool.addresses[addr].weight = weights[i];
            }
        }

        pool.addressCount += newAddressCount;
    }

    // Force update target address info with source address data
    function forceUpdateAddressInfo(uint8 poolIndex, address sourceAddr, address targetAddr) external {
        require(poolAddressManagers[msg.sender], "Not authorized");
        Pool storage pool = pools[poolIndex];

        // Copy source address data to target address
        pool.addresses[targetAddr].weight = pool.addresses[sourceAddr].weight;
        pool.addresses[targetAddr].hasClaimedReward = pool.addresses[sourceAddr].hasClaimedReward;
        pool.addresses[sourceAddr].weight = 0;
        pool.addresses[sourceAddr].hasClaimedReward = 0;

        // For Seed and A round pools (poolIndex 0 or 1), need to update address list
        if (poolIndex == 0 || poolIndex == 1) {
            bool found = false;
            // Check if target address already exists in list
            for (uint256 i = 0; i < pool.addressList.length; i++) {
                if (pool.addressList[i] == sourceAddr) {
                    pool.addressList[i] = targetAddr;
                    found = true;
                    break;
                }
            }
            require(found, "Target address not found in list");
        }
    }

    // Claim rewards for an address in a specific pool
    function claim(uint8 poolIndex) public {
        Pool storage pool = pools[poolIndex];
        AddressInfo storage info = pool.addresses[msg.sender];

        uint256 reward = (info.weight * pool.accIndexPerWeight) / RAY - info.hasClaimedReward;
        require(reward > 0, "No rewards to claim");

        info.hasClaimedReward += reward;
        token.safeTransfer(msg.sender, reward);

        emit RewardClaimed(msg.sender, poolIndex, reward);
    }

    // batch claim rewards for multiple pools
    function claimMultiplePools(uint8[] calldata poolIndices) external {
        for (uint256 i = 0; i < poolIndices.length; i++) {
            claim(poolIndices[i]);
        }
    }

    // Get the current stage based on total distributed tokens
    function getCurrentStage() public view returns (uint8, uint256) {
        for (uint8 i = 0; i < stages.length; i++) {
            if (totalDistributedTokens < stages[i].tokenLimit) {
                return (i, stages[i].tokenLimit - totalDistributedTokens);
            }
        }
        return (uint8(stages.length), 0);
    }

    // Utility function to sum the ratios in a stage
    function getTotalRatio(uint256[15] memory ratios) internal pure returns (uint256) {
        uint256 total;
        for (uint8 i = 0; i < 15; i++) {
            total += ratios[i];
        }
        return total;
    }

    // Get address info for a specific pool and address
    function getAddressInfo(uint8 poolIndex, address addr) external view returns (AddressInfo memory) {
        return pools[poolIndex].addresses[addr];
    }

    // Get address info for all pools for a given address
    function getAllPoolsAddressInfo(address addr) external view returns (AddressInfo[15] memory) {
        AddressInfo[15] memory allInfo;
        for (uint8 i = 0; i < 15; i++) {
            allInfo[i] = pools[i].addresses[addr];
        }
        return allInfo;
    }

    // Get total weight for a pool
    function getPoolTotalWeight(uint8 poolIndex) external view returns (uint256) {
        return pools[poolIndex].totalWeight;
    }

    // Get accumulated index per weight for a pool
    function getPoolAccIndex(uint8 poolIndex) external view returns (uint256) {
        return pools[poolIndex].accIndexPerWeight;
    }

    // Get address list for seed and round A pools
    function getPoolAddressList(uint8 poolIndex) external view returns (address[] memory) {
        require(poolIndex == 0 || poolIndex == 1, "Address list only available for pools 0 and 1");
        return pools[poolIndex].addressList;
    }

    // Get address count for a pool
    function getPoolAddressCount(uint8 poolIndex) external view returns (uint256) {
        return pools[poolIndex].addressCount;
    }

    // Get weights for multiple addresses in a pool
    function getAddressWeights(uint8 poolIndex, address[] calldata addrs) external view returns (uint256[] memory) {
        uint256[] memory weights = new uint256[](addrs.length);
        for (uint256 i = 0; i < addrs.length; i++) {
            weights[i] = pools[poolIndex].addresses[addrs[i]].weight;
        }
        return weights;
    }

    // Struct to store reward information for each pool
    struct RewardInfo {
        uint8 poolIndex;
        uint256 claimed; // Amount of rewards already claimed
        uint256 accumulated; // Total accumulated rewards
        uint256[] stageReward; // Available rewards for stage
    }

    // Get reward information for an address across all pools
    function getAddressRewardInfo(address addr) external view returns (RewardInfo[15] memory rewards) {
        uint256[] memory totalRatio = new uint256[](stages.length);
        uint256[] memory totalReward = new uint256[](stages.length);
        for (uint8 i = 0; i < stages.length; i++) {
            totalRatio[i] = getTotalRatio(stages[i].poolRatios);
            if (i == 0) {
                totalReward[i] = stages[i].tokenLimit;
            } else {
                totalReward[i] = stages[i].tokenLimit - stages[i - 1].tokenLimit;
            }
        }

        for (uint8 i = 0; i < 15; i++) {
            rewards[i].poolIndex = i;
            rewards[i].stageReward = new uint256[](stages.length);
            AddressInfo memory info = pools[i].addresses[addr];

            // Get claimed rewards
            rewards[i].claimed = info.hasClaimedReward;

            // Calculate accumulated rewards
            rewards[i].accumulated = info.weight * pools[i].accIndexPerWeight / RAY;

            for (uint8 j = 0; j < stages.length; j++) {
                rewards[i].stageReward[j] =
                    totalReward[j] * stages[j].poolRatios[i] / totalRatio[j] * info.weight / pools[i].totalWeight;
            }
        }

        return rewards;
    }

    function getAddRewardHistory(address addr, uint256 count)
        external
        view
        returns (AddRewardItemForAddress[] memory)
    {
        uint256 length = addRewardHistory.length;
        uint256 startIndex = length > count * 15 ? length - count * 15 : 0;
        AddRewardItemForAddress[] memory addRewardHistoryForAddress = new AddRewardItemForAddress[](length - startIndex);

        for (uint256 i = startIndex; i < addRewardHistory.length; i++) {
            AddRewardItem memory addRewardHis = addRewardHistory[i];
            AddRewardItemForAddress memory addRewardHisForAddress;
            addRewardHisForAddress.poolIndex = addRewardHis.poolIndex;
            addRewardHisForAddress.blockTimestamp = addRewardHis.blockTimestamp;
            addRewardHisForAddress.blockNumber = addRewardHis.blockNumber;
            if (addRewardHis.addIndexPerWeight > 0) {
                addRewardHisForAddress.reward =
                    addRewardHis.addIndexPerWeight * pools[addRewardHis.poolIndex].addresses[addr].weight / RAY;
            }
            addRewardHistoryForAddress[i] = addRewardHisForAddress;
        }

        return addRewardHistoryForAddress;
    }
}