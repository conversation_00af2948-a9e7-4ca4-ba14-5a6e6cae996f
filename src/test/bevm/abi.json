[{"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "AddressInsufficientBalance", "type": "error"}, {"inputs": [], "name": "FailedInnerCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "pool", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>laimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "distributionNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalAmount", "type": "uint256"}], "name": "RewardDistribution", "type": "event"}, {"inputs": [], "name": "RAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}, {"internalType": "address[]", "name": "addresses", "type": "address[]"}, {"internalType": "uint256[]", "name": "weights", "type": "uint256[]"}], "name": "addPoolAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "addRewardHistory", "outputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}, {"internalType": "uint256", "name": "addIndexPerWeight", "type": "uint256"}, {"internalType": "uint256", "name": "blockTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "blockNumber", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "addRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}], "name": "claim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8[]", "name": "poolIndices", "type": "uint8[]"}], "name": "claimMultiplePools", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}, {"internalType": "address", "name": "sourceAddr", "type": "address"}, {"internalType": "address", "name": "targetAddr", "type": "address"}], "name": "forceUpdateAddressInfo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "count", "type": "uint256"}], "name": "getAddRewardHistory", "outputs": [{"components": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}, {"internalType": "uint256", "name": "reward", "type": "uint256"}, {"internalType": "uint256", "name": "blockTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "blockNumber", "type": "uint256"}], "internalType": "struct TokenDistribution.AddRewardItemForAddress[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}, {"internalType": "address", "name": "addr", "type": "address"}], "name": "getAddressInfo", "outputs": [{"components": [{"internalType": "uint256", "name": "weight", "type": "uint256"}, {"internalType": "uint256", "name": "hasClaimedReward", "type": "uint256"}], "internalType": "struct TokenDistribution.AddressInfo", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "getAddressRewardInfo", "outputs": [{"components": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}, {"internalType": "uint256", "name": "claimed", "type": "uint256"}, {"internalType": "uint256", "name": "accumulated", "type": "uint256"}, {"internalType": "uint256[]", "name": "stageReward", "type": "uint256[]"}], "internalType": "struct TokenDistribution.RewardInfo[15]", "name": "rewards", "type": "tuple[15]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}, {"internalType": "address[]", "name": "addrs", "type": "address[]"}], "name": "getAddressWeights", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "getAllPoolsAddressInfo", "outputs": [{"components": [{"internalType": "uint256", "name": "weight", "type": "uint256"}, {"internalType": "uint256", "name": "hasClaimedReward", "type": "uint256"}], "internalType": "struct TokenDistribution.AddressInfo[15]", "name": "", "type": "tuple[15]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getCurrentStage", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}], "name": "getPoolAccIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}], "name": "getPoolAddressCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}], "name": "getPoolAddressList", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "poolIndex", "type": "uint8"}], "name": "getPoolTotalWeight", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "poolAddressManagers", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "pools", "outputs": [{"internalType": "uint256", "name": "totalWeight", "type": "uint256"}, {"internalType": "uint256", "name": "accIndexPerWeight", "type": "uint256"}, {"internalType": "uint256", "name": "addressCount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "manager", "type": "address"}, {"internalType": "bool", "name": "is<PERSON>anager", "type": "bool"}], "name": "setPoolAddressManager", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "stages", "outputs": [{"internalType": "uint256", "name": "tokenLimit", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenDecimals", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalDistributedTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalRewardDistributions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalRewardsAdded", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]