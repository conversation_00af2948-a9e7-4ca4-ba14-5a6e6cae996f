use alloy::sol;
use alloy::providers::{Provider, RootProvider};
use alloy::transports::http::Http;
use alloy::network::Ethereum;
use alloy::rpc::types::BlockNumberOrTag;
use alloy::primitives::{Address, U256};
use serde_json::{Value, from_str};
use std::fs;
use std::path::Path;
use std::str::FromStr;

// Define the RewardInfo struct and the getAddressRewardInfo function interface using sol! macro
sol! {
    #[derive(Debug)]
    #[sol(rpc)]
    interface BEVM {
        struct RewardInfo {
            uint8 poolIndex;
            uint256 claimed;
            uint256 accumulated;
            uint256[] stageReward;
        }
        function getAddressRewardInfo(address addr) external view returns (RewardInfo[15] memory rewards);
    }
}

mod tests {
    use crate::connector::Connector;

    use super::*;

    #[tokio::test]
    async fn test_get_bevm_rewards() {
        // Read bevm.json file
        let json_path = Path::new("src/test/bevm.json");
        let json_content = fs::read_to_string(json_path).expect("Failed to read bevm.json");
        let json_data: Value = from_str(&json_content).expect("Failed to parse JSON");
        
        // Extract all addresses from the JSON
        let mut addrs = Vec::new();
        let custom_addrs = vec![
            "0xee1E23068358Af52f00F0CaBCe9C7F3F6541f26C",
            "0xF109A1D7f1bDD87F7251637D27D8c30DA7E07e7F",
            "0x4077b8235282309C6Dc453a73b3eC2777EFC5267",
            "0xCf1978cf90B629C23A6d59649faede6Cd7E5D28f",
            "0x5d892fA20E5D212D822152d672C5aF175a26bbEC",
            "0x6CcC752CB670bD23E645c2B046EF61f8c8FfcCba"
            ].iter().map(|s| Address::from_str(s).expect("Invalid address format")).collect::<Vec<_>>();
        addrs.extend(custom_addrs);

        for (_, entry) in json_data.as_object().unwrap() {
            if let Some(addr_str) = entry.get("address").and_then(|a| a.as_str()) {
                addrs.push(Address::from_str(addr_str).expect("Invalid address format"));
            }
        }
        println!("Found {} addresses in bevm.json", addrs.len());
        
        let connector = Connector::new_str("https://rpc-mainnet-1.geb.network").await;
        let contract_address = Address::from_str("0x8066029413e6dbe21f4d1066188cc554d6a6De56").expect("Invalid contract address");
        
        let bevm = BEVM::new(contract_address, connector.provider());
        
        let mut total_rewards = U256::ZERO;

        // Query each address
        for (i, addr) in addrs.iter().enumerate() {
            println!("{}/{}: {}", i+1, addrs.len(), addr);
            
            let rewards = bevm.getAddressRewardInfo(*addr).call().await.unwrap();

            let mut total_reward = U256::ZERO;
            for reward in rewards {
                total_reward += reward.accumulated;
            }
            println!("Rewards: {:?} total: {:?}", total_reward, total_rewards);
            println!("------------------------------");
            total_rewards += total_reward;
        }
        //println!("Total rewards: {:?}", total_rewards);

    }
}