export default class ExTimer {
    static intervalList : NodeJS.Timer[] = [];

    static _interval = new Map<string, NodeJS.Timer>();
    static _timeout = new Map<string, NodeJS.Timeout>();

    static setIntervalOld(func : ()=>void, time:number){
        const interval = setInterval(func, time);
        //console.log("[ExTimer] setInterval", interval);
        this.intervalList.push(interval);
        return interval;
    }

    static setInterval(flag:string, func : ()=> void, time:number){
        //复写map，提升性能
        if(this._interval.has(flag)) clearInterval(this._interval.get(flag));
        
        this._interval.set(flag, setInterval(func, time));
    }

    static clearInterval(flag:string) {
        if(this._interval.has(flag)){
            this._interval.delete(flag);
            clearInterval(this._interval.get(flag));
        }
    }
    /* flag : ExTimerFlag */
    static setTimeOut(flag:string, func : ()=> void, time:number, overwrite = true){
        if(this._timeout.has(flag)){
            if(overwrite){
                clearTimeout(this._timeout.get(flag));
            } else {
                return;
            }
        }
        const _t = setTimeout(()=>{
            if(this._timeout.has(flag)) this._timeout.delete(flag);
            func();
        }, time);
        this._timeout.set(flag, _t);
    }

    static clearTimeOut(flag:string){
        if(this._timeout.has(flag)){
            clearTimeout(this._timeout.get(flag));
            this._timeout.delete(flag);
        }
    }

    static cleanAllnterval(){
        console.log("[ExTimer] cleanAllnterval");
        Array.from(this._interval.values()).forEach((x)=>{
            clearInterval(x);
        });
        this.intervalList = [];
    }

}

export enum ExTimerFlag {
    UPDATE_CACHE = "update_cache",
}