import { ethers } from "ethers";
import tools from "../tools";

export class ExContract{
    contract : ethers.Contract;
    walletAddr = "";
    abi : any[] = [];

    constructor(address:string, abi:any, wallet:ethers.Wallet){
        //this.abi = tools.cleanAbi(abi);
        this.contract = new ethers.Contract(address, tools.cleanAbi(abi), wallet);
        this.walletAddr = wallet.address;
        //console.log(wallet.address);
    }

    connect(wallet:ethers.Wallet){
        if(this.contract) this.contract.removeAllListeners();
        this.contract = this.contract.connect(wallet);
        //this.contract = new ethers.Contract(this.contract.address, this.abi, wallet);
    }

    ins(){
        return this.contract;
    }
}
export class ExWallet {
    wallet : ethers.Wallet;

    constructor(key:string, provider:ethers.providers.WebSocketProvider | ethers.providers.JsonRpcProvider){
        this.wallet = new ethers.Wallet(key, provider);
    }

    connect(provider:ethers.providers.WebSocketProvider | ethers.providers.JsonRpcProvider){
        this.wallet = this.wallet.connect(provider);
    }

    ins(){
        return this.wallet;
    }
}

