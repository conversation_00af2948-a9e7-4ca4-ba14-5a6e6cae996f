export default class TransPool {
    //支持不同货币的多线程操作

    transPool : Array<{token:string, hash:string}> = []; //是否有未确认等待的交易，有的话不触发新的交易


    add(hash:string, token:string){
        this.transPool.push({
            token : token,
            hash : hash
        });

        setTimeout(()=>{
            this.remove(hash);
        }, 1000 * 60);
    }

    remove(hash:string){
        this.transPool.splice(this.transPool.findIndex(v=> v.hash== hash),1);
    }

    isEnable(token:string){
        let obj =this.transPool.find(v => v.token == token);
        return obj ? false : true; //有交易队列中就禁止发起新的交易
    }

    clear(){
        this.transPool = [];
    }

    data(){
        return this.transPool;
    }

}