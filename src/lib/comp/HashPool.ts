export default class HashPool {
    private pool : Array<string> = [];
    private max = 100;

    constructor(max:number){
        this.max = max;
    }

    has(hash:string){
        return this.pool.includes(hash);
    }

    enable(hash:string){
        if(this.pool.includes(hash)) return false;
        this.pool.push(hash);
        if(this.pool.length > this.max) this.pool.shift();
        return true;
    }

    add(hash:string){
        this.pool.push(hash);
        if(this.pool.length > this.max) this.pool.shift();
    }

    data(){
        return this.pool;
    }

    addAndCount(hash:string){
        this.add(hash);
        let count = 0;
        this.pool.forEach((x)=>{
            if(x == hash) count += 1;
        });
        return count;
    }

}