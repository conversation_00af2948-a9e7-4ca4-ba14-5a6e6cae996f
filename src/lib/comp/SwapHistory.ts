import { Address } from "cluster";
import { maxHeaderSize } from "http";
import bot from "../../bot";
import Lazy from "../lazy/LazyController";
import { macro } from "../macro";

class SwapHistoryItem {
    dealer = "";
    action = 0; // 0:buy 1:sell
    isBigDeal = false;
}

export default class SwapHistory {
    history : {[k:string] : Array<SwapHistoryItem>} = {}; //每种币最近10条交易记录
    MAX_LENGTH = 10;

    add(token:string, dealer:string, isBuy:boolean, isBigDeal:boolean){
        this.history[token] ??= [];
        this.history[token].push({
            dealer : dealer,
            action : isBuy ? 0 : 1,
            isBigDeal : isBigDeal
        });
        if(this.history[token].length > this.MAX_LENGTH) this.history[token].shift();
    }

    isAttacker(token:string, dealer:string, isBuy = true){
        const historys = this.history[token] || [];
        //最近20条记录里超过5条是同一个人的交易，并且是小单
        let smallDeal = 0;
        let bigDeal = 0;
        for(let i = historys.length-1; i > 0; i--){
            const h = historys[i];
            if(h.dealer !== dealer) continue;
            if(isBuy && h.action == 0){
                if(h.isBigDeal){
                    bigDeal++;
                } else {
                    smallDeal++;
                }
            }

            if(!isBuy && h.action == 1){
                if(h.isBigDeal){
                    bigDeal++;
                } else {
                    smallDeal++;
                }
            }
        }
        if(smallDeal > 5){
            Lazy.ins().log1(`${macro.COLOR.Red}attacker: ${dealer} ${macro.COLOR.Off}`);
            bot.localLog.append(macro.FILE.LOG_ATTACKER, dealer, [token]);
            return true;
        } else {
            return false;
        }

    }

}