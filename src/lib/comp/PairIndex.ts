import { PairExtend } from "../type/Pair";
import { macro } from "../macro";

export default class PairIndex {
    data : Map<string, PairExtend[]> = new Map(); //token存在的所有pair

    addPair(pEx:PairExtend, sort = false){
        this.data.has(pEx.token0) || this.data.set(pEx.token0, []);
        (this.data.get(pEx.token0) as PairExtend[]).push(pEx);
        this.data.has(pEx.token1) || this.data.set(pEx.token1, []);
        (this.data.get(pEx.token1) as PairExtend[]).push(pEx);
        if(sort){
            this.sort(pEx.token0);
            this.sort(pEx.token1);
        }
    }

    sortAll(){
        for(const [tokenCur, pairs] of this.data){
            pairs.sort((a,b)=> {
                return (b.token0 == tokenCur ? b.reserve0 : b.reserve1) - (a.token0 == tokenCur ? a.reserve0 : a.reserve1)
            });
        }
    }

    sort(tokenCur:string){
        (this.data.get(tokenCur) as PairExtend[]).sort((a,b)=> {
            return (b.token0 == tokenCur ? b.reserve0 : b.reserve1) - (a.token0 == tokenCur ? a.reserve0 : a.reserve1)
        });
    }

    //找出有这个token的所有pair， 不填isEoaRouter表示获取所有
    getTopPairsOfToken(token:string, exclude : string[] = [], isEoaRouter?:boolean){
        if(!this.data.has(token)) return [];
        const result : PairExtend[] = [];

        for(const p of this.data.get(token) as PairExtend[]){
            if(p.reserve0 == 0 || p.reserve1 == 0) continue;
            const isAll = isEoaRouter == undefined;
            if(isAll){
                //忽略bad token
                if(!exclude.includes(p.address) && p.status == macro.PAIR_STATUS.ACTIVE) result.push(p);
            } else if(isEoaRouter){
                if(!exclude.includes(p.address) && p.status == macro.PAIR_STATUS.ACTIVE && p.version == macro.PAIR_VERSION.V2_EOA) result.push(p);
            } else {
                if(!exclude.includes(p.address) && p.status == macro.PAIR_STATUS.ACTIVE && p.version !== macro.PAIR_VERSION.V2_EOA) result.push(p);
            }
            if(result.length == 12) break;
        }
        return result;
    }
}