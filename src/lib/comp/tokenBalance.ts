//记录持仓具体数量
import { ethers, BigNumber, utils } from "ethers";
import { utimes } from "fs";
import bot from "../../bot";


export class TokenBalanceItem {
    address = ""; //token addr
    usdt : {num:number, bn:BigNumber};  //购买token的数量
    token : {num:number, bn:BigNumber}; //持有的token数量
    preUsdt = 0; //上次购买的价格，用于大宗出售的回购
    dealer = "";
    dealers = [];
    dealerSuccess = true;

    constructor(data : {
        usdt? : {num:number, bn:BigNumber},
        token? : {num:number, bn:BigNumber},
        preUsdt?:number,
        dealer?:string,
        dealerSuccess?:boolean})
    {
        this.usdt = data.usdt || {num:0, bn:BigNumber.from('0')};
        this.token = data.token || {num:0, bn:BigNumber.from('0')};

        this.preUsdt = data.preUsdt || 0;
        this.dealer = data.dealer || "";
        this.dealerSuccess = data.dealer ? true : false;
    }
}


export default class TokenBalance {
    //dealer,whale的地址，如果bigSell是bigBuy的同一个人，bot认为是套利者，不操作
    tokenBanlace : {[token:string] : TokenBalanceItem} = {}
    decimals = 18;

    constructor(initData? : {[token:string] :TokenBalanceItem}){
        if(initData){
            this.tokenBanlace = Object.assign({}, initData);
        }
    }

    //token是否在持仓里
    public has(addr:string){
        return this.tokenBanlace[addr] ? true : false;
    }

    private add(addr:string, r : {usdt:{num:number,bn:BigNumber},token:{num:number,bn:BigNumber}}){
        if(this.has(addr)){ //是否有记录 判断条件不能使用info()
            const balance = this.info(addr);
            balance.usdt.num += r.usdt.num;
            balance.usdt.bn = balance.usdt.bn.add(r.usdt.bn);

            balance.token.num += r.token.num;
            balance.token.bn = balance.token.bn.add(r.token.bn);
        } else {
            this.tokenBanlace[addr] = new TokenBalanceItem({
                usdt : r.usdt,
                token : r.token
            });
        }
    }

    private reduce(token:string, r:{usdt:{num:number,bn:BigNumber},token:{num:number,bn:BigNumber}}){
        if(this.has(token)){ //是否有记录
            const balance = this.info(token);
            balance.usdt.num -= r.usdt.num;
            balance.token.num -= r.token.num;
            if(balance.usdt.num <= 0 || balance.token.num <=0){
                this.remove(token);
            }
            balance.usdt.bn = balance.usdt.bn.sub(r.usdt.bn);
            balance.token.bn = balance.token.bn.sub(r.token.bn);
            /*if(balance.preUsdt > 0) {
                balance.preUsdt -= r.usdt.num;
                if(balance.preUsdt < 0) balance.preUsdt = 0;
            }*/
            return true;
        } else {
            return false;
        }
    }

    public remove (token:string){
        delete this.tokenBanlace[token];
    }

    public info(token:string){
        if(this.has(token)){
            return this.tokenBanlace[token];
        } else {
            //console.warn(" balance error: ", token);
            //console.warn(this.data());
            return new TokenBalanceItem({
                usdt:{num:0, bn:BigNumber.from('0')},
                token:{num:0, bn:BigNumber.from('0')}});
        }
    }

    public data(){
        return this.tokenBanlace;
    }

    setData(token:string, data : TokenBalanceItem){
        let t = this.tokenBanlace[token];
        if(t){
            t.token = data.token;
            t.usdt = data.usdt;
        } else {
            this.tokenBanlace[token] = data;
        }
    }

    //根据交易记录自动添加持仓数据
    public addReceipt(receipt:ethers.providers.TransactionReceipt, tokenAddress:string, usdtAddress:string, withGas=false, slience=false){

        this.decimals = bot.token(tokenAddress).decimals;
        if(receipt && Object.keys(receipt).length > 0){
            let r = bot.receiptParser(receipt, tokenAddress, usdtAddress, withGas);
            //console.log(r);
            this.add(tokenAddress, r);
            return r;
        } else {
            console.warn(" receipt format error");
        }
    }

    public reduceReceipt(receipt:ethers.providers.TransactionReceipt, tokenAddress:string, usdtAddress:string, slience=false){
        const r = bot.receiptParser(receipt, tokenAddress, usdtAddress, false);
        //console.log(r);
        this.reduce(tokenAddress, r);
        return r;
    }

}