import { sign, sign_message, sign_tx, TransactionJs }  from "../web3_wasm/web3_wasm";
import { serialize, UnsignedTransaction } from "@ethersproject/transactions";
import { BytesLike, hexlify, arrayify, SignatureLike, splitSignature } from "@ethersproject/bytes";
import bot from "../../bot";
import { BigNumber } from "ethers";

export default class WasmSign {

    public static sign(tx:UnsignedTransaction, key:string){
        //console.log(tx);
        let msg = serialize(tx);
        //console.log("wasmMsg ---> ", msg);
        const u8 = arrayify(msg);
        if(key[0] == "0" && key[1] == "x") key = key.slice(2, key.length);
        //console.log(key.length);
        /*
        console.log("---------------------------");
        console.log(tx);
        console.log("---------------------------");
        console.log(msg.slice(2, msg.length));
        console.log("---------------------------");
        console.log(u8);
        console.log("---------------------------");
        console.log(key.slice(2, key.length-2));
        */
        let signature : Uint8Array;
        if(tx.type == 2){
            signature = sign(u8, key, BigInt(tx.chainId||0));
        } else {
            signature = sign_message(u8, key);
        }
        //console.log("signature: ", signature);
        //console.log("wasmSign ----> ", splitSignature(signature));

        /*
        let signature2 = getSignatureDecode(u8, key, BigInt(tx.chainId||0));
        //console.log(signature2.v);
        let signature_rejoin = new Uint8Array(65);
        let v_byts = [Number(signature2.v)];
        signature_rejoin.set(signature2.r);
        signature_rejoin.set(signature2.s, signature2.r.length);
        signature_rejoin.set(v_byts, signature2.r.length + signature2.s.length);
        console.log("signature_rejoin: ", signature_rejoin);
        console.log(splitSignature(signature_rejoin));
        */
        return serialize(tx, signature);

    }

    static zero = BigNumber.from('0');

    public static sign_full(tx:UnsignedTransaction, key:string){
        const data = tx.data?.toString() || "";

        if(key[0] == "0" && key[1] == "x") key = key.slice(2, key.length);

        let s = sign_tx(
            key,
            (tx.nonce || 0).toString(),
            (tx.to || ""),
            (tx.value || this.zero).toString(),
            (tx.chainId || 0).toString(),
            (tx.type || 0).toString(),
            data.slice(2, data.length),
            (tx.gasLimit || this.zero).toString(),
            (tx.gasPrice || this.zero).toString(),
            (tx.maxFeePerGas || this.zero).toString(),
            (tx.maxPriorityFeePerGas || this.zero).toString()
        );

        //console.log(s);
        return '0x' + s;
    }
}