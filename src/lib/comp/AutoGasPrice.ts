import { BigNumber, ethers } from "ethers";
import { macro } from "../macro";

export default class AutoGasPrice {
    _host = "";
    provider : ethers.providers.JsonRpcProvider;

    public gasPirce : BigNumber = macro.bn.zero;

    constructor(host:string, delay_second = 120){
        this._host = host;
        this.provider = this.provider = new ethers.providers.JsonRpcProvider(host);

        setTimeout(()=>{ this.updateGasPrice()}, 1000 * 5);
        setInterval(()=>{
            this.updateGasPrice();
        }, 1000 * delay_second)
    }

    private async updateGasPrice(){
        try {
            this.gasPirce = await this.provider.getGasPrice();
        } catch(e){
            console.log(e);
            console.log( `[getGasPrice] error: ${this._host}`);
        }
    }
}