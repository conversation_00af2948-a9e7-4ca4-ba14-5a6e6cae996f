import { PairExtend } from "../type/Pair";
import PairIndex from "./PairIndex";
import TokenManager from "./TokenManager";

export default class PairManager {
    tokens = new TokenManager();

    data : Map<string, PairExtend> = new Map(); // //活跃的pair，即时更新
    matrix : {[token0:string]: {[token1:string]: {[router:string]: string}}} = {}; //路由矩阵，指向对应的lp地址
    index : PairIndex = new PairIndex(); ////token存在的所有pair

    get(addr:string){
        return this.data.get(addr) as PairExtend;
    }

    /* 不覆盖写入, 需要更新完内容再执行setSafe */
    setSafe(pair: PairExtend, udpateIndex = false){
        this.index.addPair(pair, udpateIndex);
        let updateMatrix = false;
        if(!this.has(pair.address)){
            this.data.set(pair.address, pair);
            updateMatrix = true;
        } else {
            //已经存在，判断是否需要更新router
            const old = this.get(pair.address);
            pair.routers.forEach(_r=>{
                if(!old.routers.includes(_r)){
                    old.routers.push(_r);
                    updateMatrix = true;
                }
            });
        }
        if(!updateMatrix) return;
        //update matrix
        const {token0, token1, routers, address, d0, d1, token0Symbol, token1Symbol} = pair;
        routers.forEach((r)=>{
            this.matrix[token0] ??= {};
            this.matrix[token0][token1] ??= {};
            this.matrix[token0][token1][r] ??= address;

            this.matrix[token1] ??= {};
            this.matrix[token1][token0] ??= {};
            this.matrix[token1][token0][r] ??= address;
        });
        //update token
        //console.log(address, token0, token1, token0Symbol, token1Symbol, d0, d1);
        this.tokens.setSafe(token0, d0, token0Symbol);
        this.tokens.setSafe(token1, d1, token1Symbol);
    }

    has(addr:string){
        return this.data.has(addr);
    }
    
    values(){
        return this.data.values();
    }

    size(){
        return this.data.size;
    }


}