import tools from "../tools";
import fs from "fs";
import bot from "../../bot";
import <PERSON>zy from "../lazy/LazyController";

export default class LocalLog {

    cache : Array<{ time:string, file:string, str:string}>;
    
    constructor(){
        this.cache = [];
        //每30秒检查一次缓存，把操作写入本地文件
        setInterval(this.writeCacheAppend.bind(this), 1000 * 30);
    }

    private writeCacheAppend(){
        if(this.cache.length == 0) return;
        this.cache.forEach((d)=>{
            fs.appendFile(d.file, `${d.time} | ${d.str}\n`, (err)=>{ if (err) throw err });
        });
        this.cache = [];
    }

    append(file:string, hash:string, str:string[], block=0, data=""){
        const _str = str.join(" | ");
        this.cache.push({
            time : tools.now(false).str,
            file : bot.getLogPath(file),
            str : hash + " | " + _str
        });

        Lazy.ins().logRemote(file, hash, _str);
    }


    //避免读取太大的文件, data[0] 时间, data[1] data[2]
    load(file:string){
        let data : string[][] = [];
        try {
            let raw = fs.readFileSync(bot.getLogPath(file), "utf-8");
            raw.split("\n").forEach(row => {
                //console.log(`line: ${row}`);
                data.push(row.split(" | "));
            });
        } catch(e){}
        return data;
    }

    save(file:string, data:string){
        fs.writeFileSync(bot.getLogPath(file), data, "utf-8");
    }

}