import bot from "../../bot";
import { macro } from "../macro";
import { Token, TokenExtend } from "../type/Token";


export default class TokenManager {
    data : Map<string, TokenExtend> = new Map(); //所有token资料，单独使用要避免出现未知token
    empty = new TokenExtend();

    setSafe(address:string, decimals:number, symbol:string){
        if(this.data.has(address)) return;
        const t = new TokenExtend();
        t.address = address;
        t.decimals = decimals;
        t.symbol = symbol;

        //同步config的token
        const cfg = bot.config.tokens[address];
        if(cfg){
            if(cfg.max) t.max = cfg.max;
            if(cfg.maxLp) t.maxLp = cfg.maxLp;
            if(cfg.limit) t.limit = cfg.limit;
            if(cfg.keep) t.keep = cfg.keep;
            if(cfg.whiteList) t.whiteList = cfg.whiteList;
            if(cfg.price) t.price = cfg.price;
            if(this.isCex(t.symbol) || cfg.cex) t.cex = true;
            if(cfg.stable) t.mainStableToken = cfg.stable.toLowerCase();
        }
        this.data.set(address, t);
    }

    private setToken(token: Token){
        this.data.set(token.address, Object.assign(new TokenExtend(), token));
    }

    get(addr:string){
        if(this.data.has(addr)) return this.data.get(addr) as TokenExtend;
        this.empty.address = addr;
        //console.log("not log token: ", addr);
        return this.empty;
    }

    private isCex(tokenName:string){
        for(let reg in macro.cexTokens){
            if(tokenName.toLowerCase().includes(reg)){
                return true;
            }
        }
        return false;
    }

    //自动更新token
    async getAndUpdate(address:string){
        if(this.data.has(address)) return this.data.get(address) as TokenExtend;
        let contract = bot.newContract(address, macro.abi.pair, bot.operators[0].ins()).ins();
        let token = new Token();
        try {
            token.decimals = await contract.decimals();
            token.symbol   = await contract.symbol();
            token.address = address;
            this.setToken(token);
            //console.log(token);
        } catch(e){
            console.log(e);
            console.warn('############# token error: ' + address);
        }
        return token;
    }

    isActive(addr:string){
        return this.get(addr).active;
    }
    //用于 backup
    /*
    getObj(){
        const lite : {[addr:string]:Token} = {};
        for(const tEx of this.data.values()){
            const t = new Token();
            for(const[k,v] of Object.entries(t)){
                // @ts-ignore
                t[k] = tEx[k];
            }
            lite[tEx.address] = t;
        }
        //return Object.fromEntries(this.data);
        return lite;
    }

    setFromCache(obj : {[addr:string] : Token}){
        for(const [k,v] of Object.entries(obj)){
            this.setToken(v);
        }
    }
    */
}