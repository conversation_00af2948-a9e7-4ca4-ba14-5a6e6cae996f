import { ethers } from "ethers";

// 黑名单记录结构
interface BlackListRecord {
    address: string;
    timestamp: number; // 加入黑名单的时间戳
    count: number;     // 被加入黑名单的次数
}

export default class BlackList {
    // 使用Set代替数组提高查询性能
    private tempBlackList = new Set<string>();
    // 使用Map存储黑名单详细信息
    private blackListDetails = new Map<string, BlackListRecord>();
    // 永久黑名单
    private permanentBlackList = new Set<string>();
    // 交易检查记录
    private checkList = new Map<string, { hash: string, blockNum: number }>();
    // 操作失败记录
    private failList = new Set<string>();
    
    // 黑名单清理时间（3天 = 259200000毫秒）
    private readonly CLEANUP_TIME_MS = 3 * 24 * 60 * 60 * 1000;
    
    constructor() {
        // 定期清理临时黑名单
        setInterval(this._cleanupBlackList.bind(this), 60 * 60 * 1000 * 6); // 每6小时执行一次清理
    }

    /**
     * 检查地址是否在黑名单中 - 高性能实现
     */
    has(address: string): boolean {
        // 转换为小写以确保一致性
        address = address.toLowerCase();
        // 先检查永久黑名单，再检查临时黑名单
        return this.permanentBlackList.has(address) || this.tempBlackList.has(address);
    }

    /**
     * 添加地址到黑名单
     */
    add(address: string): void {
        // 转换为小写以确保一致性
        address = address.toLowerCase();
        
        // 如果已经在永久黑名单中，不需要处理
        if (this.permanentBlackList.has(address)) {
            return;
        }

        // 获取现有记录或创建新记录
        const now = Date.now();
        const existingRecord = this.blackListDetails.get(address);
        
        if (existingRecord) {
            // 更新现有记录
            existingRecord.timestamp = now;
            existingRecord.count += 1;
        } else {
            // 创建新记录
            this.blackListDetails.set(address, {
                address,
                timestamp: now,
                count: 1
            });
            this.tempBlackList.add(address);
        }
    }

    /**
     * 移除地址从黑名单
     */
    remove(address: string): void {
        // 转换为小写以确保一致性
        address = address.toLowerCase();
        
        this.tempBlackList.delete(address);
        this.blackListDetails.delete(address);
        // 不从永久黑名单中移除
    }

    /**
     * 记录交易以便后续检查
     */
    check(trans: ethers.providers.TransactionResponse): void {
        const { from, hash, nonce, blockNumber } = trans;
        if (!from) return;
        
        const key = `${from.toLowerCase()}_${nonce}`;
        this.checkList.set(key, { 
            hash: hash, 
            blockNum: blockNumber || 0 
        });
    }

    /**
     * 清理过期的黑名单记录
     */
    private _cleanupBlackList(): void {
        const now = Date.now();
        let cleanedCount = 0;
        
        // 检查并清理过期的黑名单记录
        for (const [address, record] of this.blackListDetails.entries()) {
            if (now - record.timestamp > this.CLEANUP_TIME_MS) {
                this.tempBlackList.delete(address);
                this.blackListDetails.delete(address);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            console.log(`Cleaned up ${cleanedCount} expired blacklist records`);
        }
    }

    /**
     * 检查交易状态并更新黑名单
     */
    private async _check(): Promise<void> {
        // 实现交易检查逻辑
        for (const [key, value] of this.checkList.entries()) {
            const [from, nonce] = key.split("_");
            // 实现检查交易的逻辑
        }
    }

    /**
     * 获取黑名单统计信息
     */
    getStats(): { temp: number, permanent: number, total: number } {
        return {
            temp: this.tempBlackList.size,
            permanent: this.permanentBlackList.size,
            total: this.tempBlackList.size + this.permanentBlackList.size
        };
    }

    /**
     * 导出黑名单
     */
    export(): { temp: string[], permanent: string[] } {
        return {
            temp: Array.from(this.tempBlackList),
            permanent: Array.from(this.permanentBlackList)
        };
    }

    /**
     * 导入黑名单
     */
    import(data: { temp?: string[], permanent?: string[] }): void {
        if (data.temp) {
            const now = Date.now();
            data.temp.forEach(addr => {
                const address = addr.toLowerCase();
                this.tempBlackList.add(address);
                if (!this.blackListDetails.has(address)) {
                    this.blackListDetails.set(address, {
                        address,
                        timestamp: now,
                        count: 1
                    });
                }
            });
        }
        
        if (data.permanent) {
            data.permanent.forEach(addr => {
                this.permanentBlackList.add(addr.toLowerCase());
            });
        }
    }
}