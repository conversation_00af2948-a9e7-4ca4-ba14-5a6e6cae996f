import { Router } from "express";
import bot from "../../bot";
import { MevPath } from "./DataType";
import { PairExtend } from "./Pair";


export class ConsumeController {
    _cache : {[addr:string]:PairExtend} = {}; //缓存有变动的pair，使用完后使用resume还原

    //返回clone的对象
    add(addr:string){
        if(this._cache[addr]) return this._cache[addr];
        const p = bot.oracleV2.data.pm.get(addr).clone();
        //备份
        if(!this._cache[p.address]){
            this._cache[p.address] = p;
        }
        return p;
    }

    batchAdd(addrs:string[]){
        const arr : PairExtend[] = [];
        addrs.forEach(addr=>{
            arr.push(this.add(addr));
        });
        return arr;
    }
    //for testing
    displayDevbugCache(){
        console.log("for testing!!");
        for(const [k,v] of Object.entries(this._cache)){
            console.log(`${v.address} ${v.token0Symbol}-${v.token1Symbol}`);
        }
    }

    resume(){
        const updatePairIndex : string[] = [];
        for(const [k,v] of Object.entries(this._cache)){
            const p = bot.oracleV2.data.pm.get(k);
            p.reserve0 = v.reserve0;
            p.reserve1 = v.reserve1;
            if(!updatePairIndex.includes(p.token0)) updatePairIndex.push(p.token0);
            if(!updatePairIndex.includes(p.token1)) updatePairIndex.push(p.token1);
        }
        updatePairIndex.forEach(u=> bot.oracleV2.data.pm.index.sort(u));
    }

    //找出pairs里价值最低的pair，然后所有pair一起消费
    //exclude通常是主pair
    consume(m:MevPath, excludePair:string){
        //找出最低价值的pair
        let v = bot.oracleV2.data.calcLowestValue(m.s0, m.pairs, excludePair);
        //消费更新到pm
        const updatePairIndex : string[] = [];
        for(let i = 0 ; i < m.pairs.length; i++){
            if(m.pairs[i] == excludePair) continue;
            //console.log(`consume: ${m.pairs[i]} exclude:${excludePair}`, v.lowest, v.values[i]);
            const p = bot.oracleV2.data.pm.get(m.pairs[i]);
            this._cache[p.address] ??= p.clone();
            p.consumePercent(v.lowest / v.values[i]);
            if(!updatePairIndex.includes(p.token0)) updatePairIndex.push(p.token0);
            if(!updatePairIndex.includes(p.token1)) updatePairIndex.push(p.token1);
        }
        updatePairIndex.forEach(u=> bot.oracleV2.data.pm.index.sort(u));
        //console.log(`consume: pairValues [${v.values.join(", ")}] lowest:${v.lowest}`);
    }
}