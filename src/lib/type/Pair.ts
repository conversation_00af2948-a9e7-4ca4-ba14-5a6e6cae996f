import { BigNumber, utils } from "ethers";
import bot from "../../bot";
import { MevPath } from "./DataType";
import { macro } from "../macro";

export class Pair {
    id = 0;
    address = "";
    factory = "";
    version = macro.PAIR_VERSION.V2 ; //1: uniswapV1 2:uniswapV2, 3:uniswapV3,  21.jswapV2

    token0Symbol = "";
    token1Symbol = "";

    token0 = "";
    token1 = "";

    reserve0 = 0;
    reserve1 = 0;

    f0 = 0; //token0的fee
    f1 = 0; //token1的fee1
    fp = 0; //pair的fee，对于v2等于router的fee，对于v2变种每个pair会有不同的fee, 100000=100%,不扣税

    d0 = 0; //token0的精度
    d1 = 0; //token1的精度

    blockTimestampLast = 0;

    status = macro.PAIR_STATUS.UNCHECKED;
    stable = false;
}

export class PairExtend extends Pair {
    //fee0和fee1根据pair的f0 f1和router fee相加的结果
    fee0 = 0.997; //本地计算, 避免多一次转换 //100000 = 100%
    fee1 = 0.997; //本地计算 //100000 = 100%
    //fee0Server = BigNumber.from(99700); //服务器计算, 100000 = 100%
    //fee1Server = BigNumber.from(99700); //服务器计算, 100000 = 100%

    routers : string[] = [];

    //mev路径
    mevPath : MevPath[] = [];

    onSync(reserve0 : BigNumber, reserve1 : BigNumber, updateLogic = false){
        //console.log(`${this.address} sync: r0(${utils.formatEther(reserve0)}) r1(${utils.formatEther(reserve1)}) updateLogic:${updateLogic} status:${this.status}`);
        
        const bToken0 = bot.token(this.token0);
        const bToken1 = bot.token(this.token1);
        const r0 = Number(utils.formatUnits(reserve0, bToken0.decimals));
        const r1 = Number(utils.formatUnits(reserve1, bToken1.decimals));

        this.reserve0 = r0;
        this.reserve1 = r1;
        this.blockTimestampLast = ~~(Number(new Date())/1000);

        if(updateLogic && bot.config.trash.active && this.status == macro.PAIR_STATUS.ACTIVE){
            bot.client.trash.onChange(this.address, r0, r1);
            bToken0.updateWeight(this.address);
            bToken1.updateWeight(this.address);
        }
    }

    isBigDeal(amountIn:number){
        const [rStable, rToken] = bot.config.stableTokens.includes(this.token0) ? [this.reserve0, this.reserve1] : [this.reserve1, this.reserve0];
        let multi = 0;
        let title = "BIG";
        const priceImpact = amountIn / rToken;

        if(priceImpact > 0.034){
            multi = 4.4;
            title = "CRAZY";
        } else if(priceImpact >  0.027){
            multi = 3.6; //3.3
            title = "GREAT";
        } else if(priceImpact > 0.018){
            multi = 2.9;
            title = "SUPER";
        } else if(priceImpact > 0.012){
            multi = 2.2;
            title = "HUGE2";
        } else if(priceImpact > 0.0088){
            multi = 1.7;
            title = "HUGE";
        } else if(priceImpact > 0.0068){
            multi = 1.3;
            title = "LARGE";
        } else if(priceImpact > 0.0038){
            multi = 1;
            title = "BIG";
        } else {
            multi = 0;
        }
        if(multi >= 1) console.log(` !!! ${title} DEAL !!!! usdt: ${rStable.toFixed()}, token: ${amountIn.toFixed()}/${rToken.toFixed()}`);
        return { multi: multi, priceImpact: priceImpact};
    }

    bestDeal(slippage = 0, multi = 1){
        const [rStable, rToken] = bot.config.stableTokens.includes(this.token0) ? [this.reserve0, this.reserve1] : [this.reserve1, this.reserve0];
        let bonus = 1;
        if(slippage > 0.190){ //19%
        //    bonus = 7;
        //} else if(slippage > 0.100){
        //    bonus = 5;
        //} else if(slippage > 0.079){
        //    bonus = 4.2;
        //} else if(slippage > 0.059){
            bonus = 3.5;
        } else if(slippage > 0.039){
            bonus = 2.0;
        } else if(slippage > 0.025){
            bonus = 1.5;
        } else if(slippage > 0.019){
            bonus = 1.3;
        } else if(slippage > 0.009){
            bonus = 1.1;
        }
        if(multi < 1.3 && bonus > 2) bonus = 1.8;

        if(bonus > 1) console.log(" slippage bonus: ", bonus);

        return {
            usdt: rStable * 0.0036 * bonus,
            token : rToken * 0.0036 * bonus
        }
    }

    getReserves(tokenA:string){
        return this.token0 == tokenA ? [this.reserve0, this.reserve1, this.fee0] : [this.reserve1, this.reserve0, this.fee1];
    }

    async getReservesServer(update = true){
        //const abi = ["function getReserves() public view returns (uint112 _reserve0, uint112 _reserve1, uint32 _blockTimestampLast)"];
        //const ifaec = new utils.Interface(abi);
        //let result = "";
        //console.log(ifaec.encodeFunctionData("getReserves", [])); //0x0902f1ac
        //result = await bot.provider.call({to:this.address, data:ifaec.encodeFunctionData("getReserves", [])});
        //const r = ifaec.decodeFunctionResult("getReserves", result);

        try {
            const r = await bot.contractIns.getPairReserves(this.address);
            if(update){
                this.reserve0 = bot.token(this.token0).toNum(r.r0);
                this.reserve1 = bot.token(this.token1).toNum(r.r1);
                this.blockTimestampLast = r.blockTimestampLast;
            }
            return [r.r0 as BigNumber, r.r1 as BigNumber];

        } catch(e){
            console.log("[getReservesServer] error: ", e);
            setTimeout(() => { this.getReservesServer() }, 1000 * 20 * Math.random());
            return [macro.bn.zero, macro.bn.zero];
        }
    }

    async totalSupply(){
        let resp = await bot.provider().call({to:this.address, data:"0x18160ddd"});
        let decode = bot.abiCoder.decode(["uint256"], resp);
        return decode[0] as BigNumber;
    }

    updateFee(){
        const customFee = bot.config.whiteListPair[this.address];
        if(customFee){
            //console.log("update customFee", router.fee);
            this.fee0 = (this.fp - customFee.fee0) / 100000;
            this.fee1 = (this.fp - customFee.fee1) / 100000;
        } else {
            //onlyRouter需要给双倍的fee，多了一次转换
            this.fee0 = (this.version == macro.PAIR_VERSION.V2_ONLY_ROUTER ? this.fp - this.f0 - this.f0 : this.fp - this.f0) / 100000;
            this.fee1 = (this.version == macro.PAIR_VERSION.V2_ONLY_ROUTER ? this.fp - this.f1 - this.f1 : this.fp - this.f1) / 100000;
        }
        //this.fee0 = this.fee0Server.toNumber() / 100000;
        //this.fee1 = this.fee1Server.toNumber() / 100000;
        //console.log("update router fee", router.fee);
    }

    updateBalance(tokenIn:string, amountIn:number, amountOut:number){
        if(this.token0 == tokenIn){
            this.reserve0 += amountIn;
            this.reserve1 -= amountOut;
        } else {
            this.reserve1 += amountIn;
            this.reserve0 -= amountOut;
        }
    }

    price(){
        return bot.config.stableTokens.includes(this.token0) ? (this.reserve0/this.reserve1) : (this.reserve1/this.reserve0);
    }

    //deep clone
    clone(){
        let pair = new PairExtend();
        Object.assign(pair, this);
        return pair;
    }

    cloneIntoPair(){
        let pair = new Pair();
        for(const [k,v] of Object.entries(pair)){
            // @ts-ignore
            pair[k] = this[k];
        }
        return pair;
    }
    
    consumeAll(){
        this.reserve0 = 0;
        this.reserve1 = 0;
        return this;
    }

    consume(token0amount = 0, token1amount = 0){
        if(token0amount > 0){
            let amount = Math.min(token0amount, this.reserve0);
            this.reserve0 = (this.reserve0 - amount / this.reserve0) * this.reserve0;
            this.reserve1 = (this.reserve0 - amount / this.reserve0) * this.reserve1;
        } else if(token1amount > 0){
            let amount = Math.min(token1amount, this.reserve1);
            this.reserve0 = (this.reserve1 - amount / this.reserve1) * this.reserve0;
            this.reserve1 = (this.reserve1 - amount / this.reserve1) * this.reserve1;
        }
        return this;
    }
    // 1 = 100%消耗完
    consumePercent(consumeP = 1){
        const p = 1 - consumeP;
        //console.log(`${this.token0Symbol}-${this.token1Symbol} before r: [${this.reserve0},${this.reserve1}]`);
        this.reserve0 = this.reserve0 * p;
        this.reserve1 = this.reserve1 * p;
        //console.log(`${this.token0Symbol}-${this.token1Symbol} after  r: [${this.reserve0},${this.reserve1}]`);
        return this;
    }
}