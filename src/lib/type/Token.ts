import { BigNumber, utils } from "ethers";
import { macro } from "../macro";
import bot from "../../bot";
import Lazy from "../lazy/LazyController";

export class TokenRouter {
    router = "";
    routers = [];

    addr = "";

    path :string[] = [];

    tokenWeight = 0;
    stableWeight = 0;

    pairs : string[] = [];
}


export class Token {
    symbol : string = "-";
    decimals : number = 18;
    address : string = macro.zeroAddress;
}

export class TokenExtend extends Token {
    //bull and bear
    active = false; //禁止bear和bull交易
    whiteList = false; //白名单交易数量x120%
    max = 0;  //bear最大持仓量
    maxLp = 0;
    limit = 0; //最大交易量
    
    keep = 0; //最小剩余量，少于keep不进行bull活动
    cex = false;

    price = 1; //token的usd价格
    mainStableToken = ""; //最大的稳定币lp
    paths : Array<TokenRouter> = [];
    sum = { token : 0, stable : 0 }; //所有有效lp持仓总量
    unknow = false;

    public toNum(_amount : BigNumber){
        
        return Number(utils.formatUnits(_amount, this.decimals));
    }

    public toBigNumber(_amount : number){
        return utils.parseUnits(_amount.toFixed(this.decimals), this.decimals);
    }

    //bear和bull获取最佳的交易数量
    public bestDeal(slippage = 0, multi = 1){
        const total = this.sum;
        let bonus = 1;
        if(slippage > 0.190){ //19%
        //    bonus = 7;
        //} else if(slippage > 0.100){
            bonus = 5;
        } else if(slippage > 0.089){
            bonus = 2.7;
        } else if(slippage > 0.059){
            bonus = 2.3;
        } else if(slippage > 0.039){
            bonus = 2;
        } else if(slippage > 0.025){
            bonus = 1.6;
        } else if(slippage > 0.019){
            bonus = 1.3;
        } else if(slippage > 0.009){
            bonus = 1.2;
        }
        bonus = bonus * multi;
        if(this.cex) {
            bonus = 0.7; //中心化的币不大于0.32%
            Lazy.ins().log(' ~ cex token');
        }
        //if(multi < 1.3 && bonus > 2) bonus = 1.8;
        //if(bonus > 1) console.log(" slippage bonus: ", bonus);
        return {
            stable : total.stable * 0.004 * bonus,
            token  : total.token  * 0.004 * bonus
        }
    }

    public updateWeight(pairAddr:string){
        let router = this.paths.find( x => x.addr == pairAddr);
        if(!router) return;
        let len = router.pairs.length;
        if (len == 2){
            //console.log(`[${bot.token(this.address).symbol}] updateWeight1 before: ${this.sum.stable} / ${this.sum.token}`);
            let old_w_stable = router.stableWeight;
            let old_w_token = router.tokenWeight;
            //let pre = bot.oracleV2.data.activeLps[router.pairs[0]];
            //let current = bot.oracleV2.data.activeLps[router.pairs[1]];
            let [w0, w1] = bot.oracleV2.data.getFixedWeightOfPairs(this.mainStableToken, router.pairs);
            router.tokenWeight = w0;
            router.stableWeight = w1;
            this.sum.stable = this.sum.stable + router.stableWeight - old_w_stable;
            this.sum.token  = this.sum.token  + router.tokenWeight  - old_w_token;
            //console.log(`[${bot.token(this.address).symbol}] updateWeight1 after : ${this.sum.stable} / ${this.sum.token}`);
            //console.log(`[${bot.token(this.address).symbol}] old`, old_w_stable, old_w_token);
            //console.log(`[${bot.token(this.address).symbol}] cur`, router.stableWeight, router.tokenWeight);

        } else if (len == 1) {
            //console.log(`[${bot.token(this.address).symbol}] updateWeight2 before: ${this.sum.stable} / ${this.sum.token}`);
            const old_w_stable = router.stableWeight;
            let old_w_token = router.tokenWeight;
            
            let pair = bot.oracleV2.data.pm.get(pairAddr);
            //let pair = bot.oracleV2.data.activePairs[pairAddr];
            //console.log(pair.token0, this.address);
            if(pair.token0 == this.address){
                router.tokenWeight = pair.reserve0;
                router.stableWeight = pair.reserve1;
            } else {
                router.tokenWeight = pair.reserve1 || 0;
                router.stableWeight = pair.reserve0 || 0
            }
            this.sum.stable = this.sum.stable + router.stableWeight - old_w_stable;
            this.sum.token  = this.sum.token  + router.tokenWeight  - old_w_token;
            //console.log(`[${bot.token(this.address).symbol}] updateWeight2 after : ${this.sum.stable} / ${this.sum.token}`);
            //console.log(`[${bot.token(this.address).symbol}] old`, old_w_stable, old_w_token);
            //console.log(`[${bot.token(this.address).symbol}] cur`, router.stableWeight, router.tokenWeight);
        }
    }

}