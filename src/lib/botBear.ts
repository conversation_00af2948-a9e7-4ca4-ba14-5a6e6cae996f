import bot from "../bot";
import { BigNumber, ethers } from "ethers";

import BotBase from "./botBase";
import { WhaleSwapData, BaseSwapExData, SwapResult, MixGas, SumToken, StableBalanceSum } from "./type/DataType";
import TokenBalance, { TokenBalanceItem } from './comp/tokenBalance';
import { macro } from "./macro";
import ExTimer from "./comp/ExTimer";
import tools from "./tools";
import Lazy from "./lazy/LazyController";


export default class BotBear extends BotBase {
    enable = bot.config.bot2.active; //用于调试
    st = new StableBalanceSum();

    constructor(wallets:{[k:string]:string}, addr:string){
        super(wallets, addr);
        if(bot.mode == macro.BOT_MODE.UPDATE) return;
        if(this.enable && bot.config.bot2.active){
            this.tokenBuyBack = this.loadCache(macro.FILE.BALANCE_BEAR);
            setTimeout(this.sellExtra.bind(this), 1000 * 60 * 2);
            //setInterval(this.sellExtra.bind(this), 1000 * 60 * 5);
            ExTimer.setIntervalOld(this.sellExtra.bind(this), 1000 * 60 * 20);
            ExTimer.setIntervalOld(() => { this.saveCache(macro.FILE.BALANCE_BEAR, this.tokenBuyBack) }, 1000 * 60);
            ExTimer.setIntervalOld(()=>{ this.st.enableLogSum = true; }, 1000 * 300);
        }
    }

    //大宗买入
    async onPut(
        trans:ethers.providers.TransactionResponse,
        dataDecode:SwapResult,
        multi:number,
        slippage:number,
        token : string,
        isEth : boolean,
        totalImpact : number,
        pairIn:number,
        pairOut:number){
            if(!this.enable) return;
            const {amountIn, amountOut: amountOutMin, path, to, deadline} = dataDecode;
            const gasPrice = trans.gasPrice || bot.handle.minGas();
            //const bestDeal = bot.oracleV2.getBestDeal(router, token, 0, multi);

            //超级大单买入时卖出部分持仓
            const bToken = bot.token(token);
            const bestDeal = bToken.bestDeal(0, multi);
            const holdingToken = this.tokenBalance.info(token).token.num;
            if( !this.tokenBuyBack.has(token)
                && multi > 3 && bToken.whiteList
                && bot.client.bull.tokenBalance.has(token) //bull已经准备好建仓
                && holdingToken > 0 ){ //有持仓货币

                Lazy.ins().log1(" ############################");
                Lazy.ins().log1(" ### sell after great buy ###");
                let bestSell = Math.min(holdingToken, bestDeal.token / 2 * 1.2);
                let bestSellBn = bToken.toBigNumber(bestSell);
                let sell = await this.getBestAmountOut(token, bestSellBn, false, 1+totalImpact * 0.7);
                const data = new BaseSwapExData({
                    data:sell,
                    mixGas : new MixGas(trans),
                    whale: bot.swapDataToWhale(trans.from, dataDecode, false, isEth)
                });
                this.sellToken(data);
                return;
            }
    }
    //大宗卖出
    async onPull(
        trans:ethers.providers.TransactionResponse,
        dataDecode:SwapResult,
        multi:number,
        slippage:number,
        token : string,
        isEth : boolean,
        totalImpact : number,
        pairIn:number,
        pairOut:number){
            if(!this.enable) return;

            const {amountIn, amountOut: amountOutMin, path, to, deadline} = dataDecode;
        
            const gasPrice = trans.gasPrice || bot.handle.minGas();
            const bToken = bot.token(token);

            //不超过池子的滑点的购买价格
            //let bestDeal = bot.oracleV2.getBestDeal(router, token, slippage, multi).token;
            let bestDeal = bToken.bestDeal(slippage, multi).token;
            const maxDeal = pairIn * 1.485;
            Lazy.ins().logTs1(`${macro.COLOR.Blue}bestDeal(${bestDeal})  maxDeal(${maxDeal}) ${macro.COLOR.Off}`);
            if(bestDeal > maxDeal) bestDeal = maxDeal;

            //已经有持仓，跟着whale卖出，如果有更大的whale就补仓卖出
            const buyBack = this.tokenBuyBack.info(token);
            const holding = this.tokenBalance.info(token);
            //console.log("  ## balance: ", holding.token.num, "bestDeal: ", bestDeal);

            if(this.tokenBuyBack.has(token)) {
                if( buyBack.token.num > 0.001
                    && ((buyBack.token.num * 1.5) < bestDeal)
                    && this.tokenBalance.info(token).token.num > 0.001){
                    //继续卖出
                    bestDeal = bestDeal - buyBack.token.num;
                } else {
                    Lazy.ins().logTs1(`## ... reach max selling, begin buyback`);
                    //如果whale成功交易会卖出更高价格，这里需要调高getBestAmountOut的输出
                    let buy = await this.getBestAmountOut(token, buyBack.usdt.bn, true, 1+(totalImpact*0.7)); //本地计算有bug
                    //避免重复交易
                    //const whale = new WhaleSwapData(bot.config.bot2.address, token, buy.outSlippage.bn, false, false);
                    const data = new BaseSwapExData({
                        data:buy,
                        mixGas : new MixGas(trans),
                        whale: bot.swapDataToWhale(trans.from, dataDecode, false, isEth),
                        whaleTrans: trans
                    });
                    this.buyToken(data);
                    return;
                }
            }
            
            //是否有余额
            if(holding.token.num < 0.001){
                Lazy.ins().log1("# ... not enough token");
                return;
            }
            
            if(slippage > 0.0088) this.greedMode = true;
            bestDeal = Math.min(bestDeal, holding.token.num);
            
            let sellAmount =  bot.token(token).toBigNumber(bestDeal);
            //优先卖出
            let sell = await this.getBestAmountOut(token, sellAmount, false, macro.slippage.normal, true);
            this.logTrade(macro.FILE.LOG_BEAR_WHALE, trans.hash, token, bestDeal, bToken.mainStableToken, sell.out.num, false);
            //await this.delay(); //等待其他机器人入场
            const data = new BaseSwapExData({
                data:sell,
                mixGas : (new MixGas(trans)).more(),
                frontRun : true,
                whale:bot.swapDataToWhale(trans.from, dataDecode, true, isEth)
            });
            this.sellToken(data, (receipt)=>{
                    trans.wait().then((r)=>{
                        //等鲸鱼完成交易
                        //bot.receiptBlockNum(r, `  #### [${bToken.symbol}] whale sell success!!!!`);
                        const balance = this.tokenBuyBack.info(token);
                        balance.dealer = to;
                        balance.dealerSuccess = true;
                        this.autoBuyBack([token, bToken.mainStableToken]);
                    }).catch((e)=>{
                        //console.log(`  ##### [${bToken.symbol}] oops..... whale sell fail :(`);
                        const balance = this.tokenBuyBack.info(token);
                        balance.dealer = to;
                        balance.dealerSuccess = false;
                    });
                }
            );
    }

    buyToken(dataEx : BaseSwapExData, onFinish? : (receipt:ethers.providers.TransactionReceipt)=>void){
        const tokenAddr = dataEx.data.paths[0][dataEx.data.paths[0].length-1];
        const token = bot.token(tokenAddr);
        if(!this.trans.isEnable(tokenAddr)) {
            Lazy.ins().log1(`## (${token.symbol}) buyToken: transPool length > 0`);
            return;
        }
        Lazy.ins().logTs1(`## (${token.symbol}) buy $${dataEx.data.in.num} -> ${dataEx.data.out.num}, path:${dataEx.data.pairs.length}`);
        dataEx.onFinish = async (receipt)=>{
            bot.receiptBlockNum(receipt, `${macro.COLOR.White}## (${token.symbol}) buy `);
            const tokenPre = this.tokenBuyBack.info(token.address).token.num;

            onFinish && onFinish(receipt);
            this.updateBalance([token.mainStableToken, token.address]);
            const {transactionHash, blockNumber, transactionIndex} = receipt;
            let blockElapsed = 0;
            let whaleIndex = "-";
            if(dataEx.whaleTrans){
                try {
                    let whaleRes = await dataEx.whaleTrans.wait();
                    blockElapsed = blockNumber - whaleRes.blockNumber;
                    whaleIndex = whaleRes.transactionIndex.toString();
                } catch(e) {
                    whaleIndex = "-1";
                }
            }
            const r = this.tokenBuyBack.reduceReceipt(receipt, tokenAddr, token.mainStableToken);
            let profitStr = "";
            if(tokenPre > 0){
                const profit = r.token.num - tokenPre;
                profitStr = (blockElapsed == 0 ? "" : `(${+blockElapsed})`) + `(${transactionIndex}/${whaleIndex})` + ` reward: ${profit.toFixed(4)}`;
                Lazy.ins().log1(`bear ${macro.COLOR.BYellow}${profitStr}${macro.COLOR.Off}`);
            }
            this.logTrade(macro.FILE.LOG_BEAR, receipt.transactionHash, token.address, r.token.num, r.usdtAddr, r.usdt.num, true, profitStr);

        };
        dataEx.onFail = (e)=>{
            Lazy.ins().log1(`## (${token.symbol}) buy fail`);
        }
        this.swap(dataEx);
    }

    sellToken(dataEx : BaseSwapExData, onFinish? : (receipt:ethers.providers.TransactionReceipt)=>void, noBuyBack=false){
        const tokenAddr = dataEx.data.paths[0][0];
        const token = bot.token(tokenAddr);

        if(!this.trans.isEnable(tokenAddr)) {
            Lazy.ins().log1(`# (${token.symbol}) sellToken err:  transPool > 0 `);
            return;
        }

        Lazy.ins().log1(`# (${token.symbol}) sell ${dataEx.data.in.num} -> $${dataEx.data.out.num}, path:${dataEx.data.pairs.length}`);

        dataEx.onFinish = (receipt:ethers.providers.TransactionReceipt)=>{
            bot.receiptBlockNum(receipt, `${macro.COLOR.White}# (${token.symbol}) sell `);
            if(!noBuyBack){
                const r = this.tokenBuyBack.addReceipt(receipt,tokenAddr, token.mainStableToken);
                if(r) this.logTrade(macro.FILE.LOG_BEAR, receipt.transactionHash, token.address, r.token.num, r.usdtAddr, r.usdt.num, false);
            }

            setTimeout(()=>{ this.greedMode = false}, 1000 * 20);
            onFinish && onFinish(receipt);
            this.updateBalance([token.mainStableToken, token.address]);
        };

        dataEx.onFail = (e)=>{
            console.log(e);
            Lazy.ins().log1(`## (${token.symbol}) sell fail`);
        }
        this.swap(dataEx); 
    }

    async updateBalance(selectedTokens:string[] = []) : Promise<boolean>{
        if(!this.enable || bot.mode == macro.BOT_MODE.LOCAL) return false;
        //for(const [tokenName,v] of Object.entries(bot.config.token)){
        let sum = 0;
        let stableSum = new SumToken();
        const { stableTokens } = bot.config;
        //bear 没有贪婪模式, 使用配置表的数据
        const allTokens : string[] = [];
        Object.keys(bot.config.tokens).forEach(x=> allTokens.push(x));
        //const allTokens = tools.arrConcat(activeTokens, bot.config.stableTokens);

        for(const addr of allTokens){
            if(selectedTokens.length > 0 && !selectedTokens.includes(addr)) continue;
            const bToken = bot.token(addr);
            let balance : {bn:BigNumber, num:number};
            try {
                balance = await this.getBalance(addr);
            } catch(e){
                //console.log(e);
                Lazy.ins().log1(`${macro.COLOR.Red}error1 loading balance of ${bToken.symbol}: ${addr} ${macro.COLOR.Off}`);
                continue;
            }
            //不操作稳定币
            if(stableTokens.includes(addr)){
                this.tokenBalance.setData(addr, new TokenBalanceItem({ usdt:balance, token:balance }))
                sum += balance.num;
                stableSum.add(addr, balance.num);
                continue;
            }; 

            this.tokenBalance.setData(addr, new TokenBalanceItem({token : balance }));
            if(balance.num > 0){
                //计算价格
                try {
                    const sellAmount = await this.getBestAmountOut(addr, balance.bn, false);
                    sum += sellAmount.out.num;
                    stableSum.add(bToken.mainStableToken, sellAmount.out.num);
                } catch(e){
                    console.log(e);
                    Lazy.ins().log1(`${macro.COLOR.Red}error2 loading balance of ${bToken.symbol}: ${addr}${macro.COLOR.Off}`);
                    continue;
                }
            }
        }
        if(selectedTokens.length > 0) return true; //单个更新不走后面的逻辑
        
        this.st.show('[bear]', stableSum);
        //setTimeout(()=>{ this.updateBalance() }, 20000);
        return true;
    }

    async autoBuyBack(selectedTokens:string[] = []){
        if(!this.enable || bot.mode == macro.BOT_MODE.LOCAL) return false;
        const bonus = this.greedMode ? 1.0005 : 1.002;
        const data = this.tokenBuyBack.data();
        for(const tokenAddr of Object.keys(data)){
            if(bot.config.stableTokens.includes(tokenAddr)) continue;
            //遍历时可以会有正在卖出的token, 避免报错
            if(!this.tokenBuyBack.has(tokenAddr)) continue;
            if(selectedTokens.length > 0 && !selectedTokens.includes(tokenAddr)) continue;

            const bToken = bot.token(tokenAddr);
            const bTokenStable = bot.token(bToken.mainStableToken);
            //上次卖出时候获得的token
            const {usdt, token} = data[tokenAddr];
            const now = await this.getBestAmountOut(tokenAddr, usdt.bn, true, macro.slippage.low, true);
            Lazy.ins().log1(`${macro.COLOR.On_IBlack}[buyBack]${macro.COLOR.Blue} ${bToken.symbol}/${bTokenStable.symbol}, `
                +`usdt:${usdt.num.toFixed(2)}, `
                +`token:${now.out.num.toFixed(1)}/${token.num.toFixed(1)}/${(token.num * bonus).toFixed(1)} `
                +`price:${(usdt.num/now.out.num).toFixed(3)}/${(usdt.num/token.num).toFixed(3)}/${(usdt.num/token.num*bonus).toFixed(3)}${macro.COLOR.Off}`);

            if(now.out.num > token.num * bonus){
                if(this.tokenBalance.info(bToken.mainStableToken).usdt.num > 0){
                    this.buyToken(new BaseSwapExData({data:now}));
                } else {
                    Lazy.ins().log1("## not enough money");
                }
            }
        }
        return true;
    }

    async sellExtra(){
        if(!this.enable) return false;
        //bear 没有贪婪模式, 使用配置表的数据
        const color = macro.COLOR;
        for(const addr of Object.keys(bot.config.tokens)){
            if(bot.config.stableTokens.includes(addr)) continue;
            const t = this.tokenBalance.info(addr);
            
            const balance = await this.getBalance(addr);
            const balanceNum =balance.num;

            const token = bot.token(addr);
            if(t.token.num <= 0) continue;
            const totalWeight = token.sum.token;
            const holdingP = t.token.num == 0 ? 0 : ((t.token.num / totalWeight) * 100).toFixed(2);
            const maxP = token.max == 0 ? 0 : ((token.max / totalWeight) * 100).toFixed(2);
            const bStable = bot.token(token.mainStableToken);
            const sell = await this.getBestAmountOut(addr, t.token.bn, false);
            Lazy.ins().log1(` ## bear holding: ${bStable.symbol.padStart(5, " ")}:${sell.out.num.toFixed(2).padStart(10, " ")} ${color.Green}[${token.symbol.padEnd(8, " ")}]${color.Off} ${balanceNum.toFixed(3)}/${token.max}, ${(balanceNum >= token.max && token.max > 0) ? color.Green : color.White}${holdingP}%${color.Off} / ${maxP}%`);
            if(token.max > 0 && t.token.num > token.max * 1.2) {
                Lazy.ins().log1(" ########################## ");
                Lazy.ins().log1(` # sell Extra balance : [${token.symbol}] ${t.token.num}/${token.max}`);
                const sub = t.token.bn.sub(token.toBigNumber(token.max));
                const sell = await this.getBestAmountOut(addr, sub, false, macro.slippage.normal, true);
                this.sellToken(new BaseSwapExData({data:sell}), undefined, true);
            }
        }
    }

}