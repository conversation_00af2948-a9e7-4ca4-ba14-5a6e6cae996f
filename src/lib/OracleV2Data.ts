import { ethers , BigNumber, utils} from "ethers";
import { macro } from "./macro";
import tools from "./tools";
import bot from "../bot";
import fs from "fs";
import ExTimer, { ExTimerFlag } from "./comp/ExTimer";
import { ExContract } from "./comp/EthersWrapper";
import Lazy from "./lazy/LazyController";
import DataType, { MevPath } from "./type/DataType";
import PairManager from "./comp/PairManager";
import { TokenRouter } from "./type/Token";
import { Pair, PairExtend } from "./type/Pair";
import { ConsumeController} from "./type/Mev";

export default class OracleV2Data {
    private file_checked = `./src/data/${bot.chain}/router_${bot.chain}_checked.json`;
    private file_checked_mev = `./src/data/${bot.chain}/router_${bot.chain}_checked_mev.json`;

    private LOWEST_POOL_VALUES = 0.03;
    private CALC_VALUES_LOSS = 0.4;

    cache : {
        factorys : {
            [addr:string]: {
                routers:string[],
                pairs:Array<Pair>,
                fee?:number,
                pairLength:number,
            }
        },
        timestamp : number
    } = {factorys:{}, timestamp:0};

    pm : PairManager = new PairManager();

    //获取白名单内交易币的路由矩阵 [token0][token1][router] = factory
    //获取所有稳定币的交易对, 只需要初始化一次
    async init(){
        await this.syncAllPairs();
    
        //除去blacklist的所有token
        console.log("[OracleV2] generating token list...");

        //读取mevCache更新到activeLps
        Lazy.ins().logTs1(`[OracleV2] get mev path:  ...${this.file_checked_mev}`);
        let rawMev : {[addr:string]: MevPath[]} = {};
        if(fs.existsSync(this.file_checked_mev)){
            rawMev  = JSON.parse(fs.readFileSync(this.file_checked_mev, 'utf-8'));
            console.log("[OracleV2] mev path length : ", Object.keys(rawMev).length);
        } else {
            console.log("[OracleV2] ### no mev path ######");
        }

        //更新 activeLps
        console.log("[OracleV2] generating active pairs and lpMap...");
        for(const f of Object.values(this.cache.factorys)){
            for(let i = 0; i< f.pairs.length; i++){
                const p = f.pairs[i];
                const {address, factory} = p;

                //if(bot.config.whiteListPair[p.address]) p.status = macro.PAIR_STATUS.ACTIVE;
                //if(p.status !== macro.PAIR_STATUS.ACTIVE ) continue;
                if(p.reserve0 <= 0 || p.reserve1 <= 0) continue;
                /*
                if(bot.mode !== macro.BOT_MODE.MEV && bot.mode !== macro.BOT_MODE.UPDATE){
                    if(!rawMev[p.address]) {
                        continue; //正式环境里不缓存非pump的pair
                    }
                }
                */

                const pEx = Object.assign(new PairExtend(), p);
                if(rawMev[pEx.address]) pEx.mevPath = rawMev[pEx.address];

                pEx.routers = this.cache.factorys[factory].routers;
                pEx.updateFee();
                this.pm.setSafe(pEx);
            }
        }
        //console.log("test: ", this.pm.get("0xEb579ddcD49A7beb3f205c9fF6006Bb6390F138f".toLowerCase()));
        //console.log("activeLps: ", Object.keys(this.activeLps).slice(0,2));
        //生成路由矩阵
        console.log("[OracleV2] generating routers matrix...");
        //排序pairmap
        this.pm.index.sortAll();

        console.log("[OracleV2] ------------");
        console.log("[OracleV2] cache lp length: ", this.pm.data.size);
        console.log("[OracleV2] ------------");
        console.log("[OracleV2] token length: ", this.pm.tokens.data.size);



        //更新一次所有pair的reserve
        if(bot.mode !== macro.BOT_MODE.LOCAL && bot.mode !== macro.BOT_MODE.UPDATE && bot.mode !== macro.BOT_MODE.MEV && bot.mode !== macro.BOT_MODE.PAIR && bot.mode !== macro.BOT_MODE.DEBUG){
            await this.updateAllPairReserves();
        }

        //计算出最佳交易路由
        this.updateBestPath();
        
        console.log("[OracleV2] done");
        //统计稳定币的交易目标 (打印大纲)
        console.log("[OracleV2] stableTokenStatus:");
        const stableTokenStatus : {[addr:string]: string[]} = {}
        bot.config.stableTokens.forEach((x) => stableTokenStatus[x]=[]);
        for(const t of this.pm.tokens.data.values()){
            if(t.active && stableTokenStatus[t.mainStableToken]){
                stableTokenStatus[t.mainStableToken].push(bot.token(t.address).symbol);
            }
        };
        if(bot.mode !== macro.BOT_MODE.LOCAL){
            for(const [addr, count] of Object.entries(stableTokenStatus)){
                console.log("  #", bot.config.tokens[addr].name, ': ', count.length, count.slice(0, 50).join(', '));
            }
        }
        //定时更新bestPath
        ExTimer.setIntervalOld(()=>{ this.updateBestPath() }, 1000 * 60 * 15); //每60分
    }

    private isBlackList(addr:string){
        if(bot.config.tokenBlackList && bot.config.tokenBlackList[addr]){
            return true;
        } else {
            return false;
        }
    }

    //获取所有lp对，增量更新
    private async syncAllPairs(){
        let needUpdate = false;
        console.log(`file: ${this.file_checked}`);
        if(fs.existsSync(this.file_checked)){
            this.cache = JSON.parse(fs.readFileSync(this.file_checked, 'utf-8'));
        }
        //把缓存的token写入内存
        //this.tokens.setFromCache(this.cache.tokens);
        
        const routerInfos : {[addr:string]: {routers:string[], factory:ExContract}} = {};
        if(bot.mode == macro.BOT_MODE.UPDATE){
            for(const [addr,val] of Object.entries(bot.config.routers)){
                if(val.skipUpdate) continue; //跳过不需要更新的router
                await tools.delay(bot.config.updateAllPairsDelay); //避免同时请求数太多

                const factoryAddr : string = await bot.routers.get(addr).getFactory();
                this.cache.factorys[factoryAddr] ??= {routers:[addr], pairs:[], pairLength:0};
                const f = this.cache.factorys[factoryAddr];
                if(!f.routers.includes(addr)) f.routers.push(addr);

                //const factoryAddr = this.cache.routers[addr];
                routerInfos[factoryAddr] ??= {
                    routers : f.routers,
                    factory : bot.newContract(factoryAddr, macro.abi.factory, bot.operators[0].ins())
                }
                routerInfos[factoryAddr].routers = f.routers;
            }
            console.log("### update routers");
            let updateTime = 0; //计数器，计算更新了多少pair，超过10万个存档到本地，中断函数，手动进行下一次爬虫

            for(let r of Object.values(routerInfos)){
                await tools.delay(bot.config.updateAllPairsDelay); //避免同时请求数太多

                const router = bot.routers.get(r.routers[0]);
                const factoryAddr = r.factory.ins().address;
                const factorylength = await router.totalPairs();
                const factoryCache = this.cache.factorys[factoryAddr];
                factoryCache.pairs = factoryCache.pairs.filter( ps => ps.id != -1); //过滤掉自动添加的pair，避免影响增量更新
                const cacheLength = factoryCache.pairs.length > 0 ? factoryCache.pairs[factoryCache.pairs.length-1].id + 1 : 0;
                
                //缓存已有路由信息，判断是否需要更新
                if(cacheLength < factorylength) {
                    console.log(` ##### router need update`);
                    needUpdate = true;
                } else if(cacheLength > factorylength){
                    throw("### factory pair cacheLength error");
                }
                const nowTs = ~~(Number(new Date())/1000);
                console.log(`[${router.routerName}] ${r.routers} ${cacheLength}/${factorylength} `);
                const step = 300;
                let i = cacheLength
                while(i < factorylength){
                    /*
                    updateTime++;
                    if(updateTime > bot.MAX_UPDATE_PAIR){
                        this.cache.tokens = this.tokens.getObj();
                        this.cache.timestamp = ~~(Number(new Date())/1000);
                        tools.writeFileSync(this.file_checked, JSON.stringify(this.cache));
                        throw(`REACH updateTimeMax: ${bot.MAX_UPDATE_PAIR}, save data and reset bot for memory save.`);
                    }
                    */
                   let to = i+step-1;
                   if(to >= factorylength - 1) to = factorylength - 1;
                   let res = await router.batchGetRouterPairs(i, to);
                   if(bot.config.updateAllPairsDelay) await tools.delay(bot.config.updateAllPairsDelay); //避免同时请求数太多
                   for(const p of res){
                        console.log(`----- router: ${router.routerName} id: ${p.id}/${factorylength} pair: ${p.address} -------`);
                        p.factory = factoryAddr;
                        p.status = macro.PAIR_STATUS.UNCHECKED;
                        console.log(p);

                        if(r.routers[0] == "0xc873fecbd354f5a56e00e710b90ef4201db2448d") p.blockTimestampLast = 0; //arb camelot hack
                        //检查是否0，最近2个月是否有交易, 使用toString防止数据溢出时候的错误判断 //改成200天，某些交易不活跃的链有用
                        if(!bot.config.whiteListPair[p.address] && (p.reserve0.toString() == "0" || p.reserve1.toString() == "0" || (p.blockTimestampLast > 0 && (p.blockTimestampLast + 60*60*24 * 300) < nowTs))){
                            console.log(`  ${macro.COLOR.BBlack}...overdue or empty pair${macro.COLOR.Off}`);
                            continue;
                        }
                        factoryCache.pairs.push(p);
                   }
                    /*
                    let addr = await router.allPairs(i);
                    console.log(`----- router: ${router.routerName} id: ${i}/${factorylength} pair: ${addr} -------`);
                    try {
                        await tools.delay(bot.config.updateAllPairsDelay); //避免同时请求数太多
                        const p = await router.getPairInfo(addr);
                        p.id = i;
                        p.factory = factoryAddr;
                        p.status = macro.PAIR_STATUS.UNCHECKED;
                        console.log(p);

                        if(r.routers[0] == "0xc873fecbd354f5a56e00e710b90ef4201db2448d") p.blockTimestampLast = 0; //arb camelot hack

                        //检查是否0，最近2个月是否有交易, 使用toString防止数据溢出时候的错误判断 //改成200天，某些交易不活跃的链有用
                        if(!bot.config.whiteListPair[addr] && (p.reserve0.toString() == "0" || p.reserve1.toString() == "0" || (p.blockTimestampLast > 0 && (p.blockTimestampLast + 60*60*24 * 100) < nowTs))){
                            console.log(`  ${macro.COLOR.BBlack}...overdue or empty pair${macro.COLOR.Off}`);
                            continue;
                        }
                        factoryCache.pairs.push(p);
                    } catch(e) {
                        console.log("########## update error ############");
                        console.log(e);
                        console.log("routers: ", r.routers, "addr: ", addr);
                        console.log("[oreacleData] error");
                        await tools.delay(5000);
                    }
                    */
                    i += step;
                }
            }
        }

        //打印factory信息
        let totalActivePairs = 0;
        for(const[k,v] of Object.entries(this.cache.factorys)){
            let active = 0;
            let bad = 0;
            v.pairs.forEach(p=>{
                switch(p.status){
                    case macro.PAIR_STATUS.ACTIVE: {
                        active++;
                        totalActivePairs++;
                        break;
                    }
                    case macro.PAIR_STATUS.DISABLE: {
                        bad++;
                        break;
                    }
                }
            });
            const routerNames = v.routers.map(r => bot.config.routers[r]?.name || r).join(",");
            console.log(`[OracleV2] active pairs on ${routerNames.padStart(7, " ")}:  ${active} / ${bad} / ${v.pairs.length}`);
        }
        console.log("[OracleV2] total:", totalActivePairs);
        
        //this.cache.tokens = this.tokens.getObj();
        this.cache.timestamp = ~~(Number(new Date())/1000);
        //console.log(JSON.stringify(datas));
        if(needUpdate && bot.mode == macro.BOT_MODE.UPDATE){
            console.log("-----------------------");
            //tools.writeFileSync(this.file, JSON.stringify(this.cache));
            tools.writeFileSync(this.file_checked, JSON.stringify(this.cache));
            console.log(`[OracleV2] new json data save done: ${this.file_checked}`);
        }
        //return this.cache;
    }

    private isCex(tokenName:string){
        for(let reg in macro.cexTokens){
            if(tokenName.toLowerCase().includes(reg)){
                return true;
            }
        }
        return false;
    }

    addPairToCache(factory:string, pair:Pair){
        if(bot.mode == macro.BOT_MODE.DEBUG) return;
        let fCache = bot.oracleV2.data.cache.factorys[factory];
        fCache.pairs.push(pair);
        ExTimer.setTimeOut(ExTimerFlag.UPDATE_CACHE, ()=>{
            console.log(`-------- [updateCache] begin, pair: ${pair.address}--------`);
            //this.cache.tokens = this.pm.tokens.getObj();
            this.cache.timestamp = ~~(Number(new Date())/1000);
            tools.writeFileSync(this.file_checked, JSON.stringify(this.cache));
            const rawMev : {[addr:string] : MevPath[]} = {};
            for(let p of this.pm.data.values()){
                rawMev[p.address] = p.mevPath;
            }
            tools.writeFileSync(this.file_checked_mev, JSON.stringify(rawMev))
            console.log("-------- [updateCache] done --------");
        }, 10 * 60 * 1000); //10分钟后更新
    }

    async updateAllPairReserves(){
        Lazy.ins().logTs1("[OracleV2] update all pair...");
        const step = 100;
        let addrs : string[] = [];
        let i = 0;
        let length = this.pm.data.size;
        for(let p of this.pm.data.values()){
            addrs.push(p.address);
            if(addrs.length >= step || i >= length - 1){
                let res = await bot.contractIns.batchGetPairInfo(addrs);
                res.forEach(r=>{
                    const _p = this.pm.get(r.address);
                    _p.reserve0 = r.reserve0;
                    _p.reserve1 = r.reserve1;
                    Lazy.ins().log(`${macro.COLOR.BBlack}[${_p.id}/${length}] ${_p.address} ${_p.token0Symbol}-${_p.token1Symbol}${macro.COLOR.Off}`);
                });
                addrs = [];
                if(bot.config.updateAllPairsDelay > 0 ) await tools.delay(bot.config.updateAllPairsDelay); //避免同时请求数太多
            }
            i++;
        }
        Lazy.ins().logTs1("[OracleV2] update all pair...done");
    }

    //返回修正的tokenIn和tokenOut权重
    getFixedWeightOfPairs(tokenIn:string, pairs:string[], loseLength = 1){
        let weight0 = 0, weight1 = 0, tokenOut = "";
        for(let i = 0 ; i < pairs.length; i++){
            let {token0:t0, token1:t1, reserve0:r0, reserve1:r1} = this.pm.get(pairs[i]);
            if(i == 0) {
                weight0  = t0 == tokenIn ? r0 : r1;
                weight1  = t0 == tokenIn ? r1 : r0;
                tokenOut = t0 == tokenIn ? t1 : t0;
            } else {
                let w0 = 0, w1 = 0;
                if(tokenOut == t0){ //tokenOut是桥接token
                    w0 = r0;
                    w1 = r1;
                    tokenOut = t1;
                } else {
                    w0 = r1;
                    w1 = r0;
                    tokenOut = t0;
                }

                if(w0 < weight1){
                    weight0 = weight0 * w0 / weight1;
                    weight1 = w1;
                } else {
                    //weight0 = weight0;
                    weight1 = w1 * weight1 / w0;
                }
            }
        }
        //长度每长1,swap多一层损耗大, 这里粗略使用
        if(loseLength > 1) {
            const lose = 0.9;
            return [weight0 * Math.pow(lose, loseLength-1), weight1 * Math.pow(lose, loseLength-1)]
        } else {
            return [weight0, weight1];
        }
    }

    //更新最佳路由，定时更新或者被动更新
    updateBestPath(){
        if(bot.mode == macro.BOT_MODE.UPDATE || bot.mode == macro.BOT_MODE.MEV) return;
        Lazy.ins().logTs1("[updateBestPath]... begin");
        const stables = bot.config.stableTokens;
        const pairMap = this.pm.index;
        pairMap.sortAll();

        for(const [tokenCur, pairs] of pairMap.data){
            //if(tokenCur == "0x72cb10c6bfa5624dd07ef608027e366bd690048f") console.log("0x72cb10c6bfa5624dd07ef608027e366bd690048f (1): ", pairs[0]);
            const topPair = pairs[0];
            //console.log(topPair.fee0, topPair.fee1);

            //TODO: 新版本逻辑没有fee0和fee1，改成硬编码
            //if(topPair.fee0 < 0.997 || topPair.fee1 < 0.997) continue; //最大的pair是有fee，skip
            
            //if(tokenCur == "0x72cb10c6bfa5624dd07ef608027e366bd690048f") console.log("0x72cb10c6bfa5624dd07ef608027e366bd690048f", 1);
            const t = bot.token(tokenCur);

            //设置主交易货币
            for(let i = 0 ; i < pairs.length; i++){
                const {token0, token1} = pairs[i];
                const tokenOther = token0 == tokenCur ? token1 : token0;
                if(t.mainStableToken == "" && stables.includes(tokenOther)){
                    t.mainStableToken = tokenOther;
                    break;
                }
            }
            //if(tokenCur == "0x72cb10c6bfa5624dd07ef608027e366bd690048f") console.log("0x72cb10c6bfa5624dd07ef608027e366bd690048f", t.mainStableToken);
            const paths : Array<TokenRouter> = [];
            const usedPairs : {[addr:string]:boolean} = {};
            let stableWeight = 0, tokenWeight = 0;
            //token0是本币
            for(let i = 0; i < pairs.length; i++){
                if(pairs[i].status != macro.PAIR_STATUS.ACTIVE) continue;
                if(pairs[i].fee0 < 0.997 || pairs[i].fee1 < 0.997) continue;
                let {token0, token1, factory, address, reserve0, reserve1} = pairs[i];
                const router = this.cache.factorys[factory].routers[0];
                if(this.isBlackList(token0) || this.isBlackList(token1)) continue;
                if(token0 != tokenCur){
                    [token0, token1] = [token1, token0];
                    [reserve0, reserve1] = [reserve1, reserve0];
                }
                //稳定币-token LP，直接把r0 r1算入权重
                if(token1 == t.mainStableToken){
                    const d = new TokenRouter();
                    d.router = router;
                    d.path = [token1, token0];
                    d.pairs = [address];
                    d.addr = address;
                    d.tokenWeight = reserve0;
                    d.stableWeight = reserve1;
                    stableWeight += d.stableWeight;
                    tokenWeight += d.tokenWeight;
                    paths.push(d);
                    usedPairs[address] = true;
                } else {
                    //token1是否存在稳定币的交易对
                    //console.log(" *** ", t.mainStableToken, token0, token1);
                    //console.log(pairs[i]);

                    //if(this.matrix[t.mainStableToken]){
                        const mt = this.pm.matrix[t.mainStableToken]?.[token1];
                        if(mt){ //存在stable->token1->token0的交易对
                            const d = new TokenRouter();
                            d.router = router;
                            d.addr = address;
                            /*
                            if(mt[router]){ //是否同个router(依赖router的swap)
                                //计算两个lp token1的比例，获得正确的reserve0权重
                                const lpPreAddr = mt[router]; //tokenB与稳定币的lp
                                d.path = [t.mainStableToken, token1, token0];
                                d.pairs = [lpPreAddr, address];
                                const [w0,w1] = this.getFixedWeightOfPairs(t.mainStableToken, [lpPreAddr, address]);
                                d.tokenWeight = w1;
                                d.stableWeight = w0;
                            }
                            */

                            //找出所有的stable->token1中最大的不重复的pair
                            const prePairs = Object.values(mt).filter(p => !usedPairs[p]).sort((a,b)=> {
                                return this.pm.get(b).reserve0 - this.pm.get(a).reserve0
                            });
                            if(prePairs.length == 0) continue;
                            let topPrePair = prePairs[0];
                            d.router = router;
                            d.path = [t.mainStableToken, token1, token0];
                            d.pairs = [topPrePair, address];
                            d.addr = address;

                            const [w0,w1] = this.getFixedWeightOfPairs(t.mainStableToken, [topPrePair, address]);
                            d.tokenWeight = w1;
                            d.stableWeight = w0;

                            stableWeight += d.stableWeight;
                            tokenWeight += d.tokenWeight;

                            paths.push(d);
                            usedPairs[address] = true;
                            usedPairs[topPrePair] = true;
                        }
                    //}
                }
                //改成{key:value}格式，交易的时候再排序
                /*t.routers = t.routers.sort((a,b)=>{
                    return (b.weight - a.weight)
                });*/
            }
            //if(tokenCur == "0x72cb10c6bfa5624dd07ef608027e366bd690048f") console.log("0x72cb10c6bfa5624dd07ef608027e366bd690048f", paths);
            if(paths.length > 0){
                t.paths = paths.sort((a,b)=> b.stableWeight - a.stableWeight).slice(0,4); //取前4个
                t.sum.token = tokenWeight;
                t.sum.stable = stableWeight;
            }
        }
        for(const t of this.pm.tokens.data.values()){
            if(t.paths.length == 0){
                t.active = false;
            } else {
                if(this.isBlackList(t.address)) t.active = false;
                const cfg = bot.config.tokens[t.address];
                if(cfg){    
                    t.active = cfg.ignore ? false : true;
                } else {
                    //if((t.symbol !== "DFKGOLD" && t.symbol.includes("DFK")) || t.symbol.includes("REALTOKEN-")){ //one xdai
                    //    t.active = false;
                    //} else
                    if(bot.config.bot.crazy) {
                        t.active = true;
                    }
                }

            }

        }

        Lazy.ins().logTs1("[updateBestPath]... done");
    }

    private getPath(fromToken:string, toTokens:string[], exclude:string[]=[], isEoa=false){
        //获取所有 token-> stable(支持多个stable)路径，返回最高权重的那个
        //console.log("---> ", fromToken, toToken);
        const excludeLocal = [...exclude];
        let top1 = this.pm.index.getTopPairsOfToken(fromToken, excludeLocal, isEoa);
        if(top1.length == 0) return;
        top1.forEach(p => excludeLocal.push(p.address)); //第一层加入缓存

        let results : Array<{stable:string, pairs:string[]}> = [];

        top1.forEach(l1=>{
            const path = [l1.address];
            const tokenOuts1 = this.getTokenOuts(fromToken, path);
            const tokenOut1 = tokenOuts1[tokenOuts1.length-1];
            if(toTokens.includes(tokenOut1)){
                results.push({stable: tokenOut1, pairs:path});
            } else {
                const top2 = this.pm.index.getTopPairsOfToken(tokenOut1, excludeLocal, l1.version == macro.PAIR_VERSION.V2_EOA);

                if(top2.length > 0){
                    top2.forEach(l2=>{
                        const path2 = [...path];
                        path2.push(l2.address);
                        const tokenOuts2 = this.getTokenOuts(fromToken, path2);
                        const tokenOut2 = tokenOuts2[tokenOuts2.length-1];
                        if(toTokens.includes(tokenOut2)){
                            results.push({stable: tokenOut2, pairs:path2});
                        } else {
                            const top3 = this.pm.index.getTopPairsOfToken(tokenOut2, excludeLocal, l2.version == macro.PAIR_VERSION.V2_EOA);
                            if(top3.length > 0){
                                top3.forEach(l3=>{
                                    const path3 = [...path2];
                                    path3.push(l3.address);
                                    const tokenOuts3 = this.getTokenOuts(fromToken, path3);
                                    const tokenOut3 = tokenOuts3[tokenOuts3.length-1];
                                    if(toTokens.includes(tokenOut3)){
                                        results.push({stable: tokenOut3, pairs:path3});
                                    } else {
                                        //不搜索第三层
                                    }
                                })
                            }
                        }
                    });
                }
            }
        });
        //results.forEach(r=>{console.log(`${r.pairs.join(',')} ${this.calcLowestValue(r.stable, [...r.pairs].reverse()).lowest}`);});
        //权重排序
        results.sort((a,b)=>{
            //长度每长1,swap多一层损耗大
            //return this.getFixedWeightOfPairs(fromToken, b.pairs, b.pairs.length-1)[1] - this.getFixedWeightOfPairs(fromToken, a.pairs, a.pairs.length-1)[1];
                        
            const valueA = this.calcLowestValue(a.stable, [...a.pairs].reverse()).lowest * Math.pow(this.CALC_VALUES_LOSS, a.pairs.length);
            const valueB = this.calcLowestValue(b.stable, [...b.pairs].reverse()).lowest * Math.pow(this.CALC_VALUES_LOSS, b.pairs.length);;
            if(valueA !== valueB){
                return valueB - valueA;
            } else {
                return a.pairs.length - b.pairs.length;
            }
        });
        //console.log("-------------------");
        //results.forEach(r=>{ console.log(`${r.pairs.join(',')} ${this.calcLowestValue(r.stable, [...r.pairs].reverse()).lowest}`);});
        //throw("exit");
       const r0 = results[0];
       if(r0){
            let tokenOuts = this.getTokenOuts(fromToken, r0.pairs);
            return { path: r0.pairs, tokenOut: tokenOuts[tokenOuts.length - 1]};
       }
       return;
    }

    _old_getMevPath(addr:string){
        addr = addr.toLocaleLowerCase();
        let mevPaths : MevPath[] = [];
        let s0 = "", s1 = "";
        for(const stable of bot.config.stableTokens){
            let usedPair = [addr];
            let stables = [stable];
            const cfg = bot.config.tokens[stable];
            if(cfg && cfg.eq) stables = [...stables, ...cfg.eq];
            //找10次
            for(let i = 0 ; i < 10; i++){
                let path = [addr];
                const {token0, token1, version} = this.pm.get(addr);
                const isEoa = version == macro.PAIR_VERSION.V2_EOA;
                if(!stables.includes(token0)){
                    //扩展左边
                    let left = this.getPath(token0, stables, usedPair, isEoa);
                    if(left){
                        left.path.reverse();
                        path = [...left.path,...path];
                        usedPair = [...left.path,...usedPair];
                        s0 = left.tokenOut;
                        //console.log(`[left] ${this.getTokenOuts(left.tokenOut,left.path,true).join('->')}`);
                    } else {
                        //console.log("left not found");
                        break;
                    }
                } else {
                    s0 = token0;
                }

                if(!stables.includes(token1)){
                    //扩展右边
                    let right = this.getPath(token1, stables, usedPair, isEoa);
                    if(right){
                        path = [...path,...right.path];
                        usedPair = [...usedPair,...right.path];
                        s1 = right.tokenOut;
                        //console.log(`[right] ${this.getTokenOuts(token1,right.path,true).join('->')}`);
                    } else {
                        //console.log("right not found");
                        break;
                    }
                } else {
                    s1 = token1;
                }
                //过滤规则，pair长度 > 5，pair含有4个以上其他stable (首尾2个，中间还有2个其他token)
                //const tokenOuts = this.getTokenOuts(s0, path);
                const {V1, V2, V2_ONLY_ROUTER, V2_EOA, V2_STABLE} = macro.PAIR_VERSION;
                if(path.length <= 4){
                    //let stableCount = 0;
                    //tokenOuts.forEach(t => {if(bot.config.stableTokens.includes(t)) stableCount++;});
                    //if(stableCount >= 4) break;
                    let mev = new MevPath();
                    mev.s0 = s0;
                    mev.s1 = s1;
                    if(s0 != s1 && (bot.config.tokens[s0].isEth && bot.config.tokens[s1].isEth)) mev.convertEth = 1;
                    mev.tokenIn0or1[0] = s0 == this.pm.get(path[0]).token0 ? 0 : 1;
                    mev.tokenIn0or1[1] = s1 == this.pm.get(path[path.length-1]).token0 ? 0 : 1;

                    mev.pairs = path;
                    //weight计算错误
                    mev.weight = this.getFixedWeightOfPairs(s0, path, path.length - 1)[0];
                    
                    let hasEoa = false;
                    const calcType = path.every((p)=>{ 
                        const version = this.pm.get(p).version;
                        if(version == V2_EOA) hasEoa = true;
                        return (version == V1 || version == V2 || version == V2_ONLY_ROUTER)
                    }) ? macro.MEV_TYPE.DXDY : macro.MEV_TYPE.BASE;

                    mev.type = hasEoa ? macro.MEV_TYPE.DXDY_EOA : calcType;
                    mevPaths.push(mev);
                }
            }
        }
        //去重 + 排序, 取头部15个
        mevPaths = mevPaths
            .filter((v, index, self) => 
                //不同货币重复但是顺序不一样
                self.findIndex(t=> [...t.pairs].sort().join('') === [...v.pairs].sort().join('')) === index
            )
            .filter((v,index,self)=> 
                //有更短的path
                !self.find((t, index2) => index !== index2 && v.pairs.join('').includes(t.pairs.join('')) && t.pairs.length > 1)
            )
            .sort((a,b)=> b.weight-a.weight)
            .slice(0, 15);

        if(bot.mode == macro.BOT_MODE.LOCAL){
            mevPaths.forEach(m=>{
                const stable = bot.token(m.s0);
                const weight = this.getFixedWeightOfPairs(m.s0, m.pairs, m.pairs.length-2)[1] * stable.price;
                console.log(`(${bot.config.tokens[stable.address].name}) ${this.getTokenOuts(stable.address, m.pairs, true).join(' -> ')} ${macro.COLOR.BBlack} weight:${weight}${macro.COLOR.Off}`);
                console.log(`${macro.COLOR.BBlack}${m.pairs.join(",")}${macro.COLOR.Off}`);
            });
            console.log(`length: ${mevPaths.length}`);
        }
        return mevPaths;
    }

    getMevPath(addr:string){
        addr = addr.toLocaleLowerCase();
        const {V1, V2, V2_ONLY_ROUTER, V2_EOA, V2_STABLE} = macro.PAIR_VERSION;
        let mevPaths : MevPath[] = [];
        
        //缓存所有消费的pair
        let consume = new ConsumeController();
        //consume.add(addr); //备份
        const mainPair = this.pm.get(addr); //消费掉全部，不参与后续的mev path

        let exclude = [addr];

        //更新stable对
        const stables = bot.config.stableTokens;
        const stableGroup : string[][] = [];
        let _s_tmp : string[] = [];
        stables.forEach(_s=>{
            if(_s_tmp.includes(_s)) return;
            _s_tmp.push(_s);
            let s : string[];
            const eq = bot.config.tokens[_s].eq;
            if(eq){
                s = [_s, ...eq];
                _s_tmp = _s_tmp.concat(eq);
            } else {
                s = [_s];
            }
            stableGroup.push(s);
        });
        /*
        stableGroup.forEach(x=>{
            console.log(`stables: ${x.map(_x=>bot.token(_x).symbol).join(",")}`);
        });
        */
        for(let i = 0 ; i < 15; i++) {
            //扩展左边
            const {token0, token1, version} = mainPair;
            const isEoa = version == V2_EOA;
            let _tmp : MevPath[] = [];
            //算出每种stable的top1
            stableGroup.forEach( _ss => {
                let s0 = "", s1 = "";
                let path = [addr];
                //扩展左边
                if(_ss.includes(token0)){
                    s0 = token0;
                } else {
                    let left = this.getPath(token0, _ss, exclude, isEoa);
                    if(left){
                        left.path.reverse();
                        path = [...left.path,...path];
                        //exclude = [...left.path,...exclude];
                        s0 = left.tokenOut;
                        //console.log(`[left] ${this.getTokenOuts(left.tokenOut,left.path,true).join('->')}`);
                    } else {
                        //console.log("left not found: ", token0, _ss); //左边不存在，跳过
                        return;
                    }
                }

                //扩展右边
                if(_ss.includes(token1)){
                    s1 = token1;
                } else {
                    let right = this.getPath(token1, _ss, exclude, isEoa);
                    if(right){
                        path = [...path,...right.path];
                        //exclude = [...exclude,...right.path];
                        s1 = right.tokenOut;
                        //console.log(`[right] ${this.getTokenOuts(token1,right.path,true).join('->')}`);
                    } else {
                        //console.log("right not found"); //右边不存在，跳过
                        return;
                    }
                }
                //新建mev
                let mev = new MevPath();
                mev.s0 = s0;
                mev.s1 = s1;
                if(s0 != s1 && (bot.config.tokens[s0].isEth || bot.config.tokens[s1].isEth)) mev.convertEth = 1;
                mev.tokenIn0or1[0] = s0 == this.pm.get(path[0]).token0 ? 0 : 1;
                mev.tokenIn0or1[1] = s1 == this.pm.get(path[path.length-1]).token0 ? 0 : 1;

                mev.pairs = path;
                //计算权重
                mev.weight = this.calcLowestValue(mev.s0, mev.pairs, addr).lowest;
                if(mev.weight < this.LOWEST_POOL_VALUES) return; //pool价值太小的舍去

                let hasEoa = false;
                const calcType = path.every((p)=>{ 
                    const version = this.pm.get(p).version;
                    if(version == V2_EOA) hasEoa = true;
                    return (version == V1 || version == V2 || version == V2_ONLY_ROUTER)
                }) ? macro.MEV_TYPE.DXDY : macro.MEV_TYPE.BASE;

                mev.type = hasEoa ? macro.MEV_TYPE.DXDY_EOA : calcType;
                _tmp.push(mev);
            });
            //console.log("_tmp.length: ", _tmp.length);
            //找出所有stables的最大价值路径
            if(_tmp.length == 0) break;
            if(_tmp.length > 1) {
                /*_tmp.forEach(_t=> {
                    console.log(`${this.getTokenOuts(_t.s0, _t.pairs, true).join(' -> ')} ${_t.weight}, ${_t.pairs.length}`);
                    console.log(`${macro.COLOR.BBlack}${_t.pairs.join(",")}${macro.COLOR.Off}`);
                });*/
                //两种stables的价格可能不即时，子pair的weight可能不对，sort不能排除重复的pairs
                //ex: USDT -> WOKT -> BABYOKX -> WOKT -> USDT
                _tmp = _tmp.filter((v,index,self)=> 
                    //有更短的path, 删除USDT -> WOKT -> BABYOKX -> WOKT -> USDT的情况
                    !self.find((t, index2) => index !== index2 && v.pairs.join('').includes(t.pairs.join('')) && t.pairs.length > 1)
                )
                .sort((a,b)=> {
                    if(b.weight !== a.weight){
                        //return b.weight - a.weight;
                        return b.weight * Math.pow(this.CALC_VALUES_LOSS, b.pairs.length) - a.weight * Math.pow(this.CALC_VALUES_LOSS, a.pairs.length);
                    } else {
                        return a.pairs.length - b.pairs.length;
                    }
                });

            }

            consume.consume(_tmp[0], addr);
            //consume.displayCache();
            mevPaths.push(_tmp[0]);
            //console.log("mevPaths.length: ", mevPaths.length);
        }
        mevPaths.sort((a,b)=> {
            if(b.weight !== a.weight){
                //return b.weight - a.weight;
                return b.weight * Math.pow(this.CALC_VALUES_LOSS, b.pairs.length) - a.weight * Math.pow(this.CALC_VALUES_LOSS, a.pairs.length);
            } else {
                return a.pairs.length - b.pairs.length;
            }
        });
        //consume.displayData();
        if(bot.mode == macro.BOT_MODE.LOCAL){
            mevPaths.forEach( m => {
                const stable = bot.token(m.s0);
                console.log(`(${bot.config.tokens[stable.address].name}) ${this.getTokenOuts(stable.address, m.pairs, true).join(' -> ')} ${macro.COLOR.BBlack} convertEth:${m.convertEth} weight:${m.weight}${macro.COLOR.Off}`);
                console.log(`${macro.COLOR.BBlack}${m.pairs.join(",")}${macro.COLOR.Off}`);
            });
            console.log(`length: ${mevPaths.length}`);
        }
        consume.resume();

        return mevPaths;
    }

    getTokenOuts(tokenIn:string, pairs:string[], displaySymbol = false){
        const tokenOuts = [];
        tokenOuts[0] = tokenIn;
        for(let i = 0 ; i < pairs.length; i++){
            tokenIn = tokenOuts[i];
            const {token0, token1} = this.pm.get(pairs[i]);
            tokenOuts[i+1] = tokenIn == token0 ? token1 : token0;
        }
        if(displaySymbol){
            return tokenOuts.map((t)=> bot.token(t).symbol);
        } else {
            return tokenOuts;
        }
    }

    //打印结果
    _printCheckPairLog(p:Pair, str=""){
        const TYPE = macro.PAIR_STATUS;
        switch(p.status){
            case TYPE.ACTIVE:
                console.log(`${str} ${p.address} ${p.token0Symbol}-${p.token1Symbol}`);
                break;
            case TYPE.OVERDUE:
                console.log(`${str} ${macro.COLOR.Red}${p.address} ${p.token0Symbol}-${p.token1Symbol} (overdue)${macro.COLOR.Off}`);
                break;
            case TYPE.DISABLE:
                console.log(`${str} ${macro.COLOR.BRed}${p.address} ${p.token0Symbol}-${p.token1Symbol} (bad)${macro.COLOR.Off}`);
                break;
            case TYPE.EMPTY:
                console.log(`${str} ${macro.COLOR.BPurple}${p.address} ${p.token0Symbol}-${p.token1Symbol} (empty)${macro.COLOR.Off}`);
                break;
        }
    }

    async updateAllMevPaths(){
        let result : {[addr:string]:MevPath[]} = {};
        let isFreshNew = true;
        if(fs.existsSync(this.file_checked_mev)){
            console.log('isFreshNew: false');
            isFreshNew = false;
            result = JSON.parse(fs.readFileSync(this.file_checked_mev, 'utf-8'));
        }
        //检查token里的eq是否精度相等
        for(let stable of bot.config.stableTokens){
            const eq = bot.config.tokens[stable].eq;
            if(eq){
                eq.forEach(e=>{
                    const tokenE = bot.token(e);
                    const tokenStable = bot.token(stable);
                    console.log(`${bot.config.tokens[tokenStable.address].name} <-> ${bot.config.tokens[tokenE.address].name}  decimals is ${tokenE.decimals}`)
                    if(tokenE.decimals != tokenStable.decimals){
                        throw(`ERROR!!! decimals is different!!! ${tokenE.symbol}:${tokenE.decimals}  ${tokenStable.symbol}:${tokenStable.decimals}`);
                    }
                });
            }
        }

        //计算每个stable coin的path，再取出所有stable中权重最大的前15个
        Lazy.ins().logTs("[updateMevPath]... generate map");

        const mevPairs :string[] = [];

        let pairs = Array.from(this.pm.data.values()).filter( _p => _p.status == macro.PAIR_STATUS.ACTIVE);

        for(let i = 0 ; i < pairs.length; i++){
            const p = pairs[i];
            //先生成所有mevpaths,再一起检查
            const mevPaths = this.getMevPath(p.address);
            if(mevPaths.length == 0) continue;
            if(bot.cmd == "debug") {
                mevPaths.forEach((m)=>{
                    m.fees0 = new Array(m.pairs.length).fill(0);
                    m.fees1 = new Array(m.pairs.length).fill(0);
                    m.gas = 200000;
                    m.status0 = macro.MEV_STATUS.ACTIVE;
                    m.status1 = macro.MEV_STATUS.ACTIVE;
                });
            }
            result[p.address] = mevPaths;
            //增量更新时，记录关联的旧pair，后续再更新一次
            if(!isFreshNew && mevPaths.length > 0){
                mevPaths.forEach(_m=>{
                    _m.pairs.forEach(_p=>{ if(!mevPairs.includes(_p) && _p !== p.address) mevPairs.push(_p);});
                });
            }

            if(i%100==0) console.log(`[updateMevPath] getMevPath (${i}), path:${mevPaths.length}`)
        }
        //消耗太多时间wss断开了，先hard code重连一下
        /*if(pairs.length > 15000){
            bot.handle.wss[0].listen(true);
            await tools.delay(8000);
        }*/


        if(!isFreshNew){
            //增量更新时要检查之前跑过的pair 2x
            for(const _p of mevPairs){
                result[_p] = this.getMevPath(_p);
            }
        }
        tools.writeFileSync(this.file_checked_mev, JSON.stringify(result));
        Lazy.ins().logTs("[updateMevPath]... check on chain");
    
        //生成path完毕，开始onChain检查的逻辑
        const _t = Object.keys(result);
        const lastAddr = _t[_t.length - 1];
        const step = 25;
        let j = 0; //用于备份
        let tmp : MevPath[] = [];
        let tmpAddr : string[] = [];

        for(const [addr, v] of Object.entries(result)){
            if(bot.cmd == "debug") break;
            if(v.length > 0 && v[0].status0 !== macro.MEV_STATUS.UNCHECKED) continue; //跳过已经检查过的
            tmp = tmp.concat(v);
            tmpAddr.push(addr);
            if(tmp.length > step || addr == lastAddr){
                let res = [];
                try {
                    res = await bot.contractIns.batchCheckMev(tmp);
                } catch(e){
                    console.log(e);
                    console.log("[ERR PAIRS] pm size: ", this.pm.data.size);
                    console.log("[ERR PAIRS] ", tmpAddr.join(", "));
                    await tools.delay(10000);
                    res = await bot.contractIns.batchCheckMev(tmp);
                    //throw("[ERR] batch updateAllMevPaths error on contract request ....");
                }
                    for(let k = 0 ; k < res.length; k++){
                        const {fee0, fee1, gas} = res[k];
                        tmp[k].fees0 = fee0;
                        tmp[k].fees1 = fee1;
                        tmp[k].gas   = bot.chain == macro.CHAIN.MXC ? gas + 150000 : gas + 60000; //每一条增加7万gas用于计划额外消耗
                        tmp[k].status0 = tmp[k].fees0.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
                        tmp[k].status1 = tmp[k].fees1.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
                    }

                    //打印结果
                    for(const addr of tmpAddr){
                        const p = this.pm.get(addr);
                        const all = result[addr].length;
                        let bad = 0;
                        let single = 0;
                        let both = 0;
                        result[addr].forEach(_r => {
                            if(p.version == macro.PAIR_VERSION.V2_EOA){
                                const length = _r.pairs.length;
                                _r.fees0 = new Array(length).fill(0);
                                _r.fees1 = new Array(length).fill(0);
                                _r.status0 = macro.MEV_STATUS.ACTIVE;
                                _r.status1 = macro.MEV_STATUS.ACTIVE;
                                _r.gas   = 2000000;
                            }
                            const {status0, status1} = _r;
                            if(status0 == macro.MEV_STATUS.ACTIVE && status1 == macro.MEV_STATUS.ACTIVE){
                                both++;
                            } else if(status0 == macro.MEV_STATUS.ACTIVE || status1 == macro.MEV_STATUS.ACTIVE){
                                single++;
                            } else {
                                bad++;
                            }
                        });
                        let color = macro.COLOR.BBlack;
                        if(both == all) color = macro.COLOR.Off;
                        if(bad == all) color = macro.COLOR.Red;
                        if(single == all) color = macro.COLOR.Purple;
                        Lazy.ins().log1(`${color}[updateMevPath (${j}/${pairs.length}](${bot.config.routers[p.routers[0]].name}) ${p.address} ${p.token0Symbol}-${p.token1Symbol} ${both}/${single}/${bad} (${all})${macro.COLOR.Off}`);
                        j++;
                        //每1000个pair备份一下
                        if(j % 2000 == 0){
                            console.log("####  backup every 1000 pairs");
                            tools.writeFileSync(this.file_checked_mev, JSON.stringify(result));
                        }
                    }

                    tmp = [];
                    tmpAddr = [];
            }
        }
    
        if(Object.keys(result).length == 0){
            throw("error pair length == 0");
        } else {
            tools.writeFileSync(this.file_checked_mev, JSON.stringify(result));
            Lazy.ins().logTs1(`[updateMevPath]... total:${pairs.length}) done`);
            console.log(`saving to ${this.file_checked_mev}`);
        }
    }

    async updateAllPairsAndFees(){
        console.log(`${macro.COLOR.Yellow}==== CHECK ALL PAIRS =====${macro.COLOR.Off}`);
        const TYPE = macro.PAIR_STATUS;
        const count = {total:0, active:0, bad:0, fee:0, honey:0};
        const stables = bot.config.stableTokens;
        const tokenInBalances : {[k:string]:BigNumber} = {};

        for(let t of stables){
            const amount = (await bot.client.bull.getOperator().ins().functions.getBalance(t))[0];
            if(amount.isZero()){
                throw(`[checkAllPairs] pump token is zero ${t}`);
            } else {
                const bToken = bot.token(t);
                console.log(` (${bToken.symbol}) ${bToken.toNum(amount)}`);
                tokenInBalances[t] = amount;
            }  
        }

        //获取所有pair
        console.log( "bull master: ", await bot.getbotOwner() );
        //重置所未检查的pair的fee
        for(const f of Object.values(this.cache.factorys)){
            for(const p of f.pairs){
                if(p.status !== TYPE.UNCHECKED) continue;
                //p.status = TYPE.UNCHECKED;
                p.f0 = 0;
                p.f1 = 0;
                const pEx = this.pm.get(p.address);
                if(pEx){
                    pEx.f0 = 0;
                    pEx.f1 = 0;
                    pEx.updateFee();
                    //throw(`[checkAllPairs] error with p:${p.address}`)
                }
                if(bot.cmd == "debug"){
                    p.status = TYPE.ACTIVE;
                }
            }
        }

        //每20个一组
        const step = 20;
        for(let i = 0 ; i < 2; i++){
            if(bot.cmd == "debug") break;
            console.log( i==0 ? `#################  1 checking stable-Token pairs #############` : `################# 2 checking token-token pairs #############`);
            for(const f of Object.values(this.cache.factorys)){
                let tmp : Pair[] = [];
                let z = 0;
                const filter = f.pairs.filter(_p => {
                    if(_p.status !== TYPE.UNCHECKED || (i==0 && !stables.includes(_p.token0) && !stables.includes(_p.token1))) return false;
                    return true;
                });
                for(const p of filter){
                    count.total++;
                    tmp.push(p);
                    //if(p.status !== TYPE.UNCHECKED) continue; //第二轮检查tokenA-tokenB
                    //if(i==0 && !stables.includes(p.token0) && !stables.includes(p.token1)) continue; //第一轮只查询stable-token
                    if(tmp.length >= step || (z == filter.length - 1 && tmp.length > 0)){
                        const fees = await bot.oracleV2.batchCheckPairFee(tmp);
                        for(let j = 0 ; j < tmp.length; j++){

                            tmp[j].f0 = fees[j][0];
                            tmp[j].f1 = fees[j][1];
                            if(tmp[j].version == macro.PAIR_VERSION.V2_EOA) {
                                tmp[j].f0 = 0;
                                tmp[j].f1 = 0;
                            }
                            
                            const {f0, f1, d0, d1} = tmp[j];

                            if(f0 > 20000 || f1 > 20000){
                                //f0 == 800000 || f1 == 800000
                                if(f0 == 666666 || f1 == 666666){
                                    tmp[j].status = TYPE.ACTIVE;
                                    tmp[j].f0 = 0;
                                    tmp[j].f1 = 0;
                                    count.honey++;
                                } else {
                                    tmp[j].status = TYPE.DISABLE;
                                    count.bad++;
                                }
                            } else if(d0 < 4 || d1 < 4){ //TODO: 暂时不处理精度小于6的代币
                                tmp[j].status = TYPE.DISABLE;
                                count.bad++;
                            } else {
                                tmp[j].status = TYPE.ACTIVE;
                                if(f0 > 0 || f1 > 0){
                                    count.fee++;
                                } else {
                                    count.active++;
                                }
                            }
                            const pairEx = this.pm.get(tmp[j].address);
                            pairEx.f0 = tmp[j].f0;
                            pairEx.f1 = tmp[j].f1;
                            pairEx.status = tmp[j].status;
                            pairEx.updateFee();
                            pairEx.routers = f.routers;
                            this._printCheckPairLog(tmp[j], ` (${tmp[j].id}/${f.pairs.length}) ${macro.COLOR.Blue}Fee: [${tmp[j].f0},${tmp[j].f1}]${macro.COLOR.Off}`);
                        }
                        tmp = [];
                    }
                    z++;
                }
            }
        }
        tools.writeFileSync(this.file_checked, JSON.stringify(this.cache));
        console.log(`check all paires done!! total:${count.total}, active:${count.active}, bad:${count.bad}, fee:${count.fee}, `
                        + `${count.honey > 0 ? macro.COLOR.BGreen : macro.COLOR.White}honey:${count.honey}${macro.COLOR.Off}`);
        console.log(`saving to... ${this.file_checked}`);
        
    }

    //计算每个pair的价值 todo: 移动到在checkMev阶段
    calcLowestValue(tokenIn:string, pairs:string[], exclude=""){
        const pairValues : number[] = [];
        let tokenOut = "";
        let rIn;
        let rOut;
        let tokenInPrice = bot.token(tokenIn).price;
        let tokenOutPrice = 0;
        for(let i = 0 ; i < pairs.length; i++){
            const p = bot.oracleV2.data.pm.get(pairs[i]);
            if(i==0){
                tokenOut = tokenIn == p.token0 ? p.token1 : p.token0;
            } else {
                tokenIn = tokenOut;
                tokenOut = tokenIn == p.token0 ? p.token1 : p.token0;
            }
            [rIn, rOut] = tokenIn == p.token0 ? [p.reserve0, p.reserve1] : [p.reserve1, p.reserve0];
            if(i > 0) tokenInPrice = tokenOutPrice;
            tokenOutPrice = tokenInPrice * rIn / rOut;
            pairValues.push(tokenInPrice * rIn * 2);
            //console.log(i, tokenInPrice, tokenOutPrice, rIn, rOut)
        }

        //找出最低价值的pair
        let lowest = 0;
        //let lowestIndex = 0;
        if(pairs.length == 1){
            lowest = pairValues[0];
            //lowestIndex = 0;
        } else {
            for(let i = 0 ; i < pairs.length; i++){
                if( exclude == pairs[i]) continue;
                if(lowest == 0 || (pairValues[i] < lowest)){
                    lowest = pairValues[i];
                    //lowestIndex = i;
                }
            }
        }

        //if(lowest == 0) console.warn(`# [calcLowestValue] err 0 : ${pairs.join(", ")}`);
        return {
            values : pairValues,
            lowest : lowest
        };
    }
}