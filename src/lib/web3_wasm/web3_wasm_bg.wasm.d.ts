/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export function __wbg_transactionjs_free(a: number): void;
export function __wbg_get_transactionjs_nonce(a: number, b: number): void;
export function __wbg_set_transactionjs_nonce(a: number, b: number, c: number): void;
export function __wbg_get_transactionjs_to(a: number, b: number): void;
export function __wbg_set_transactionjs_to(a: number, b: number, c: number): void;
export function __wbg_get_transactionjs_value(a: number, b: number): void;
export function __wbg_set_transactionjs_value(a: number, b: number, c: number): void;
export function __wbg_get_transactionjs_chain_id(a: number, b: number): void;
export function __wbg_set_transactionjs_chain_id(a: number, b: number, c: number): void;
export function __wbg_get_transactionjs_data(a: number, b: number): void;
export function __wbg_set_transactionjs_data(a: number, b: number, c: number): void;
export function __wbg_get_transactionjs_gas(a: number, b: number): void;
export function __wbg_set_transactionjs_gas(a: number, b: number, c: number): void;
export function __wbg_get_transactionjs_gas_price(a: number, b: number): void;
export function __wbg_set_transactionjs_gas_price(a: number, b: number, c: number): void;
export function __wbg_get_transactionjs_max_fee_per_gas(a: number, b: number): void;
export function __wbg_set_transactionjs_max_fee_per_gas(a: number, b: number, c: number): void;
export function __wbg_get_transactionjs_max_priority_fee_per_gas(a: number, b: number): void;
export function __wbg_set_transactionjs_max_priority_fee_per_gas(a: number, b: number, c: number): void;
export function __wbg_get_transactionjs_transaction_type(a: number, b: number): void;
export function __wbg_set_transactionjs_transaction_type(a: number, b: number, c: number): void;
export function transactionjs_new(a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number, o: number, p: number, q: number, r: number, s: number, t: number): number;
export function __wbg_signresult_free(a: number): void;
export function __wbg_get_signresult_r(a: number, b: number): void;
export function __wbg_set_signresult_r(a: number, b: number, c: number): void;
export function __wbg_get_signresult_s(a: number, b: number): void;
export function __wbg_set_signresult_s(a: number, b: number, c: number): void;
export function __wbg_get_signresult_v(a: number, b: number): void;
export function __wbg_set_signresult_v(a: number, b: number, c: number): void;
export function rustsecp256k1_v0_4_1_context_create(a: number): number;
export function rustsecp256k1_v0_4_1_context_destroy(a: number): void;
export function rustsecp256k1_v0_4_1_default_illegal_callback_fn(a: number, b: number): void;
export function rustsecp256k1_v0_4_1_default_error_callback_fn(a: number, b: number): void;
export function get_fibonacci(a: number): number;
export function init_web3(a: number, b: number): void;
export function sign(a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number): void;
export function sign_message(a: number, b: number, c: number, d: number, e: number): void;
export function sign_tx(a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number, o: number, p: number, q: number, r: number, s: number, t: number, u: number, v: number, w: number): void;
export function __wbindgen_add_to_stack_pointer(a: number): number;
export function __wbindgen_free(a: number, b: number): void;
export function __wbindgen_malloc(a: number): number;
export function __wbindgen_realloc(a: number, b: number, c: number): number;
