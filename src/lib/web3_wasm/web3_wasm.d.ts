/* tslint:disable */
/* eslint-disable */
/**
* @param {number} n
* @returns {number}
*/
export function get_fibonacci(n: number): number;
/**
* @param {string} url
*/
export function init_web3(url: string): void;
/**
* @param {Uint8Array} msg
* @param {string} key
* @param {bigint | undefined} chain_id
* @returns {Uint8Array}
*/
export function sign(msg: Uint8Array, key: string, chain_id?: bigint): Uint8Array;
/**
* @param {Uint8Array} msg
* @param {string} key
* @returns {Uint8Array}
*/
export function sign_message(msg: Uint8Array, key: string): Uint8Array;
/**
* @param {string} key
* @param {string} nonce
* @param {string} to
* @param {string} value
* @param {string} chain_id
* @param {string} transaction_type
* @param {string} data
* @param {string} gas
* @param {string} gas_price
* @param {string} max_fee_per_gas
* @param {string} max_priority_fee_per_gas
* @returns {string}
*/
export function sign_tx(key: string, nonce: string, to: string, value: string, chain_id: string, transaction_type: string, data: string, gas: string, gas_price: string, max_fee_per_gas: string, max_priority_fee_per_gas: string): string;
/**
*/
export class SignResult {
  free(): void;
/**
*/
  r: Uint8Array;
/**
*/
  s: Uint8Array;
/**
*/
  v: bigint;
}
/**
*/
export class TransactionJs {
  free(): void;
/**
* @param {string} nonce
* @param {string} to
* @param {string} value
* @param {string} chain_id
* @param {string} transaction_type
* @param {string} data
* @param {string} gas
* @param {string} gas_price
* @param {string} max_fee_per_gas
* @param {string} max_priority_fee_per_gas
*/
  constructor(nonce: string, to: string, value: string, chain_id: string, transaction_type: string, data: string, gas: string, gas_price: string, max_fee_per_gas: string, max_priority_fee_per_gas: string);
/**
*/
  chain_id: string;
/**
*/
  data: string;
/**
*/
  gas: string;
/**
*/
  gas_price: string;
/**
*/
  max_fee_per_gas: string;
/**
*/
  max_priority_fee_per_gas: string;
/**
*/
  nonce: string;
/**
*/
  to: string;
/**
*/
  transaction_type: string;
/**
*/
  value: string;
}
