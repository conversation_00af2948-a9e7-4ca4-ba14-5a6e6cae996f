import axios from "axios";
import { exec, spawn } from "child_process";


//https://docs.aws.amazon.com/zh_cn/AWSEC2/latest/UserGuide/spot-instance-termination-notices.html
const SERVER = "http://169.254.169.254/latest/meta-data/spot/instance-action";
console.log("---- aws spot shutdown action begin -----");
setInterval(()=>{
    axios.get(SERVER).then((e)=>{
        console.log(`[${(new Date()).toString()}] spot is ready to stop.`);
        console.log(`############################################################################`);
        console.log("########################### AWS SHUTING DOWN WARNING ######################");
        console.log("############################################################################");
        console.log(e.data);
        /*
        const pm2 = spawn("pm2", ["stop", "all"]);
        pm2.stdout.on("data", data => {
            console.log(`[PM2] stdout: ${data}`);
        });
        
        pm2.stderr.on("data", data => {
            console.log(`[PM2] stderr: ${data}`);
        });
        
        pm2.on('error', (error) => {
            console.log(`[PM2] error: ${error.message}`);
        });
        
        pm2.on("close", code => {
            console.log(`[PM2] child process exited with code ${code}`);
        });
        */

        exec("pm2 stop all", (error, stdout, stderr)=>{
            if (error) {
                console.log(`[PM2] error: ${error.message}`);
                return;
            }
            if (stderr) {
                console.log(`[PM2] stderr: ${stderr}`);
                return;
            }
            console.log(`[PM2] stdout: ${stdout}`);
        });

        exec("sudo docker stop $(sudo docker ps -aq)", (error, stdout, stderr)=>{
            if (error) {
                console.log(`[DOCKER] error: ${error.message}`);
                return;
            }
            if (stderr) {
                console.log(`[DOCKER] stderr: ${stderr}`);
                return;
            }
            console.log(`[DOCKER] stdout: ${stdout}`);
        });

    }).catch(e=>{
        //正常
        console.log(`[${(new Date()).toString()}][aws] good...`);
    });
}, 50 * 1000);

setInterval(()=>{
    exec("sudo rm -rf /var/log", (error, stdout, stderr)=>{
        if (error) {
            console.log(`[remove log] error: ${error.message}`);
            return;
        }
        if (stderr) {
            console.log(`[remove log] stderr: ${stderr}`);
            return;
        }
        console.log(`[remove log] stdout: ${stdout}`);
    });
}, 60 * 60 * 24 * 1000);

