// 用于转发websocket
import { ethers, utils } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";
import tools from "../tools";
import { ProviderBase } from "./ProviderBase";
import Lazy from "../lazy/LazyController";

export class ProviderHttp extends ProviderBase {
    delay = 2000;

    constructor(url: string, id:number, onlyBroadcast = false){
        super(url, id, onlyBroadcast);

        this.delay = bot.config.blockTime * 1000 * 2 / 3;
        
        if(id == 0 && !onlyBroadcast){
            if(bot.mode !== macro.BOT_MODE.PAIR && bot.mode !== macro.BOT_MODE.UPDATE && bot.mode !== macro.BOT_MODE.MEV && bot.mode !== macro.BOT_MODE.LOCAL){
                console.log("### polling for http sever ####");
                this.listen();
            }
        }
        this.isReady = true;
    }

    async listen(){
        while(true){
            if(bot.oracleV2?.isReady){
                console.log(`listening pair : ${bot.oracleV2.data.pm.data.size}`);
                this.getLogPolling();
                break;
            }
            await tools.delay(2500);
        }
    }

    getLogPolling(){
        this.provider.getBlockNumber().then(num=>{ //这里拿到的是当前已经打包好的块
            //Lazy.ins().logTs(`blocknum: ${num}`);
            bot.event.emit(macro.EVENT.WS_ON_BLOCK, num);
            if(num <= this.blockNum){
                setTimeout(() => { this.getLogPolling() }, this.delay);
                return;
            }
            let begin = Math.max(this.blockNum+1, num - 20); //获取最近不超过20个block的log
            this.blockNum = num;
            this.getlog(begin, num, ()=>{
                setTimeout(() => { this.getLogPolling() }, this.delay);
            }, ()=>{
                setTimeout(() => { this.getLogPolling() }, this.delay);
            });
        }).catch(e=>{
            console.log("[httpProvider] getBlockNumber error", e);
        });
    }

}