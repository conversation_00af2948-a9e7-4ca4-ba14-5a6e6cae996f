// 用于转发websocket
import { BigNumber, ethers, utils } from "ethers";
import bot from "../../bot";
import { ExContract, ExWallet } from "../comp/EthersWrapper";
import { macro } from "../macro";
import Lazy from "../lazy/LazyController";


export class ProviderBase {
    public id = -1;
    public url = "";
    public provider : ethers.providers.JsonRpcProvider | ethers.providers.WebSocketProvider;
    public speed = 0;
    public onlyBroadcast = true;
    public minGas = BigNumber.from('0');
    public isReady = false;

    public enablePost = true;

    public blockNum = 0;


    constructor(url: string, id:number, onlyBroadcast = false){
        this.url = url;
        this.id = id;
        this.onlyBroadcast = onlyBroadcast;

        if(url.includes('http')){
            this.provider = new ethers.providers.JsonRpcProvider(url);
        } else {
            this.provider = new ethers.providers.WebSocketProvider(url);
        }

        if(bot.chain == macro.CHAIN.CELO){
            const originalBlockFormatter = this.provider.formatter._block;
            this.provider.formatter._block = (value, format) => {
            return originalBlockFormatter(
                {
                gasLimit: BigNumber.from(0),
                ...value,
                },
                format,
            );
            };
        }
        console.log(`#### (${this.id}) connecting to: ${this.url} ${this.onlyBroadcast ? "[onlyBroadcast]" : ""}`);
        if(this.onlyBroadcast) return;
        
        this.updateMinGas();
        setInterval(this.updateMinGas.bind(this), 1000 * 60);
    }

    async updateMinGas(){
        try {
            this.minGas = await this.provider.getGasPrice();
        } catch(e){
            console.log(e);
            console.log( `[getGasPrice] error: ${this.url}`);
        }
    }

    _contactList : {[opAddr:string]: {[contractAddr:string] : ExContract}} = {};
    _walletList : {[addr:string]: ExWallet} = {};

    wallet(privateKey : string){
        const wallet = new ExWallet(privateKey, this.provider);
        this._walletList[wallet.ins().address] = wallet;
        return wallet;
    }

    contract(address:string, abi:any, wallet:ethers.Wallet){
        if(!this._contactList[wallet.address]){
            this._contactList[wallet.address] =  { [address] : new ExContract(address, abi, wallet)};
        } else {
            if(!this._contactList[wallet.address][address]){
                this._contactList[wallet.address][address] = new ExContract(address, abi, wallet);
            }
        }
        return this._contactList[wallet.address][address];
    }


    getlog(begin:number , to : number, onSuccess?:Function, onFail?:Function){
        this.provider.getLogs({fromBlock:begin, toBlock:to}).then(logs=>{
            //Lazy.ins().log(`[getlog] ${begin} -> ${to}, len: ${logs.length}`);
            for(let log of logs){
                if(log.topics.length !== 1 && log.topics[0] !== "0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1") continue;
                if(!bot.oracleV2?.data?.pm?.has(log.address)) continue;
                //const pair = bot.oracleV2.data.pm.get(log.address);
                const {address, data, blockNumber, transactionHash} = log;
                const [r0,r1] = bot.abiCoder.decode(["uint112", "uint112"], data);
                //console.log(`[${blockNumber}] ${address} r0:${utils.formatEther(r0)} r1:${utils.formatEther(r1)}`);
                bot.handle.onSync(address, r0, r1, transactionHash);    
            }
            //console.log("[getlog] end ------- ", to);
            onSuccess && onSuccess();
        }).catch(e=>{
            Lazy.ins().logWsTs(this.id,`${this.url}`);
            console.log("[httpProvider] getLogs error", e);
            onFail && onFail();
        });
    }

    subscribeLog(){
        
    }
}