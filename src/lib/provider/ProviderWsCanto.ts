import bot from "../../bot";
import { BigNumber, ethers, providers, utils } from "ethers";
import { ProviderWs } from "./ProviderWs";

export class ProviderWsCanto extends ProviderWs {

    async subscribeLog(){
        if(bot.config.disableSubscribeLog) return;
        //必须等oracle初始化后才有所有pair列表
        console.log(`subscribe log length: `, bot.oracleV2.data.pm.data.size);
        let filter = {
            address: bot.oracleV2.data.pm.data.keys(),
            topics : [
                //utils.id("Sync(uint112 reserve0, uint112 reserve1)")
                //"0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1",
                "0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822" //canto, 监听swap事件
            ]
        };
        (this.provider as providers.WebSocketProvider)._subscribe("logs", ["logs", filter], async (result) => {
            this.hasPending = true;
            //console.log(result);
            const {address, data, blockNumber, transactionHash} = result;
            //动态获取resers
            const [r0,r1] = await bot.oracleV2.data.pm.get(address).getReservesServer(false);
            //const [r0,r1] = bot.abiCoder.decode(["uint112", "uint112"], data);
            bot.handle.onSync(address, r0, r1, transactionHash);
        });
    }

}