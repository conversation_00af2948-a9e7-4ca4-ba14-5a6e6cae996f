import { BigNumber, ethers, providers } from "ethers";
import bot from "../../bot";
import HashPool from "../comp/HashPool";
import { macro } from "../macro";
import { ProviderHttp } from "./ProviderHttp";
import Lazy from "../lazy/LazyController";

export class ProviderHttpTxPool extends ProviderHttp {
    _pool = new HashPool(2000); //opbnb:2000, oec:500
    _txpoolDelay = 300; //300ms
    isOpen = false;

    constructor(url: string, id:number, onlyBroadcast = false){
        super(url, id, onlyBroadcast);
        this.isReady = true;
        this.isOpen = true;
        setTimeout(() => {
            this.getTxPool();
        }, 10 * 1000);
    }

    async getTxPool(){
        if(bot.mode == macro.BOT_MODE.UPDATE) return;
        //console.log(`${tools.now().str} getTxPool`);
        let res = await (this.provider as providers.JsonRpcProvider).send("txpool_content", []); 
        this._formartTxPool(res.pending);
        this._formartTxPool(res.queued);
        //console.log(res);
        setTimeout(()=>{
            this.getTxPool();
        }, this._txpoolDelay);
    }

    _formartTxPool(obj : any){
        if(!obj) return;
        //for( const [addr, p] of Object.entries(obj)){
        Object.keys(obj).forEach((addr:any)=>{
            const p = obj[addr] as {[nonce:string]:any};
            Object.keys(p).forEach((nonce)=>{
                const {to, hash} = p[nonce];
                //if(!bot.config.routers[to]) return; //忽略除了router以外的tx
                if(to == bot.config.bot.address && to == bot.config.bot2.address) return;
                
                if(!this._pool.has(hash)){
                    this._pool.add(hash);
                    if(bot.oracleV2.isReady) this._onPending(p[nonce]);
                }
            });
        });
    }
    _onPending(p:any){
        try {
            const pro = this.provider as ethers.providers.Web3Provider;
            let tx = pro.formatter.transactionResponse(p);
            //避免重复数据
            if(!bot.handle.hashPool.enable(tx.hash)) return;

            const blockNumber = bot.handle.blockNum;
            if (tx.blockNumber == null) {
                tx.confirmations = 0;
            } else if (tx.confirmations == null) {
                let confirmations = (blockNumber - tx.blockNumber) + 1;
                if (confirmations <= 0) { confirmations = 1; }
                tx.confirmations = confirmations;
            }
            const trans = pro._wrapTransaction(tx);
            //Lazy.ins().log(`${macro.COLOR.BBlack}[http txpool] ${blockNumber} ${trans.hash} ${trans.from} (${trans.nonce})${macro.COLOR.Off}`);
            bot.handle.onPending(trans, this.id);
        } catch(e){
            console.log('########### ws onpending error', e);
        }
    }

}