import { ethers } from "ethers";
import { Network, Networkish } from "@ethersproject/networks";
import { Logger } from "@ethersproject/logger";
import { defineReadOnly } from "@ethersproject/properties";


const logger = new Logger("cfx1.0");

export interface WebSocketLike {
    onopen: ((...args: Array<any>) => any) | null;
    onmessage: ((...args: Array<any>) => any) | null;
    onerror: ((...args: Array<any>) => any) | null;

    readyState: number;

    send(payload: any): void;
    close(code?: number, reason?: string): void;
}

export default class CfxWebSocketProvider extends ethers.providers.WebSocketProvider {

    constructor(url: string | WebSocketLike, network?: Networkish) {

        // This will be added in the future; please open an issue to expedite
        if (network === "any") {
            logger.throwError("WebSocketProvider does not support 'any' network yet", Logger.errors.UNSUPPORTED_OPERATION, {
                operation: "network:any"
            });
        }

        if (typeof(url) === "string") {
            super(url, network);
        } else {
            super("_websocket", network);
        }

        this._pollingInterval = -1;

        this._wsReady = false;

        if (typeof(url) === "string") {
            defineReadOnly(this, "_websocket", new WebSocket(this.connection.url));
        } else {
            defineReadOnly(this, "_websocket", url);
        }

        defineReadOnly(this, "_requests", { });
        defineReadOnly(this, "_subs", { });
        defineReadOnly(this, "_subIds", { });
        defineReadOnly(this, "_detectNetwork", super.detectNetwork());

        // Stall sending requests until the socket is open...
        this.websocket.onopen = () => {
            this._wsReady = true;
            Object.keys(this._requests).forEach((id) => {
                this.websocket.send(this._requests[id].payload);
            });
        };

        this.websocket.onmessage = (messageEvent: { data: string }) => {
            const data = messageEvent.data;
            const result = JSON.parse(data);
            if (result.id != null) {
                const id = String(result.id);
                const request = this._requests[id];
                delete this._requests[id];

                if (result.result !== undefined) {
                    //@ts-ignore
                    request.callback(null, result.result);

                    this.emit("debug", {
                        action: "response",
                        request: JSON.parse(request.payload),
                        response: result.result,
                        provider: this
                    });

                } else {
                    //@ts-ignore
                    let error: Error = null;
                    if (result.error) {
                        error = new Error(result.error.message || "unknown error");
                        defineReadOnly(<any>error, "code", result.error.code || null);
                        defineReadOnly(<any>error, "response", data);
                    } else {
                        error = new Error("unknown error");
                    }

                    request.callback(error, undefined);

                    this.emit("debug", {
                        action: "response",
                        error: error,
                        request: JSON.parse(request.payload),
                        provider: this
                    });

                }

            } else if (result.method === "eth_subscription") {
                // Subscription...
                const sub = this._subs[result.params.subscription];
                if (sub) {
                    //this.emit.apply(this,                  );
                    sub.processFunc(result.params.result)
                }

            } else {
                console.warn("this should not happen");
            }
        };

        // This Provider does not actually poll, but we want to trigger
        // poll events for things that depend on them (like stalling for
        // block and transaction lookups)
        const fauxPoll = setInterval(() => {
            this.emit("poll");
        }, 1000);
        if (fauxPoll.unref) { fauxPoll.unref(); }
    }


    async _subscribe(tag: string, param: Array<any>, processFunc: (result: any) => void): Promise<void> {
        let subIdPromise = this._subIds[tag];
        if (subIdPromise == null) {
            subIdPromise = Promise.all(param).then((param) => {
                return this.send("cfx_subscribe", param);
            });
            this._subIds[tag] = subIdPromise;
        }
        const subId = await subIdPromise;
        this._subs[subId] = { tag, processFunc };
    }


}