// 用于转发websocket
import { BigNumber, ethers, providers, utils } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";
import Lazy from "../lazy/LazyController";
import tools from "../tools";
import { ProviderWs } from "./ProviderWs";


export class ProviderWsSafe extends ProviderWs {

    async subscribe(){
        if(this.isListenPending){
            this.provider.on('pending', async (tx:any)=>{
            //p._subscribe("newPendingTransactions", ["newPendingTransactions"], async (tx:any)=>{
                //const beginTime = Number(new Date());
                //Lazy.ins().logTs(`tx: ${tx}`);
                if(this.onlyBroadcast) {
                    this.hasPending = true;
                    return;
                }
                //避免重复数据
                if(!bot.handle.hashPool.enable(tx)) return;
                if(this.checkSpeed){
                    this.beginTime = new Date().getTime();
                    this.checkSpeed = false;
                }

                Promise.all([
                    this.provider.getTransaction(tx),
                    this.provider.getTransactionReceipt(tx),
                ]).then(r => {
                    const [trans, receipt] = r;
                    // Make sure transaction hasn't been mined
                    if (receipt !== null) return;

                    if(this.beginTime > 0){
                        this.speed = new Date().getTime() - this.beginTime;
                        this.beginTime = 0;
                    }
                    if(trans && trans.to){
                        this.hasPending = true;
                        if(bot.config.blackList.includes(trans.from)) return;
                        bot.handle.onPending(trans, this.id);
                        //bot.handle.onPendingTs(trans, this.id, beginTime);

                    }
                });

            });
        }
        this.provider.on("block", (blockNumber :number)=>{
            //p._subscribe("newHeads", ["newHeads"], (result) => {
                bot.event.emit(macro.EVENT.WS_ON_BLOCK, blockNumber);
                //Lazy.ins().logTs(`block: ${blockNumber}`);
                this.checkSpeed = true;

                if(bot.config.disableSubscribeLog && this.blockNum + 20 >= blockNumber) this.getlog(this.blockNum+1, blockNumber); //监听不超过20个block

                this.blockNum = blockNumber;
    
                clearTimeout(this.reconnectTimeout);
                this.reconnectTimeout = setTimeout(this.resetProvider.bind(this), 1000 * 10 * bot.config.blockTime); //10个块没有收到block重启

                if(blockNumber == 0) return; //没有同步上的时候blockNum是0

                if(this.hasPending){
                    this.lastPendingBlock = blockNumber;
                    this.hasPending = false;
                } else if(this.isListenPending) {
                    const length = blockNumber - this.lastPendingBlock;
                    if(length > bot.config.maxNoPendingBlock){
                        Lazy.ins().logTs1(`${macro.COLOR.Red}#### (${this.id}) ${this.url} no pending over 60 block  #############${macro.COLOR.Off}`);
                        this.hasPending = true;
                        this.lastPendingBlock = blockNumber;
                        this.resetProvider();
                    }
                }
        });
        if(bot.mode == macro.BOT_MODE.MEV) return;
        if(bot.config.disableSubscribeLog) return;

        if(this.isListenLog){
            while(true){
                if(bot.oracleV2?.isReady){
                    this.subscribeLog();
                    break;
                }
                await tools.delay(3000);
            }
        }
    }
}