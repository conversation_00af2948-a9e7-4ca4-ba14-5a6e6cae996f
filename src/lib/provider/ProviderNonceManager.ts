import bot from "../../bot"
import ExTimer from "../comp/ExTimer";
import <PERSON><PERSON> from "../lazy/LazyController";
import { macro } from "../macro";

export default class ProviderNonceManager {
    private nonces : {[address:string] : number} = {}
    private updateTime;

    constructor(updateTime = 30){
        this.updateTime = updateTime;
    }

    init(){
        if(bot.mode == macro.BOT_MODE.LOCAL || bot.mode == macro.BOT_MODE.UPDATE || bot.mode == macro.BOT_MODE.MEV) return;
        this.updateAll();
    }

    async updateAll(){
        console.log('[nonce manager] ###### updateAll');
        for(let address of Object.keys(this.nonces)){
            if(this.nonces[address] == -1){
                await this.update(address);
            }
        }
    }

    set(address:string){
        //console.log("listen nonce: ", address);
        this.nonces[address] = -1;
    }

    /* delay: s */
    async update(address:string, silence = false, delay = 0, overwrite = true){
        ExTimer.setTimeOut(address, ()=>{
            bot.provider().getTransactionCount(address).then((nonce:number)=>{
                this.nonces[address] = nonce;
                if(!silence) Lazy.ins().log1(`${macro.COLOR.BBlack} [nonce] overwrite:${overwrite} update: ${address} n:${this.nonces[address]}${macro.COLOR.Off}`);
            }).catch(e=> {
                console.log('[update] nonce error', e);
            });
        }, delay * 1000, overwrite);
    }

    async updatePending(address:string, silence = false, delay = 0){
        ExTimer.setTimeOut(address + '_pending', ()=>{
            bot.provider().getTransactionCount(address, 'pending').then((nonce:number)=>{
                this.nonces[address] = nonce;
                if(!silence) Lazy.ins().log1(`${macro.COLOR.BBlack} [nonce] pending update: ${address} n:${this.nonces[address]}${macro.COLOR.Off}`);
            }).catch(e=> {
                console.log('[updatePending] nonce error', e);
            });
        }, delay * 1000);
    }

    //使用nonce之后设为-1，updateTime之后自动更新两次
    async use(address:string){
        this.nonces[address] ??= -1;
        let nonce = this.nonces[address];
        if(nonce == -1 || !bot.handle.isListeningPending){
            //动态获取
            try {
                nonce = await bot.provider().getTransactionCount(address);
                Lazy.ins().logTs1(`${macro.COLOR.Purple} dynamic nonce${macro.COLOR.Off}: ${address}, n:${nonce}`);
            } catch(e){
                console.log("get nonce error: ", e);
            }
        } else {
            this.nonces[address]++;
        }
        this.updatePending(address, false, this.updateTime);
        this.update(address, false, this.updateTime * 5);
        return nonce;
    }

    //速度或并发要求高才使用，使用后nonce在本地自动+1，如果是-1抛出异常
    useSync(address:string){
        this.nonces[address] ??= -1;
        let nonce = this.nonces[address];
        if(nonce == -1){
            Lazy.ins().logTs1(`${macro.COLOR.Purple} dynamic nonce${macro.COLOR.Off}: ${address}, n:${nonce}`);
        }
        this.nonces[address]++;
        this.updatePending(address, false, this.updateTime);
        this.update(address, false, this.updateTime * 5);
        return nonce; 
    }

    //不自动更新，主要用于有pending的场合
    get(address:string){
        this.nonces[address] ??= -1;
        return this.nonces[address];
    }

}