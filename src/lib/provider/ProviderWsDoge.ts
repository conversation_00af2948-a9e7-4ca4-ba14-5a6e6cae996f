import { BigNumber, ethers, providers } from "ethers";
import bot from "../../bot";
import HashPool from "../comp/HashPool";
import { macro } from "../macro";
import Lazy from "../lazy/LazyController";
import tools from "../tools";
import { ProviderWs } from "./ProviderWs";

export class ProviderWsDoge extends ProviderWs {
    speed = 0;
    enablePost = false;

    _pool = new HashPool(2000);
    cacheReady = false;
    reconnectTime = 90;

    isOpen = false;
    mevs = [
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************"
    ];

    constructor(url: string, id:number){
        super(url, id, true);
    }

    async getTxPool(){
        if(!this.isOpen || bot.mode == macro.BOT_MODE.UPDATE) return;
        //console.log(`${tools.now().str} getTxPool`);
        let res = await (this.provider as providers.WebSocketProvider).send("txpool_content",[]); 
        this._formartTxPool(res.pending);
        //this._formartTxPool(res.queued);
        //ethers.providers.TransactionResponse
        //console.log(res);
        
        setTimeout(()=>{
            this.getTxPool();
        }, 40);
    }
    _formartTxPool(obj : any){
        if(!obj) return;
        this.hasPending = true;
        if(!this.isReady){
            this.isReady = true;
            this.reconnectWalletAndContract();
            bot.event.emit(macro.EVENT.WS_READY, this.id);
        }
        //for( const [addr, p] of Object.entries(obj)){
        Object.keys(obj).forEach((addr:any)=>{
            const p = obj[addr] as {[nonce:string]:any};
            Object.keys(p).forEach((nonce)=>{
                const {hash} = p[nonce];
                let to = p[nonce].to ? p[nonce].to.toLowerCase() : macro.zeroAddress;
                if(this._pool.has(hash)) return;
                //TODO: 记录unknow router
                if(this.mevs.includes(to)) return; //忽略mev机器人
                if(!bot.config.routers[to] && to !== bot.config.bot.address && to !== bot.config.bot2.address) return; //忽略除了router以外的tx
                
                //new pending
                this._pool.add(hash);
                if(this.cacheReady) this._onPending(p[nonce]);
            });
        });
    }
    _onPending(p:any){
        try {
            const pro = this.provider as ethers.providers.Web3Provider;
            let tx = pro.formatter.transactionResponse(p);
            tx.from =tx.from.toLowerCase();
            tx.to = tx.to?.toLowerCase();
            //if(!bot.handle.hashPool.enable(tx.hash)) return;

            if (tx.blockNumber == null) {
                tx.confirmations = 0;
            } else if (tx.confirmations == null) {
                const blockNumber = this.blockNum;
                // Add the confirmations using the fast block number (pessimistic)
                let confirmations = (blockNumber - tx.blockNumber) + 1;
                if (confirmations <= 0) { confirmations = 1; }
                tx.confirmations = confirmations;
            }
            const trans = pro._wrapTransaction(tx);
            bot.handle.onPending(trans, this.id);

        } catch(e){
            console.log('########### ws onpending error', e);
        }
    }

    async listen(reconnect = true){
        if(reconnect){
            Lazy.ins().logTs1(`${macro.COLOR.Green}#### (${this.id}) connecting to: ${this.url} ${this.onlyBroadcast ? "[onlyBroadcast]" : ""}${macro.COLOR.Off}`);
            this.provider = new ethers.providers.WebSocketProvider(this.url);
        }
        const p = this.provider as providers.WebSocketProvider;
        if(bot.mode == macro.BOT_MODE.LOCAL || bot.mode == macro.BOT_MODE.UPDATE) return;
        // websocket
        p._websocket.on('open', ()=>{
            Lazy.ins().logTs1(`${macro.COLOR.Green}#### (${this.id}) ${this.url} is open ${macro.COLOR.Off}`);
            this.reconnectTimeout = setTimeout(this.resetProvider.bind(this), 1000 * 15 * bot.config.blockTime); //10个区块没有收到block重启
            this.isOpen = true;
            
            if((bot.config.bot.active || bot.config.bot2.active || bot.config.pump.active)){
                setTimeout(()=>{ this.getTxPool() }, 3 * 1000);
            }

            setTimeout(() => { this.cacheReady = true }, 10 * 1000);
        });
        p._websocket.on('close', ()=>{
            Lazy.ins().logTs1(`${macro.COLOR.Red}#### (${this.id}) ${this.url} The websocket connection was closed${macro.COLOR.Off}`);
            this.isOpen = false;
            this.isReady = false;
            this.cacheReady = false;
            clearTimeout(this.reconnectTimeout);
            p.destroy().then(()=>{
                setTimeout(()=>{
                    this.listen();
                    bot.event.emit(macro.EVENT.WS_RECONNECT, this.id);
                }, this.reconnectTime * 1000)
            });
        });
        p._websocket.on('error', (e:any)=>{
            console.log("websocket.on error: ", e);
        });

        p._subscribe("newHeads", ["newHeads"], (result) => {
            //console.log("block : ", result.Number, "timestamp: ", result.Timestamp);
            bot.event.emit(macro.EVENT.WS_ON_BLOCK, result.Number);
            //console.log(blockNumber);
            //this.checkSpeed = true;
            this.blockNum = result.Number;

            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = setTimeout(this.resetProvider.bind(this), 1000 * 15 * bot.config.blockTime); //10个块没有收到block重启

            if(this.hasPending){
                this.lastPendingBlock = result.Number;
                this.hasPending = false;
            } else {
                const length = result.Number - this.lastPendingBlock;
                if(length > 600 && bot.config.pump.active){
                    Lazy.ins().logTs1(`${macro.COLOR.Red}#### (${this.id}) ${this.url} no pending over 60 block  #############${macro.COLOR.Off}`);
                    this.hasPending = true;
                    this.lastPendingBlock = result.Number;
                    this.resetProvider();
                }
            }
        });

        if(this.isListenLog){
            while(true){
                if(bot.oracleV2?.isReady){
                    this.subscribeLog();
                    break;
                }
                await tools.delay(3000);
            }
        }
    }

    async subscribeLog(){
        if(bot.config.disableSubscribeLog) return;
        //必须等oracle初始化后才有所有pair列表
        console.log(`subscribe log length: `, bot.oracleV2.data.pm.data.size);
        let filter = {
            address: bot.oracleV2.data.pm.data.keys(),
            topics : [
                //utils.id("Sync(uint112 reserve0, uint112 reserve1)")
                "0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1"
            ]
        };
        (this.provider as providers.WebSocketProvider)._subscribe("logs", ["logs", filter], (result) => {
            this.hasPending = true;
            //console.log(result);
            const {address, data, blockNumber, transactionHash} = result;
            const [r0,r1] = bot.abiCoder.decode(["uint112", "uint112"], data);
            bot.handle.onSync(address.toLowerCase(), r0, r1, transactionHash);
        });
    }

}