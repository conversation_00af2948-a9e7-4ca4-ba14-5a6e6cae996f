import { BigNumber, ethers } from "ethers";
import bot from "../bot";
import OracleV2Data from "./OracleV2Data";
import { Pair, PairExtend } from "./type/Pair";
import { macro } from "./macro";
import tools from "./tools";
import Lazy from "./lazy/LazyController";
import DataType from "./type/DataType";
import { ProviderWs } from "./provider/ProviderWs";
import HashPool from "./comp/HashPool";


export default class OracleV2 {
    data : OracleV2Data;
    
    //badPairs : string[] = [];

    isReady = false;

    constructor(){
        this.data = new OracleV2Data();
    }

    //第一次手动启动，方便后续逻辑
    async init(){
        console.log("[OracleV2] init");
        await this.data.init();
        this.isReady = true;
        console.log("[OracleV2] init done");
        /*
        bot.localLog.load(macro.FILE.LOG_BAD_PAIR).forEach(line=> {
            const paths = line[2];
            if(paths && !this.badPairs.includes(paths)) this.badPairs.push(paths);
        });
        */
    }

    public isActiveRouter(router:string){
        return bot.config.routers[router] ? true : false;
    }

    public async getAmountsOutByPath(router:string, amountIn : BigNumber, path:string[]){
        let amounts : number[] = [];
        //const {activeLps} = this.data;
        //const isActive = this.data.tokens.isActive(path[0]);
        //if(isActive){
        //先查看本地数据库是否可以计算
        amounts[0] = bot.token(path[0]).toNum(amountIn);
        for(let i = 0; i < path.length-1; i++){
            let lp = this.getPair(router, path[i], path[i+1]);
            if(lp){
                const [reserveIn, reserveOut, feeIn] = lp.getReserves(path[i]);
                amounts[i+1] = tools.getAmountOut(amounts[i], reserveIn, reserveOut, feeIn, lp.version == macro.PAIR_VERSION.V2_STABLE);
            } else {
                break;
            }
        }
        //}
        //Lazy.ins().logTs('[pending time] getAmountsOutByPath');
        //console.log("local amountOut: ", amounts, path.length);
        //需要动态获取
        if(amounts.length < path.length){
            Lazy.ins().log1(`${macro.COLOR.BBlack}   dynamic amount out ${macro.COLOR.Off}`);
            amounts = [];
            try {
                const res : BigNumber[] = await bot.routers.get(router).getAmountsOut(amountIn, path);
                for(let i = 0; i < path.length; i++){
                    amounts.push(bot.token(path[i]).toNum(res[i]));
                }
            } catch(e){
                Lazy.ins().log1("########################################");
                Lazy.ins().log1(`path: ${path.join(", ")}`);
                Lazy.ins().log1(`router: ${router}`);
                throw("########## getAmountsOutByPath error ###########");
            }

        }
        //console.log("server amountOut: ", amounts);
        return amounts;
    }

    public async getAmountsInByPath(router:string, amountOut : BigNumber, pathOrigin:string[]){
        let amounts : number[] = [];
        let path = [...pathOrigin].reverse();
        const isActive = this.data.pm.tokens.isActive(path[0]);
        if(isActive){
            const amountInNum = bot.token(path[0]).toNum(amountOut);
            //先查看本地数据库是否可以计算
            amounts[0] = amountInNum;
            for(let i = 0; i < path.length-1; i++){
                let lp = this.getPair(router, path[i], path[i+1]);
                //本地没有lp缓存
                if(lp){
                    const [reserveIn, reserveOut, feeIn] = lp.getReserves(path[i]);
                    amounts[i+1] = tools.getAmountOut(amounts[i], reserveIn, reserveOut, feeIn, lp.version == macro.PAIR_VERSION.V2_STABLE);
                } else {
                    break;
                }
            }
        }
        //console.log("local amountOut: ", amounts);
        //需要动态获取
        if(amounts.length < path.length){
            //Lazy.ins().log(`${macro.LINUX_COLOR.BBlack}   dynamic amount out ${macro.LINUX_COLOR.Off}`);
            amounts = [];
            try {
                const res : BigNumber[] = await bot.routers.get(router).getAmountsOut(amountOut, path);
                for(let i = 0; i < path.length; i++){
                    amounts.push(bot.token(path[i]).toNum(res[i]));
                }
                amounts.reverse();
                Lazy.ins().log1(`server amountIn: ${amounts.join(' -> ')}`);
                return amounts;
            } catch(e){
                Lazy.ins().log1("########################################");
                Lazy.ins().log1(`path: ${path.join(", ")}`);
                Lazy.ins().log1(`router: ${router}`);
                throw("########## getAmountsInByPath error ###########");
                //throw(e);
            }

        } else {
            const r = amounts.reverse();
            Lazy.ins().log1(`local amountIn: ${amounts.join(' -> ')}`)
            return r;
        }
    }

    public async getAmountsOutByPair(tokenIn:string, amountIn : BigNumber | number, pairs:string[]){
        const amountInNum = typeof(tokenIn) == "number" ? amountIn as number : bot.token(tokenIn).toNum(amountIn as BigNumber);
        
        //先查看本地数据库是否可以计算
        let amounts : number[] = this.getAmountsOutByPairLocal(tokenIn, amountInNum, pairs);

        if(amounts.length == pairs.length + 1){
            return amounts[amounts.length - 1]
        } else {
            //需要动态获取
            Lazy.ins().log1(` dynamic pair amount out: ${amounts.length}/${pairs.length}, ${pairs} `);
            try {
                /*
                const promises = [];
                for(let i = 0 ; i < bot.config.mainnet.wss.length; i++){
                    if(!bot.handle.wss[i].onlyBroadcast) {
                        promises.push(bot.client.bull.getOperator(undefined, i).ins()
                            .getAmountsOutByPairOnline(tokenIn, amountIn, DataType.pairToServerData(pairs)));
                    }
                }
                const [amounts, tokenOut] = await Promise.any(promises);
                */
                const [amounts, ,tokenOut] = await bot.client.bull.getOperator().ins().getAmountsOutByPairOnline(tokenIn, amountIn, DataType.pairToServerData(pairs, pairs.map( _=> 0)));
                return bot.token(tokenOut).toNum(amounts[amounts.length-1])
            } catch(e){
                Lazy.ins().log1("########## getAmountsOutByPairOnline error ###########");
                throw(e);
            }
        }
    }

    public getAmountsOutByPairLocal(tokenIn:string, amountIn : number, pairs:string[]){
        let amounts : number[] = [];
        amounts[0] = amountIn;
        let _tokenIn = tokenIn;
        let _tokenOut = "";
        for(let i = 0; i < pairs.length; i++){
            const lp = this.getPairByAddr(pairs[i]);
            if(lp){
                if(i > 0) _tokenIn = _tokenOut;
                _tokenOut = _tokenIn == lp.token0 ? lp.token1 : lp.token0;
                const [reserveIn, reserveOut, feeIn] = lp.getReserves(_tokenIn);
                amounts[i+1] = tools.getAmountOut(amounts[i], reserveIn, reserveOut, feeIn, lp.version == macro.PAIR_VERSION.V2_STABLE);
            } else {
                //本地没有lp缓存
                break;
            }
        }
        return amounts;
    }

    public getPair(router:string, token0:string, token1:string){
        return this.data.pm.get(this.getPairAddr(router, token0, token1));
    }

    public getPairByAddr(addr:string){
        if(this.data.pm.has(addr)){
            return this.data.pm.get(addr) as PairExtend;
        }
        //TODO: 获取失败，尝试动态获取这个pair

        throw(`[oracleV2] getPairByAddr error: ${addr}`);
    }

    /** 性能差, 测试用 */
    public async getPairByAddrFromCache(_addrs:string[]){
        const addrs = _addrs.map(a => {return a.toLocaleLowerCase()});
        let pairs : {[addr:string]:Pair} = {};
        addrs.forEach(addr=>{
            for(const f of Object.values(this.data.cache.factorys)){
                for(const p of f.pairs){
                    if(p.address == addr){
                        pairs[p.address] = p;
                    }
                }
            }
        });
        //检查是否有遗漏
        const addrOnCache = Object.keys(pairs);
        addrs.forEach(async (addr) => {
            if(bot.oracleV2.data.pm.has(addr)){
                console.log("--- activeLps ----");
                console.log(bot.oracleV2.data.pm.get(addr));
            } else if(!addrOnCache.includes(addr)){
                //动态获取
                console.log("--- online TODO: ----");
            } else {
                console.log("--- cache ----");
                console.log(pairs[addr]);
            }
        });

        return pairs;
    }

    /** 获取lp的地址，支持长度2 */
    getPairAddr(router:string, token0:string, token1:string){
        //console.log(this.data.matrix[token0][token1]);
        return this.data.pm.matrix[token0]?.[token1]?.[router] ?? macro.zeroAddress;
    }

    /** 根据权重分割amountIn */
    getSwapRouters(token:string, stableAmountIn:BigNumber, maxLp = 4){
        const bToken = bot.token(token);
        /*
        const __routers = Object.values(bToken.routers).sort((a,b)=>{
            return (b.tokenWeight - a.tokenWeight)
        }).slice(0, maxLp); //取前N个价值最高的lp
        */
       //whiteList只参与pump
        const paths = bToken.paths.slice(0, maxLp);


        let _routers  : string[]   = [];
        let _paths    : string[][] = [];
        let _pairs    : string[][] = [];
        let _amounsIn : Array<BigNumber> = [];

        if(paths.length == 0){
            let msg = `[getSwapRouters] error path for token: ${token}`;
            throw (new Error (msg));
        } else if(paths.length == 1){
            let first = paths[0];
            _paths.push([...first.path]); //copy避免数据污染
            _pairs.push([...first.pairs]); //copy避免数据污染
            _routers.push(first.router);
            _amounsIn.push(stableAmountIn);
        } else {
            let lpSum = bToken.sum.token;
            //const lpSumBn = BigNumber.from((lpSum * 10000).toFixed()).div(BigNumber.from('10000'));
            const lpSumBn = BigNumber.from((lpSum * 100000).toFixed()).div(BigNumber.from('100000'));

            let tmp = BigNumber.from('0');
            for(let i = paths.length-1 ; i >= 0; i--){ //从小的开始计算
                const router = paths[i];
                if(router.tokenWeight/lpSum < 0.0006) continue; //小于总量0.06%的lp不计算

                try {
                    if(i > 0){
                        let part = stableAmountIn.mul(BigNumber.from(router.tokenWeight.toFixed())).div(lpSumBn);
                        if(part.lte(macro.bn.zero)) continue;
                        tmp = tmp.add(part);
                        _amounsIn.unshift(part);
                    } else {
                        const left = stableAmountIn.sub(tmp);
                        if(left.lte(macro.bn.zero)) continue;
                        _amounsIn.unshift(left);
                    }
                } catch(e){
                    //console.log(`[getSwapRoutersV2] (${token}) error amountsIn should greater then zero.`);
                    continue;
                }
                
                _routers.unshift(router.router);
                _paths.unshift([...router.path]);
                _pairs.unshift([...router.pairs]);
            }
        }
        return {
            routers : _routers,
            paths : _paths,
            pairs: _pairs,
            amountsIn : _amounsIn,
            inSum : stableAmountIn
        }
    }

    private autoAddPairs : HashPool = new HashPool(10);
    async autoUpdatePair(routerAddr:string, token0:string, token1:string){
        //需要更新activeLps, token
        const {pm} = this.data;
        const router = bot.routers.get(routerAddr);
        const pairAddr = await router.getPair(token0, token1);
        if(pairAddr == macro.zeroAddress) return;

        console.log(`${macro.COLOR.BYellow}[autoUpdatePair] : factory:${router.factoryAddr}, pair:${pairAddr}${macro.COLOR.Off}`);
        //已经订阅了，但是可能不同router
        const activeP = pm.get(pairAddr) as PairExtend;
        if(activeP){
            pm.matrix[token0][token1][routerAddr] ??= pairAddr;
            pm.matrix[token1][token0][routerAddr] ??= pairAddr;
            if(!activeP.routers.includes(routerAddr)) activeP.routers.push(routerAddr);
            return;
        }
        //添加到缓存
        if(!this.autoAddPairs.enable(pairAddr)) return;

        //加入activeLps
        const pair = Object.assign(new PairExtend(), await router.getPairInfo(pairAddr));
        pair.routers = [routerAddr];
        pair.factory = router.factoryAddr;
        pair.updateFee();
        pm.setSafe(pair, true);
        
        /*const info = await this.checkPair(routerAddr, pairAddr, true);
        pair.f0 = info.f0;
        pair.f1 = info.f1;
        pair.status = info.status;*/
        const fee = await this.batchCheckPairFee([pair]);
        pair.f0 = fee[0][0];
        pair.f1 = fee[0][1];
        pair.status = (pair.f0 >= 20000 || pair.f1 >= 20000) ? macro.PAIR_STATUS.DISABLE : macro.PAIR_STATUS.ACTIVE;
        pair.id = -1;
        pair.updateFee();
    
        //添加到PairIndex
        bot.oracleV2.data.pm.index.addPair(pair, true);

        if(pair.status == macro.PAIR_STATUS.ACTIVE){
            console.log(`${macro.COLOR.Green}(${pair.id}) ${pair.address} ${pair.token0Symbol}-${pair.token1Symbol} fee0:${pair.f0} fee1:${pair.f1} ${macro.COLOR.Off}`);

            const mevPaths = this.data.getMevPath(pairAddr);
            if(mevPaths.length > 0){
                let res = await bot.contractIns.batchCheckMev(mevPaths);
                for(let i = 0; i < mevPaths.length; i++){
                    if(pair.version == macro.PAIR_VERSION.V2_EOA){
                        const length = mevPaths[i].pairs.length;
                        mevPaths[i].fees0 = new Array(length).fill(0);
                        mevPaths[i].fees1 = new Array(length).fill(0);
                        mevPaths[i].status0 = macro.MEV_STATUS.ACTIVE;
                        mevPaths[i].status1 = macro.MEV_STATUS.ACTIVE;
                        mevPaths[i].gas = 500000;
                    } else {
                        mevPaths[i].fees0 = res[i].fee0;
                        mevPaths[i].fees1 = res[i].fee1;
                        mevPaths[i].gas = res[i].gas;
                        mevPaths[i].status0 = mevPaths[i].fees0.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
                        mevPaths[i].status0 = mevPaths[i].fees1.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
                    }
                }
                pair.mevPath = mevPaths;
                bot.client.pump.mevLiquidity(pair, pair.token0, pair.token1, pair.reserve0, pair.reserve1);

                //统计所有相关联的pair
                const updateList : string[] = [];
                pair.mevPath.forEach(p => p.pairs.forEach(_p=> {
                    if(!updateList.includes(_p)) updateList.push(_p);
                }));
                for(let i = 0; i < updateList.length; i++){
                    const latestPair = pm.get(updateList[i]);
                    if(!latestPair || updateList[i] == pair.address) continue;
                    console.log(`testing (${i}/${updateList.length}): ${updateList[i]}`);
                    let subMevPaths = this.data.getMevPath(updateList[i]);
                    let resSub = await bot.contractIns.batchCheckMev(subMevPaths);
                    for(let i = 0; i < subMevPaths.length; i++){
                        if(pair.version == macro.PAIR_VERSION.V2_EOA){
                            const length = subMevPaths[i].pairs.length;
                            subMevPaths[i].fees0 = new Array(length).fill(0);
                            subMevPaths[i].fees1 = new Array(length).fill(0);
                            subMevPaths[i].status0 = macro.MEV_STATUS.ACTIVE;
                            subMevPaths[i].status1 = macro.MEV_STATUS.ACTIVE;
                            subMevPaths[i].gas = 500000;
                        } else {
                            subMevPaths[i].fees0 = resSub[i].fee0;
                            subMevPaths[i].fees1 = resSub[i].fee1;
                            subMevPaths[i].gas = resSub[i].gas;
                            subMevPaths[i].status0 = subMevPaths[i].fees0.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
                            subMevPaths[i].status0 = subMevPaths[i].fees1.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
                        }
                    }
                    latestPair.mevPath = subMevPaths;
                }
                bot.handle.wss[0].subscribeLog(); //重新订阅
            }
            //更新到oracle.data.cache
            bot.oracleV2.data.addPairToCache(router.factoryAddr, pair.cloneIntoPair());
        } else {
            console.log(`${macro.COLOR.Red}(${pair.id}) ${pair.address} ${pair.token0Symbol}-${pair.token1Symbol} fee0:${pair.f0} fee1:${pair.f1} ${macro.COLOR.Off}`);
        }
    }

    async batchCheckPairFee(pairs : Pair[]){
        const {pm} = this.data;
        const stables = bot.config.stableTokens;
        const batchData : Array<{pair:Pair, tokenIn:string, prePairs:Pair[]}> = [];

        for (const p of pairs) {
            const d : {pair:Pair, tokenIn:string, prePairs:Pair[]} = {pair:p, tokenIn:"", prePairs:[]};
            if(stables.includes(p.token0)) {
                d.tokenIn = p.token0;
            } else if(stables.includes(p.token1)){ 
                d.tokenIn = p.token1;
            }
            if(d.tokenIn != ""){
                batchData.push(d);
                continue;
            }
            //if(p.address == "0xd0a96414b6f60c022c0746fe8211296cb1eb5175")  console.log(p);
            for(let s of stables){
                //找出stable -> token0的pair
                for(let i = 0 ; i < 2 ; i++){
                    const bridgeToken = i==0 ? p.token0 : p.token1;
                    if(pm.matrix[s][bridgeToken]){
                        const prePair = Object.values(pm.matrix[s][bridgeToken]).find( x => {
                            const _p = pm.get(x);
                            return _p.status == macro.PAIR_STATUS.ACTIVE && _p.version !== macro.PAIR_VERSION.V2_EOA;
                        });
                        if(prePair){
                            d.tokenIn = s;
                            d.prePairs = [pm.get(prePair)];
                            break;
                        }
                    }
                }
                if(d.tokenIn != ""){
                    batchData.push(d);
                    break;
                }
            }
            //if(p.address == "0xd0a96414b6f60c022c0746fe8211296cb1eb5175")  console.log(d);
        }
        let onChainfees = await bot.contractIns.batchCheckPairFee(batchData);
        const fees = [];
        for(let i = 0 ; i < pairs.length; i++){
            if(pairs[i].version == macro.PAIR_VERSION.V2_EOA){
                fees.push([0,0]);
            } else {
                let index = batchData.findIndex(_b => _b.pair == pairs[i]);
                if(index >=0){
                    fees.push(onChainfees[index]);
                } else {
                    fees.push([99999, 99999]); //没有stable-token的pair，不进行交易
                }
            }
        }
        return fees;
    }
    
    //不支持eoaRouter
    /*
    async checkPair(routerAddr:string, addr:string, debug = false){
        const {pm} = this.data;
        const router = bot.routers.get(routerAddr);
        const p = await router.getPairInfo(addr);
        if(p.version == macro.PAIR_VERSION.V2_EOA){
            p.status = macro.PAIR_STATUS.ACTIVE;
            return p;
        }
        const stables = bot.config.stableTokens;
        let stable = "";
        let prePaths : string[] = [];

        const fees = [-1, -1];
        for(let i = 0 ; i < 2; i++){
            const token = i==0? p.token0 : p.token1;
            const tokenOther = i==0? p.token1 : p.token0;
            if(stables.includes(token)){
                fees[i]=0; //如果是稳定币基本是0fee
                continue;
            }
            if(stables.includes(tokenOther)){
                stable = tokenOther;
                prePaths = [addr];
            } else {
                for(let s of stables){
                    if(pm.matrix[s][token]){
                        //找出stable -> tokenA的pair
                        const prePair = Object.values(pm.matrix[s][token]).find( x => {
                            const _p = pm.get(x);
                            return _p.status == macro.PAIR_STATUS.ACTIVE && _p.version !== macro.PAIR_VERSION.V2_EOA;
                        });
                        if(prePair){
                            stable = s;
                            prePaths = [prePair];
                            break;
                        }
                    }
                }
            }

            //没有stable -> tokenA的pair，找出最大的stable -> tokenB -> tokenA
            if(stable == ""){
                let top = this.data.pm.index.getTopPairsOfToken(token, [], false); //不要找需要eoa的pair
                if(top && top.length > 0){
                    //找出stable -> tokenB
                    const tokenB = top[0].token0 == token ? top[0].token1 : top[0].token0;
                    for(let s of stables){
                        if(pm.matrix[s][tokenB]){
                            const prePair = Object.values(pm.matrix[s][tokenB]).find( x => {
                                const _p = pm.get(x);
                                return _p.status == macro.PAIR_STATUS.ACTIVE && _p.version !== macro.PAIR_VERSION.V2_EOA;
                            });
                            if(prePair){
                                stable = s;
                                prePaths = [prePair, top[0].address];
                                break;
                            }
                        }
                    }
                }
            }

            if(stable!==""){
                if(debug) console.log(`[checkPair] testing: stable:${bot.token(stable).symbol} prePair:${prePaths.join(',')}`);
                //查fee
                fees[i] = await this.data.checkPairInFeeRange(addr, stable, prePaths, debug);
            }
        }
        
        p.status = fees.includes(-1) ? macro.PAIR_STATUS.DISABLE : macro.PAIR_STATUS.ACTIVE;


        const config = bot.config.whiteListPair[p.address];
        if(config){
            p.status = macro.PAIR_STATUS.ACTIVE;
            p.f0 = config.fee0;
            p.f1 = config.fee1;
        } else  if(p.status == macro.PAIR_STATUS.ACTIVE){       
            p.f0 = fees[0];
            p.f1 = fees[1];
        }
        if(debug){
            console.log(p);
            console.log(fees);
        }
        return p;
    }
    */

}