import { ethers } from "ethers";
import { macro } from "../macro";
import fs from 'fs';
import tools from "../tools";

export default class RouterSniffer {
    private cache : {[addr:string]:string} = {};
    private keywords : string[] = [];
    private file = "";
    private provider : ethers.providers.JsonRpcProvider;

    constructor(path : string, server: ethers.providers.JsonRpcProvider){
        this.keywords = Object.keys(macro.uniswapV2FuncMap);
        this.provider = server;
        this.file = path;
        try {
            let local = fs.readFileSync(this.file, "utf-8");
            local.split('\n').forEach( (x)=>{
                const [addr,desc] =  x.split(',');
                if(addr) this.cache[addr] = desc || "";
            });
        } catch(e){}
    }

    async check(to:string, data:string){
        if(this.cache[to]) return; //已经记录了
        const funcHash = data.slice(2,10);
        if(this.keywords.includes(funcHash)){
            //检查是否有factory
            let checkFactory = await this.provider.call({ to : to, data:"0xc45a0155"}).then((f)=>{
                if(f == "0x") return;
                if(this.cache[to]) return; //已经记录了, 异步的时候可能会多次触发
                //未记录的router
                console.log(` ${macro.COLOR.BRed} new router ${to}${macro.COLOR.Off}`);
                this.cache[to] = tools.now(false).str;
                let saveStr = [];
                for(let key of Object.keys(this.cache)){
                    saveStr.push(`${key},${this.cache[key]}`);
                }
                tools.writeFileSync(this.file, saveStr.join('\n'));
            }).catch((e)=>{
                //console.log(e);
            });
        }
    }
}