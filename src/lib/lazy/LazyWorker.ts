import { ethers } from "ethers";
import { Worker, isMainThread, workerData, parentPort }  from "node:worker_threads";
import { macro } from "../macro";
import tools from "../tools";
import LazyLogFailTx from "./LazyLogFailTx";
import RouterSniffer from "./LazyRouterSniffer";
import axios from "axios";

class LogDataV1 {
    version = 1;

    chain = "";
    file = "";
    hash = "";

    str = "";
}

class LogNewHeadData {
    chain = "";
    blockNum = "";
    ts = 0;
}

class LogFirstTxData {
    chain = "";
    blockNum = "";
    firstHash = "";
    ts = 0;
}

class LogRewards {
    chain = "";
    swapTx = "";
    tx = "";
    reward : {[token:string]:{expect:number, claim:number}} = {};
    win = false;
    type = 0; //0:pump 1:pump残渣 2:trash

    constructor(chain:string, swapTx:string, tx:string){
        this.chain = chain;
        this.swapTx = swapTx;
        this.tx = tx;
    }
}


if(!isMainThread){
    const provider = new ethers.providers.JsonRpcProvider(workerData.server);
    const routerSniffer = new RouterSniffer(workerData.path_unknow, provider);
    const managerFail = new LazyLogFailTx(provider); 

    function parseMsg(msg:string){
        const raw = msg.split("||");
        const funcName = raw[0];
        const args = raw.slice(1, raw.length);
        switch (funcName) {
            case  "log" : log(args[0]); break;
            case  "logTs" : logTs(args[0]); break;
            case  "logWsTs" : logWsTs(args[0], args[1]); break;
            case  "logPending" : logPending(args[0], args[1]); break;
            case  "logFailTx" : logFailTx(args[0]); break;
            //case  "logNewHead" : logNewHead(args[0]); break;
            case  "logFirstTx" : logFirstTx(args[0], args[1]); break;
            case  "logRemote" : logRemote(args[0], args[1], args[2]); break;
    
            case  "testPostMessageSpeed" : testPostMessageSpeed(); break;
        }
    }

    function testPostMessageSpeed(){
        parentPort?.postMessage("msg back");
    }

    function log(str : string){
        console.log(`   ${str}`);
    }

    function logPending(to : string, data:string){
        //console.log(to, " " , workerData);
        routerSniffer.check(to, data);
    }

    function logTs(str : string){
        console.log(`   ${macro.COLOR.BBlack}${tools.now().str}${macro.COLOR.Off} ${str}`);
    }

    function logWsTs(websocketId : string, str : string){
        console.log(`${macro.COLOR.BBlack}${websocketId}) ${tools.now().str}${macro.COLOR.Off} ${str}`);
    }

    function logFailTx(hash:string){
        managerFail.onFail(hash);
    }
    /*
    function logNewHead(blockNum:string){
        const ts = Number(new Date());
        //console.log(`logNewHead : ${blockNum}, ts:${ts}`);
        const {PORT, NEWHEAD_PATH, HOST, KEY} = macro.LOG_SERVER_CONFIG;
        const json : LogNewHeadData = {
            chain : workerData.chain,
            blockNum : blockNum,
            ts : ts
        };
        //console.log(JSON.stringify(json));
        axios.post(`http://${HOST}:${PORT}${NEWHEAD_PATH}`, { data: tools.encode(JSON.stringify(json), KEY) })
            .then(res => {})
            .catch(err => console.log(`${macro.COLOR.Red}log error${macro.COLOR.Off}`));
    }
    */
    function logFirstTx(blockNum:string, firstTx:string){
        const ts = Number(new Date());
        //console.log(`logNewHead : ${blockNum}, ts:${ts}`);
        const {PORT, FIRST_TX_PATH, HOST, KEY} = macro.LOG_SERVER_CONFIG;
        const json : LogFirstTxData = {
            chain : workerData.chain,
            blockNum : blockNum,
            firstHash : firstTx,
            ts :ts
        };
        //console.log("logFirstTx: ", JSON.stringify(json));
        axios.post(`http://${HOST}:${PORT}${FIRST_TX_PATH}`, { data: tools.encode(JSON.stringify(json), KEY) })
            .then(res => {})
            .catch(err => console.log(`${macro.COLOR.Red}log error${macro.COLOR.Off}`));
    }

    function logRemote(file:string, hash:string, str:string){
        const {PORT, PATH, HOST, KEY} = macro.LOG_SERVER_CONFIG;
        const json : LogDataV1 = {
            version: 1,
            chain : workerData.chain,
            file : file,
            hash : hash,
            str : str,
            //block : 0
            //data : 0
        };
        //console.log(JSON.stringify(json));
        axios.post(`http://${HOST}:${PORT}${PATH}`, { data: tools.encode(JSON.stringify(json), KEY) })
            .then(res => {})
            .catch(err => console.log(`${macro.COLOR.Red}log error${macro.COLOR.Off}`));
    }

    /*
    const _rewardDatas : {[whaleHash:string]: LogRewards} = {};
    function logExpectRewards(whaleHash:string, token:string, num:number){
        if(!_rewardDatas[whaleHash]){
            _rewardDatas[whaleHash] = new LogRewards(whaleHash);
            setTimeout(() => { reportRewards(whaleHash);}, 30 * 1000); 
        }
        const d = _rewardDatas[whaleHash];
        d.expect[token] ??= 0;
        d.expect[token] += num;
    }

    function logRewards(whaleHash:string, token:string, num:number, isWin=false){
        if(!_rewardDatas[whaleHash]){
            _rewardDatas[whaleHash] = new LogRewards(whaleHash);
            setTimeout(() => { reportRewards(whaleHash);}, 30 * 1000); 
        }
        const d = _rewardDatas[whaleHash];
        d.claim[token] ??= 0;
        d.claim[token] += num;

        if(isWin) d.win = true;
    }


    function reportRewards(hash:string){
        let data = _rewardDatas[hash];
        if(!data) return;
        const {PORT, PATH, HOST, KEY, REWARDS_PATH: REWARD_PATH} = macro.LOG_SERVER_CONFIG;
        axios.post(`http://${HOST}:${PORT}${REWARD_PATH}`, { data: tools.encode(JSON.stringify(data), KEY) })
            .then(res => {
                delete _rewardDatas[hash];
            })
            .catch(err => {
                delete _rewardDatas[hash];
                console.log(`${macro.COLOR.Red}log error${macro.COLOR.Off}`);
            });
    }
    */


    parentPort?.on('message', (msg : any)=>{
        parseMsg(msg);
    });
}