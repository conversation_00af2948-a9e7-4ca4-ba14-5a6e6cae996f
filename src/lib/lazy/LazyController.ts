import { Worker } from "node:worker_threads";
import bot from "../../bot";
import { macro } from "../macro";
import tools from "../tools";

export default class Lazy {
    private static instance : Lazy;
    private logLevel = 0;
    worker : Worker;

    private constructor(){
        this.logLevel = bot.logLevel;
        //this.worker = new Worker(__filename.replace('Controller.js', 'Worker.ts'), {workerData: "lazyWorker"});
        const file = `./LazyWorker.ts`;
        this.worker = new Worker(require.resolve(file), {
            execArgv: /\.ts$/.test(file) ? ["--require", "ts-node/register"] : undefined,
            workerData: {
                server: bot.config.mainnet.data,
                chain : bot.chain,
                path_unknow : bot.getLogPath(macro.FILE.UNKNOW_ROUTER),
            },
          });
        
        this.worker.on('error', (e)=>{
            console.log("worker error: ", e);
        });
        this.worker.on('exit', (code)=>{
            console.log("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
            console.log(`${macro.COLOR.BRed} !!!!! Lazy Worker stopped with exit code ${code} ${macro.COLOR.Off}`);
        });
    }
    public static ins(){
        if(!this.instance){
            this.instance = new Lazy();
        }
        return this.instance;
    }

    log(str : string){
        //必定输出
        console.log(`   ${str}`);
        //this.worker.postMessage("log|" + str);
    }

    logTs(str : string){
        console.log(`   ${macro.COLOR.BBlack}${tools.printTime()}${macro.COLOR.Off} ${str}`);
        //this.worker.postMessage("logTs|" + str);
    }

    logWsTs(websocketId:number, str:string){
        console.log(`${macro.COLOR.BBlack}${websocketId}) ${tools.printTime()}${macro.COLOR.Off} ${str}`);
        //this.worker.postMessage("logWsTs|" + websocketId + "|" + str);
    }

    log1(str : string){
        if(this.logLevel < 1) return;
        console.log(`   ${str}`);
        //this.worker.postMessage("log|" + str);
    }

    logTs1(str : string){
        if(this.logLevel < 1) return;
        console.log(`   ${macro.COLOR.BBlack}${tools.printTime()}${macro.COLOR.Off} ${str}`);
        //this.worker.postMessage("logTs|" + str);
    }

    logWsTs1(websocketId:number, str:string){
        if(this.logLevel < 1) return;
        console.log(`${macro.COLOR.BBlack}${websocketId}) ${tools.printTime()}${macro.COLOR.Off} ${str}`);
        //this.worker.postMessage("logWsTs|" + websocketId + "|" + str);
    }

    logFailTx(hash:string){
        this.worker.postMessage("logFailTx||" + hash);
    }

    logPending(to="", data=""){
        this.worker.postMessage("logPending||" + to + "||" + data);
    }

    logRemote(file:string, hash:string, str:string, block=0, data=""){
        this.worker.postMessage(`logRemote||${file}||${hash}||${str}||${block}||${data}`);
    }

    /*
    logNewHead(blockNum:number){
        this.worker.postMessage("logNewHead||" + blockNum);
    }
    */

    logFirstTx(blockNum:number, hash:string){
        this.worker.postMessage("logFirstTx||" + blockNum + "||" + hash);
    }
    
    testPostMessageSpeed(){
        console.log("thread_worker")
        console.time('time');
        this.worker.postMessage("testPostMessageSpeed|"+"asdf");
        this.worker.once('message', (x)=>{
            console.timeEnd('time');
        });
    }


}