import { ethers, utils, BigNumber } from 'ethers';
import fs from 'fs';
import path from 'path';
import { Token } from './type/Token';

export default class tools {
    /**
     * static now
     */
    static now(short=true) {
        const date = new Date();
        const str = date.toLocaleString('en-US', { timeZone : "Asia/Shanghai", hour12: false, fractionalSecondDigits: 3,hour: 'numeric',minute: 'numeric',second: 'numeric',month: 'numeric',day: 'numeric',});
        return {
            str : short ? str.slice(str.length-11) : str,
            tsMilli : date.getTime()
        };
    }

    static printTime(){
        const date = new Date();
        return `${date.getHours().toString().padStart(2,' ')}:${date.getMinutes().toString().padStart(2,'0')}:${date.getSeconds().toString().padStart(2,'0')}.${date.getMilliseconds().toString().padStart(3,'0')}`;
    }

    static valueOfStr(str:string){
        let sum = 0;
        for(let i = 0; i < str.length; i++){
            sum += str.charCodeAt(i);
        }
        return sum;
    }

    static min(num1:number, num2:number){
        return num1 > num2 ? num2 : num1;
    }

    static max(num1:number, num2:number){
        return num1 > num2 ? num1 : num2;
    }

    /** 随机数组中的一个 */
    static randomElem<T>(arr : Array<T>){
        return arr[~~(Math.random() * arr.length)];
    }

    /** include min and max */
    static randomInt(min : number, max : number){
        return ~~(Math.random() * (max - min + 1)) + min;
    }

    static randomFloat(min:number, max:number) {
        return Math.random() * (max - min) + min;
    }
    /** 如果小于rate返回true  0~1之间 */
    static randomBool(rate = 0.5) {
        return Math.random() <= rate;
    }

    static delay(milliseconds:number){
        if(milliseconds == 0) return; 
        return new Promise(resolve => {
            setTimeout(resolve, milliseconds);
        });
    }

    static getOutSlippage(_amountOut:number, _amountOutMin:number){
        return (_amountOut-_amountOutMin)/_amountOut;
    }

    static getInSlippage(amountIn:number, amountInMax:number){
        return (amountInMax-amountIn)/amountIn;
    }

    static getLowerCase(value: string): string {
        if (value) { return value.toLowerCase(); }
        return value;
    }

    static arrConcat(arrA:Array<any>, arrB:Array<any>){
        const newArr = [...arrA];
        arrB.forEach((x)=>{
            if(!newArr.includes(x)) newArr.push(x);
        });
        return newArr;
    }

    static cleanAbi(abi : any[]){
        abi.map((x)=> {
            delete x.constant;
            delete x.payable; //new
        });
        return abi;
    }

    static calcAmount(amountA:number, tokenA:Token, tokenB:Token){
        if(tokenA.address == tokenB.address){
            return amountA;
        } else {
            
        }
    }

    static formatGas(gas?:BigNumber){
        if(gas){
            return utils.formatUnits(gas, 'gwei');
        } else {
            return -1;
        }
    }

    static isLowerCase(str:string){
        return str == str.toLowerCase();
    }

    static priceImpactColor(impact:number){
        const color = COLOR;
        const str = (impact * 100).toFixed(2) + "%";
        //return `\x1b[32m${str}\x1b[0m`;
        if(impact < 0.003){
            return str;
        } else if(impact < 0.01) {
            return `${color.Green}${str}${color.Off}`; 
        } else if(impact < 0.02) {
            return `${color.BGreen}${str}${color.Off}`; 
        } else if (impact < 0.04){
            return `${color.Cyan}${str}${color.Off}`;
        } else if (impact < 0.07){
            return `${color.BCyan}${str}${color.Off}`;
        } else {
            return `${color.BIRed}${str}${color.Off}`; 
        }
    }

    static getDealMulti(priceImpact:number){
        let multi = 0;
        let title = "";
        if(priceImpact > 0.10){
            multi = 7.5;
            title = "CRAZY2";
        } else if(priceImpact > 0.04){
            multi = 7;
            title = "CRAZY";
        } else if(priceImpact >  0.027){
            multi = 6;
            title = "GREAT";
        } else if(priceImpact > 0.018){
            multi = 5;
            title = "SUPER";
        } else if(priceImpact > 0.012){
            multi = 3.5;
            title = "HUGE2";
        } else if(priceImpact > 0.0088){
            multi = 2;
            title = "HUGE";
        } else if(priceImpact > 0.0068){
            multi = 1.5;
            title = "LARGE";
        } else if(priceImpact > 0.005){
            multi = 1.25;
            title = "BIG2";
        } else if(priceImpact > 0.0030){
            multi = 1;
            title = "BIG";
        } else {
            multi = 0;
        }
        return {multi: multi, title: title}
    }

    static indexOfMax(arr:Array<number>) {
        if (arr.length === 0) {
            return -1;
        }
        let max = arr[0];
        let maxIndex = 0;
    
        for (let i = 1; i < arr.length; i++) {
            if (arr[i] > max) {
                maxIndex = i;
                max = arr[i];
            }
        }
        return maxIndex;
    }

    static writeFileSync(_path:string, content:string){
        fs.mkdirSync(path.dirname(_path), {recursive: true});
        fs.writeFileSync(_path, content);
    }

    static shuffleArray<T>(array: T[]): T[] {
        for (let i = array.length - 1; i > 0; i--) {
          // 随机选择一个不大于i的元素
          const j = Math.floor(Math.random() * (i + 1));
          // 交换它和第i个元素
          [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
      }

    static invSqrt(n:number) {
        const buf = new ArrayBuffer(4);
        const i = new Int32Array(buf);
        const y = new Float32Array(buf);
        y[0] = n;
        i[0] = 0x5f3759df - (i[0] >> 1);
        var r = y[0];
        return r * (1.5 - (n * 0.5 * r * r));
    }

    static quote(amountA:number, reserveA:number, reserveB:number){
        const amountB = amountA * reserveB / reserveA;
        return amountB;
    }

    static getAmountIn(amountOut:number, reserveIn:number, reserveOut:number, fee = 0.997){
        const numerator = reserveIn * amountOut;
        const denominator = (reserveOut - amountOut) * fee;
        const amountIn = (numerator / denominator) + 1;
        return amountIn;
    }

    static getAmountOut(amountIn : number, reserveIn:number, reserveOut:number, fee = 0.997, stable=false){
        if(!stable){
            const amountInWithFee = amountIn * fee;
            const amountOut = (amountInWithFee * reserveOut) / (reserveIn + amountInWithFee);
            return amountOut;
        } else {
            return v2Stable._getAmountOut(amountIn, reserveIn, reserveOut);
        }
    }

    static encode(str:string, key:string){
        const encoded = Buffer.from(this.xor(str, key), 'utf8').toString('base64');
        return encoded;
    }
    
    static decode(str:string, key:string){
        const plain = Buffer.from(str, 'base64').toString('utf8');
        return this.xor(plain, key);
    }
    
    static xor(str:string, key:string) {
        let result = "";
        for (let i = 0; i < str.length; i++) {
          result += String.fromCharCode(str.charCodeAt(i) ^ key.charCodeAt(i % key.length));
        }
        return result;
    }

    static getSelectorId(str:string){
        return "0x" + utils.id(str).slice(2,10);
    }
}

export class bMath {
    static min(one:BigNumber, two:BigNumber){
        return one.lt(two) ? one : two;
    }
    static max(one:BigNumber, two:BigNumber){
        return one.gt(two) ? one : two;
    }
}

export class v2Stable {
    static _f(x0:number, y:number) {
        return x0*(y*y*y)+(x0*x0*x0)*y;
    }

    static _d(x0:number, y:number) {
        return 3*x0*y*y+(x0*x0*x0);
    }

    static _get_y(x0:number, xy:number, y:number) {
        for (let i = 0; i < 255; i++) {
            const y_prev = y;
            const k = this._f(x0, y);
            if (k < xy) {
                const dy = (xy - k)/this._d(x0, y);
                y = y + dy;
            } else {
                const dy = (k - xy)/this._d(x0, y);
                y = y - dy;
            }
            if (y > y_prev) {
                if (y - y_prev <= 1) {
                    return y;
                }
            } else {
                if (y_prev - y <= 1) {
                    return y;
                }
            }
        }
        return y;
    }

    static _k(x:number, y:number) {
        return (x*x + y*y) * (x * y);
    }

    static _getAmountOut(amountIn: number, reserveIn:number, reserveOut:number) {
        const xy =  this._k(reserveIn, reserveOut);
        const y = reserveOut - this._get_y(amountIn+reserveIn, xy, reserveOut);
        return y;
    }

}

export class numCalc {
    static binarySearch(
        left : number, // Lower bound
        right : number, // Upper bound
        calculateF : (input:number) => number, // Generic calculate function
        passConditionF : (output:number) => boolean, // Condition checker
        tolerance = 0.01, // Tolerable delta (in %, in 18 dec, i.e. parseUnits('0.01') means left and right delta can be 1%),
        maxSteps = 30
    ) : number {
        const _tolerance = tolerance * ((right+left)/2);
        if (right - left > _tolerance && maxSteps > 0) {
            const mid = (right+left) / 2;
            const out = calculateF(mid);
        
            // If we pass the condition
            // Number go up
            if (passConditionF(out)) {
              return this.binarySearch(mid, right, calculateF, passConditionF, tolerance, maxSteps - 1);
            }
        
            // Number go down
            return this.binarySearch(left, mid, calculateF, passConditionF, tolerance, maxSteps - 1);
          }
        
          // No negatives
          const ret = (right + left) / 2;
          if (ret <= 0) return 0;
          return ret;
    }

    static goldenSectionSearch(
        left: number, // Lower bound
        right: number, // Upper bound
        calculateF: (input: number) => number, // Generic calculate function
        passConditionF: (output: number) => boolean, // Condition checker
        tolerance = 0.01, // Tolerable delta (in %, in 18 dec, i.e. parseUnits('0.01') means left and right delta can be 1%)
        maxSteps = 30
      ): number {
        //const phi = (1 + Math.sqrt(5)) / 2; // Golden ratio
        const phi = 1.618033988749895;
    
        let x1 = left + (right - left) / phi;
        let x2 = right - (right - left) / phi;
        let f1 = calculateF(x1);
        let f2 = calculateF(x2);
        let steps = 0;
    
        const _tolerance = tolerance * ((right+left)/2);
        while (right - left > _tolerance && steps < maxSteps) {
          if (passConditionF(f1)) {
            right = x2;
            x2 = x1;
            f2 = f1;
            x1 = left + (right - left) / phi;
            f1 = calculateF(x1);
          } else {
            left = x1;
            x1 = x2;
            f1 = f2;
            x2 = right - (right - left) / phi;
            f2 = calculateF(x2);
          }
          steps++;
        }
    
        const ret = (right + left) / 2;
        if (ret <= 0) return 0;
        return ret;
      }
}

enum COLOR {
    // Reset
    Off='\x1b[0m',       // Text Reset

    // Regular Colors
    Black='\x1b[0;30m',        // Black
    Red='\x1b[0;31m',          // Red
    Green='\x1b[0;32m',        // Green
    Yellow='\x1b[0;33m',       // Yellow
    Blue='\x1b[0;34m',         // Blue
    Purple='\x1b[0;35m',       // Purple
    Cyan='\x1b[0;36m',         // Cyan
    White='\x1b[0;37m',        // White

    // Bold
    BBlack='\x1b[1;30m',       // Black
    BRed='\x1b[1;31m',         // Red
    BGreen='\x1b[1;32m',       // Green
    BYellow='\x1b[1;33m',      // Yellow
    BBlue='\x1b[1;34m',        // Blue
    BPurple='\x1b[1;35m',      // Purple
    BCyan='\x1b[1;36m',        // Cyan
    BWhite='\x1b[1;37m',       // White

    // Underline
    UBlack='\x1b[4;30m',       // Black
    URed='\x1b[4;31m',         // Red
    UGreen='\x1b[4;32m',       // Green
    UYellow='\x1b[4;33m',      // Yellow
    UBlue='\x1b[4;34m',        // Blue
    UPurple='\x1b[4;35m',      // Purple
    UCyan='\x1b[4;36m',        // Cyan
    UWhite='\x1b[4;37m',       // White

    // Background
    On_Black='\x1b[40m',       // Black
    On_Red='\x1b[41m',         // Red
    On_Green='\x1b[42m',       // Green
    On_Yellow='\x1b[43m',      // Yellow
    On_Blue='\x1b[44m',        // Blue
    On_Purple='\x1b[45m',      // Purple
    On_Cyan='\x1b[46m',        // Cyan
    On_White='\x1b[47m',       // White

    // High Intensity
    IBlack='\x1b[0;90m',       // Black
    IRed='\x1b[0;91m',         // Red
    IGreen='\x1b[0;92m',       // Green
    IYellow='\x1b[0;93m',      // Yellow
    IBlue='\x1b[0;94m',        // Blue
    IPurple='\x1b[0;95m',      // Purple
    ICyan='\x1b[0;96m',        // Cyan
    IWhite='\x1b[0;97m',       // White

    // Bold High Intensity
    BIBlack='\x1b[1;90m',      // Black
    BIRed='\x1b[1;91m',        // Red
    BIGreen='\x1b[1;92m',      // Green
    BIYellow='\x1b[1;93m',     // Yellow
    BIBlue='\x1b[1;94m',       // Blue
    BIPurple='\x1b[1;95m',     // Purple
    BICyan='\x1b[1;96m',       // Cyan
    BIWhite='\x1b[1;97m',      // White

    // High Intensity backgrounds
    On_IBlack='\x1b[0;100m',   // Black
    On_IRed='\x1b[0;101m',     // Red
    On_IGreen='\x1b[0;102m',   // Green
    On_IYellow='\x1b[0;103m',  // Yellow
    On_IBlue='\x1b[0;104m',    // Blue
    On_IPurple='\x1b[0;105m',  // Purple
    On_ICyan='\x1b[0;106m',    // Cyan
    On_IWhite='\x1b[0;107m',   // White
}