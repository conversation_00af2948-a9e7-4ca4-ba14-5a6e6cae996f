import { ethers } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";

async function main(){
    bot.mode = macro.BOT_MODE.LOCAL;
    const arg = process.argv.splice(2);
    const host = arg[0];
    const hash = arg[1];

    console.log(`[test trace] connecting to ${host}, tracing tx:${hash}`);
    var provider = host.includes("http") ? new ethers.providers.JsonRpcProvider(host) : new ethers.providers.WebSocketProvider(host);
    var result = await provider.send('debug_traceTransaction', [ hash ]);
    console.log(result);
}

main();