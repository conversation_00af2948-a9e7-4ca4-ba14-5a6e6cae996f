import { BigNumber, ethers, utils } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";


let ifactory = new utils.Interface([
    "function getAmountOut(uint256 amountIn, uint256 reserveIn, uint256 reserveOut, address token0, address token1)",
    "function getAmountIn(uint256 amountOut, uint256 reserveIn, uint256 reserveOut, address token0, address token1)",
    "function getPair(address tokenA, address tokenB) external view returns (address pair)",
    "function allPairs(uint) external view returns (address pair)",
    "function allPairsLength() external view returns (uint)"
]);


const factoryCache : {[k:string]: number} = {};

async function main(){
    bot.mode = macro.BOT_MODE.LOCAL;
    const arg = process.argv.splice(2);
    await bot.init(arg[0] as macro.CHAIN);
    
    let amountIn = BigNumber.from(10**10);
    let reserveIn = BigNumber.from(10**10);
    let reserveOut = BigNumber.from(10**10);
    let one = BigNumber.from(100000);
    
    console.log("----- checking router fee and type ----------");
    for(const [k,v] of Object.entries(bot.config.routers)){
        const router = bot.routers.get(k);
        let pairLength = 0;
        let isV2Stable = false; //检查是否stableSwapV2
        let weth = "";
        //检查factory和第一个pair是否stable
        try {
            const f = await router.getFactory(); //检查是否有factory
            console.log(f);
            pairLength = await allPairsLength(f);
            const pair0 = await router.allPairs(0);
            try {
                const res = await bot.provider().call({to:pair0, data: utils.id("stable()")});
                console.log("isV2Stable: ", res);
                isV2Stable = true;
                await getFeeByFactory(f);
            } catch(e){
                //console.log("error pair: ", pair0);
                //console.log(e);
                //不是v2Stable
            }

            //检查eth是否在配置表里
            try {
                weth = await router.weth();
            } catch(e){
                
            }

        } catch(e){
            //假router
            console.log(e);
            console.log(`${v.name.padStart(10, " ")}: #### fake router`);
            continue;
        }


        try {
            let amountOut = await router.getAmountOut(amountIn, reserveIn, reserveOut);
            let fee = one.mul(amountOut).mul(reserveIn).div(amountIn.mul(reserveOut).sub(amountIn.mul(amountOut)));
            if(fee.lt(one)) fee = fee.add(BigNumber.from(1));
            //let result = utils.formatUnits(amountOut);
            
            let isModify = !fee.eq(BigNumber.from(99700));
            console.log(`${isModify ? macro.COLOR.Green : macro.COLOR.White}${v.name.padStart(10, " ")} pair:${pairLength} fee : ${fee.toString()}${macro.COLOR.Off}`);
        } catch(e){
            console.log(`${v.name.padStart(10, " ")} pair:${pairLength} fee : -`);
        }
        if(isV2Stable) console.log(`            ${macro.COLOR.BCyan}is V2StableRouter${macro.COLOR.Off}`);
        if(weth != bot.config.eth) console.log(`            ${macro.COLOR.BCyan}weth: ${weth}${macro.COLOR.Off}`);
    }
    console.log("----- end ------");
}

async function getAmountOutByFactory(addr:string, amountIn:BigNumber, reserveIn:BigNumber, reserveOut:BigNumber){
    let data = ifactory.encodeFunctionData("getAmountOut", [amountIn, reserveIn, reserveOut]);
    let amountOut = await bot.handle.wss[0].provider.call({
        to : addr,
        data : data
    });
    console.log(amountOut);
    return amountOut;
}

async function allPairsLength(addr:string){
    const f = new ethers.Contract(addr, macro.abi.factory, bot.provider());
    const lengthBn = await Promise.any([f.allPairsLength(), f.totalPairs()]) as BigNumber; //makiswap
    //console.log(lengthBn.toNumber());
    return lengthBn.toNumber();
}

async function getFeeByFactory(addr:string){
    const f = new ethers.Contract(addr, macro.abi.factory, bot.provider());
    try {
        const stableFee = await f.getFee(true);
        const volatileFee = await f.getFee(false);
        console.log(`${macro.COLOR.Cyan}stableFee: ${stableFee}, volatileFee: ${volatileFee}${macro.COLOR.Off}`);
    } catch(e){
        //console.log(e);
        console.log("[ERR] getFeeByFactory");
    }
}

main();