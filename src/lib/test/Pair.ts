import { BigNumber, ethers, utils } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";

async function main(){
    bot.mode = macro.BOT_MODE.LOCAL;
    //let pair = findPair("******************************************");
    //console.log(pair);
    testPair2();
    //testPairCanto();
}

async function testPair2(){
    await bot.init(macro.CHAIN.OEC);
    //****************************************** //babyokx-usdt //1%
    //****************************************** wokt-usdt //che
    //****************************************** wokt-usdt //ks
    //"******************************************" : {fee0:50, fee1:0}, //BABYOKX-WOKT //1%
    //"******************************************" : {fee0:50, fee1:0}, //BABYOKX-WOKT  //okc
    //"******************************************" : {fee0:50, fee1:0}, //BABYOKX-WOKT //0.3%
    //****************************************** usdt-okt //9978

    //let res = await bot.oracleV2.data.checkPair("******************************************", utils.parseEther('0.01'), ["******************************************", "******************************************"]);
    //console.log(res);
    //return;

    let raw = bot.client.iBot.encodeFunctionData("checkPairWithFee", ["******************************************", utils.parseEther('0.01'), 
    [
    {
        addr : "******************************************",
        fee0 : BigNumber.from(99200),
        fee1 : BigNumber.from(99700),
    },
    {
        addr : "******************************************",
        fee0 : BigNumber.from(99200),
        fee1 : BigNumber.from(99700),
    },
    ]]);
    console.log(raw);
    let p = await bot.provider().estimateGas({
        from : "******************************************",
        to : "******************************************",
        data : raw,
        gasLimit: 4000000
    });
    console.log(p);
    //console.log(p.toNumber());
}

async function testPairCanto(){
    await bot.init(macro.CHAIN.CANTO);
    let raw = bot.client.iBot.encodeFunctionData("checkPairWithFee", ["******************************************", utils.parseEther('0.01'), 
    [
    {
        addr : "******************************************",
        fee0 : BigNumber.from(100000),
        fee1 : BigNumber.from(100000),
    }
    ]]);
    console.log(raw);
    let p = await bot.provider().estimateGas({
        from : "******************************************",
        to : "******************************************",
        data : raw,
        gasLimit: 4000000
    });
    console.log(p);
    //console.log(p.toNumber());
}

async function testPair(){
    //****************************************** babyokx-usdt
    //****************************************** wokt-usdt
    //****************************************** wokt-usdt
    let raw = bot.client.iBot.encodeFunctionData("checkPair", ["******************************************", utils.parseEther('0.01'), 
    [
        "******************************************", "******************************************",
    ]]);
    console.log(raw);
    let p = await bot.provider().estimateGas({
        from : "******************************************",
        to : "******************************************",
        data : raw
    });
    console.log(p);
    //console.log(p.toNumber());
}

function findPair(address:string){
    for(const [k, v] of Object.entries(bot.oracleV2.data.cache.factorys)){
        for(const p of v.pairs){
            if(p.address.toLowerCase() == address.toLowerCase()){
                return p;
            }
        }
    }
}

main();