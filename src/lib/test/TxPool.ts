import { ethers } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";

let provider : ethers.providers.JsonRpcProvider;

async function main(){
    bot.mode = macro.BOT_MODE.LOCAL;
    const arg = process.argv.splice(2);
    const host = arg[0];
    
    provider = host.includes("http") ? new ethers.providers.JsonRpcProvider(host) : new ethers.providers.WebSocketProvider(host);
    console.log(`[test txpool] connecting to ${host}`);

    let result = await provider.send("txpool_content", []);
    //var result = await provider.send('debug_traceTransaction', [ hash ]);
    console.log("[test txpool]" , JSON.stringify(result));
    console.log("--------------------------");
    _formartTxPool(result);
}

function _formartTxPool(txObj : any){
    if(!txObj) return;
    for( const [_type, obj] of Object.entries(txObj)){
        Object.keys(obj as {[addr:string]:any}).forEach((addr:any)=>{
            const p = (obj as {[addr:string]:any})[addr] as {[nonce:string]:any};
            Object.keys(p).forEach((nonce)=>{
                //const {to, hash} = p[nonce];
                //const pro = this.provider as ethers.providers.Web3Provider;
                let tx = provider.formatter.transactionResponse(p[nonce]);
                const trans = provider._wrapTransaction(tx);
                console.log(`(${_type}) [${trans.nonce}] ${trans.from} -> ${trans.to}`);
            });
        });
    }
}

main();