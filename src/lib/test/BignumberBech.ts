import { sign } from "crypto";
import Lazy from "../lazy/LazyController";
import SignController from "../prefabs/SignController";

function main(){

    setTimeout(()=>{
        let begin = new Date().getTime();
        for(let i = 0 ; i < 100000; i++){
            let a = 1000.283 * 200.234879;
            let b = a - 128.32423;
            let c = b /123;
        }
        console.log(`number spd: ${new Date().getTime() - begin} ms`);
    }, 2000);

    setTimeout(()=>{
        let begin = new Date().getTime();
        for(let i = 0 ; i < 100000; i++){
            let a = 1000283n * 200234n;
            let b = a - 128324n;
            let c = b / 123n;
        }
        console.log(`bigInt spd: ${new Date().getTime() - begin} ms`);
    }, 1000);
}
//main();

function testThreadSpeed(){
    setInterval(() => {
        Lazy.ins().testPostMessageSpeed();
    }, 1000);
}
testThreadSpeed();

function testChildSpeed(){
    const signer = new SignController();
    signer.testChild();
}
//testChildSpeed();