import { ethers, UnsignedTransaction, utils } from "ethers";
import WasmSign from "../comp/WasmSign";
import {keccak, createK<PERSON>cak} from "hash-wasm";
//import { instantiateSecp256k1 } from '@bitauth/libauth';
import { serialize } from "@ethersproject/transactions";
import { keccak256 } from "@ethersproject/keccak256";
import { arrayify, BytesLike, hexlify, splitSignature } from "@ethersproject/bytes";
import tools from "../tools";
import bot from "../../bot";
//import SignController from "../prefabs/SignController";


//chrome://inspect/#devices

function main(){
    for(let i = 0 ; i < 16; i++){
        let beginTime = Number(new Date());
        let speed = Number(new Date()) - beginTime;
        console.log(`[${i}] rust wasm sign : ${speed} ms`);
    }
}

async function testSign(){
    const data : ethers.providers.TransactionRequest = {
        //from : "******************************************",
        to : "******************************************",
        gasPrice: utils.parseUnits('120', 'gwei'),
        gasLimit:210000,
        data : "0x53048ae000000000000000000000000072cb10c6bfa5624dd07ef608027e366bd690048f0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000075370d83127b900000000000000000000000000000000000000000000000000000000000000030000000000000000000000000000000000000000000000000c9a00f9f69eab92000000000000000000000000000000000000000000000000024d3492765406cc00000000000000000000000000000000000000000000000000191293d975f5650000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000016000000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000f09128c3be126a91a6eb453613e7136ab9b6e320000000000000000000000000321eafb0aed358966a90513290de99763946a54b00000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000d7ef803ad48654016890dda9f12d709f87c79cd9000000000000000000000000eb579ddcd49a7beb3f205c9ff6006bb6390f138f00000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000a1c6ea4c6b9ea2cf0a8b48a3218656fbfb91c209000000000000000000000000a1221a5bbea699f507cc00bdedea05b5d2e32eba",
        nonce: 3810,
        chainId: 20,
        type: 1
    }
    //let a0 = serialize(<UnsignedTransaction>data);
    let a0 = "test";
    //let req = "";
    //await w.signTransaction(data);
    //let a0 = await resolveProperties(data);
    setTimeout(async ()=>{
        for(let i = 0; i < 10; i++){
            let beginTime = Number(new Date());
            let a1_wasm = ("0x" + await keccak(arrayify(a0), 256));
            //let s = serialize(<UnsignedTransaction>data, w._signingKey().signDigest(a1_wasm));
            //console.log(s);
            let speed = Number(new Date()) - beginTime;
            console.log(`js + wasm sign  ${i} : ${speed} ms`);
        }
    }, 3000);



    setTimeout(()=>{
        for(let i = 0; i < 10; i++){
            let beginTime = Number(new Date());
            //let a1 = keccak256(a0);
            //let s = serialize(<UnsignedTransaction>data, w._signingKey().signDigest(a1));
            //console.log(s);
            let speed = Number(new Date()) - beginTime;
            console.log(`js sign  ${i} : ${speed} ms`);
        }
    }, 3000);

}

//main();

function test2(){
    const data : UnsignedTransaction = {
        to : "******************************************",
        gasPrice: utils.parseUnits('120', 'gwei'),
        gasLimit:210000,
        data : "0x53048ae000000000000000000000000072cb10c6bfa5624dd07ef608027e366bd690048f0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000075370d83127b900000000000000000000000000000000000000000000000000000000000000030000000000000000000000000000000000000000000000000c9a00f9f69eab92000000000000000000000000000000000000000000000000024d3492765406cc00000000000000000000000000000000000000000000000000191293d975f5650000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000016000000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000f09128c3be126a91a6eb453613e7136ab9b6e320000000000000000000000000321eafb0aed358966a90513290de99763946a54b00000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000d7ef803ad48654016890dda9f12d709f87c79cd9000000000000000000000000eb579ddcd49a7beb3f205c9ff6006bb6390f138f00000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000a1c6ea4c6b9ea2cf0a8b48a3218656fbfb91c209000000000000000000000000a1221a5bbea699f507cc00bdedea05b5d2e32eba",
        nonce: 3810,
        chainId: 20,
        type: 1
    }
    let key = "d81e0a4db9731b209056c5e2d164fedb2206c8fc1b74cf6dca5173b3e0b8775c";
    let w = new ethers.Wallet("d81e0a4db9731b209056c5e2d164fedb2206c8fc1b74cf6dca5173b3e0b8775c");
    const count = 1000;

    setTimeout(()=>{
        const begin = new Date().getTime();
        for(let i = 0; i< 1; i++){
            let a0 = serialize(<UnsignedTransaction>data)
            let a1 = keccak256(a0);
            let s = serialize(<UnsignedTransaction>data, w._signingKey().signDigest(a1));
        }
        console.log(`[sign] js native init:  ${new Date().getTime() - begin} ms`);
    }, 100);

    setTimeout(()=>{
        const begin = new Date().getTime();
        for(let i = 0; i< 1; i++){
            WasmSign.sign(data, key)
        }
        console.log(`[sign] rust wasm init:  ${new Date().getTime() - begin} ms`);
    }, 200);

    setTimeout(()=>{
        const begin = new Date().getTime();
        for(let i = 0; i< count; i++){
            let a0 = serialize(<UnsignedTransaction>data)
            let a1 = keccak256(a0);
            let s = serialize(<UnsignedTransaction>data, w._signingKey().signDigest(a1));
        }
        console.log(`[sign] js native ${count}x:  ${new Date().getTime() - begin} ms`);
    }, 500);

    setTimeout(()=>{
        const begin = new Date().getTime();
        for(let i = 0; i< count; i++){
            WasmSign.sign(data, key)
        }
        console.log(`[sign] rust wasm ${count}:  ${new Date().getTime() - begin} ms`);
    }, 3000);
}
//test2();


function testRandSign(){
    const data : UnsignedTransaction = {
        to : "******************************************",
        //gasPrice: utils.parseUnits('120', 'gwei'),
        maxFeePerGas : utils.parseUnits('120', 'gwei'),
        maxPriorityFeePerGas : utils.parseUnits('120', 'gwei'),
        gasLimit:210000,
        data : "0x53048ae000000000000000000000000072cb10c6bfa5624dd07ef608027e366bd690048f0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000075370d83127b900000000000000000000000000000000000000000000000000000000000000030000000000000000000000000000000000000000000000000c9a00f9f69eab92000000000000000000000000000000000000000000000000024d3492765406cc00000000000000000000000000000000000000000000000000191293d975f5650000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000016000000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000f09128c3be126a91a6eb453613e7136ab9b6e320000000000000000000000000321eafb0aed358966a90513290de99763946a54b00000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000d7ef803ad48654016890dda9f12d709f87c79cd9000000000000000000000000eb579ddcd49a7beb3f205c9ff6006bb6390f138f00000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000a1c6ea4c6b9ea2cf0a8b48a3218656fbfb91c209000000000000000000000000a1221a5bbea699f507cc00bdedea05b5d2e32eba",
        nonce: 0,
        chainId: 1000,
        type: 2
    }
    let key = "d81e0a4db9731b209056c5e2d164fedb2206c8fc1b74cf6dca5173b3e0b8775c";
    let w = new ethers.Wallet("d81e0a4db9731b209056c5e2d164fedb2206c8fc1b74cf6dca5173b3e0b8775c");
    let keys = Object.values({
        "******************************************": "0xd81e0a4db9731b209056c5e2d164fedb2206c8fc1b74cf6dca5173b3e0b8775c",
        "******************************************": "0xa1bcfd06dfa6f9230ea34acdb36f6326228eef38e7166c608ecda23c905c38d6",
        "******************************************": "0xba8d3ec50effa5ca7bcd67b6ecbaea938a33a67057d43ac180bcf0bb5b817301",
        "******************************************": "0xec82dfb500207bda2732e21da4a09aaaed4e5f81bb1664eb7c652be24f1d12c6",
        "******************************************": "0x5b046fe54a9112e1bf6083e8cf94323f5ff169b3840cc9471e5c7b940960bf39",
        "******************************************": "0x6c72dfe71e3e3db1fc872b0d4c0d52ca7a60ffeb5e23737da17f3f9666f9e779",
        "******************************************": "0x3233e98a62615faebe19a1d9109d933257300499655a69c3ff10f9fc6c09a949",
        "0x0905b5e9A804e53C9304c79E9Ee44D7c62677561": "0x11d55e59d067d095c7d9edd81fc3cd7d5b3cdc07d7a206c9a17bfbeebad965f6",
        "0xb9221c0d8B1cac58967368c7B798E089aB3D1F90": "0x905a25753e901516ecd4aac55731babb24b46b21b5ced963c79c63c01ba6be71",
        "0xB11f985E3EAa0D05b1e36C59E81891BA3b58707D": "0x34c80ed9c7aed4756ae1abcdb77d5cccbb4934a7ad92bed322f9609011bc51b8"
    });

    //let signController = new SignController();

    for(let i = 0 ; i < 1000; i++){
        
        setTimeout(()=>{
            const begin = new Date().getTime();
            let a0 = serialize(<UnsignedTransaction>data)
            let a1 = keccak256(a0);
            let s = serialize(<UnsignedTransaction>data, w._signingKey().signDigest(a1));
            console.log(`[sign] js native:  ${new Date().getTime() - begin} ms`);
        }, i * 1000);
        
        
        /*
        setTimeout(()=>{
            const begin = new Date().getTime();
            const d = {...data};
            data.nonce = i * 10;
            WasmSign.sign(d, randomElem(keys));
            console.log(`${tools.now().str} [sign] rust wasm:  ${new Date().getTime() - begin} ms`);
        }, i * 2500);
        */
       
        setTimeout(async ()=>{
            let count = tools.randomInt(1,10);
            if(i == 0) count = 10;
            //count = 1; //debug
            //const promises = [];
            console.log(`${tools.now().str} [sign] rust async wasm (${i}): begin`);
            let begin = new Date().getTime();
            for(let j = 0 ; j < count; j++){
                const d = {...data};
                d.nonce = j * count;
                //await bot.signer.sign(d, tools.randomElem(keys));
                const key = tools.randomElem(keys);
                //let u8 = WasmSign.sign_full(d,key.slice(2, key.length));
                //let raw = WasmSign.sign_full(d, "d81e0a4db9731b209056c5e2d164fedb2206c8fc1b74cf6dca5173b3e0b8775c");
                bot.signer.sign(d, key).then((x)=>{
                    console.log(`${tools.now().str} [sign] rust async wasm (${i}-${j}):  ${new Date().getTime() - begin} ms`);
                });
                //console.log("WasmSign.sign_full >>>>>>>>>", raw);
                //promises.push(signer.sign(d, randomElem(keys)));
                
                /*
                begin = new Date().getTime();
                let a0 = serialize(<UnsignedTransaction>d);
                let a1 = keccak256(a0);
                let sign = w._signingKey().signDigest(a1);
                let s = serialize(<UnsignedTransaction>d, sign);
                console.log("r: ", sign.r);
                console.log("s: ", sign.s);
                console.log("v: ", sign.v);
                console.log("js sign >>>>>>>>>", s);
                console.log(`[sign] js native:  ${new Date().getTime() - begin} ms`);
                */
            }
        }, i * tools.randomInt(2000, 5000));
    }
}
testRandSign();