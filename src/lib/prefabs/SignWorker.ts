import { sign, sign_message, sign_tx } from "../web3_wasm/web3_wasm";
import { serialize, UnsignedTransaction } from "@ethersproject/transactions";
import { BytesLike, hexlify, arrayify, SignatureLike, splitSignature } from "@ethersproject/bytes";


import { Worker, isMainThread, workerData, parentPort } from 'node:worker_threads';
import { BigNumber } from "ethers";

if(!isMainThread){
    //console.log("thread init: ", workerData);
    const id = workerData;
    const zero = BigNumber.from('0');

    function wasnSign(tx: UnsignedTransaction, key:string){
        //console.log(tx);
        if(tx.gasPrice){
            tx.gasPrice = BigNumber.from(tx.gasPrice);
        } else if (tx.maxFeePerGas){
            tx.maxFeePerGas = BigNumber.from(tx.maxFeePerGas);
            tx.maxPriorityFeePerGas = BigNumber.from(tx.maxPriorityFeePerGas);
        }
        let msg = serialize(tx);
        const u8 = arrayify(msg);
        if(key[0] == "0" && key[1] == "x") key = key.slice(2, key.length);
        
        return serialize(tx, tx.type == 0 ? sign_message(u8, key) : sign(u8, key, BigInt(tx.chainId||0)));
    }

    function sign_full(tx : UnsignedTransaction, key:string){
        const data = tx.data?.toString() || "";
        if(key[0] == "0" && key[1] == "x") key = key.slice(2, key.length);
        let s = sign_tx(
            key,
            (tx.nonce || 0).toString(),
            (tx.to || ""),
            (tx.value || zero).toString(),
            (tx.chainId || 0).toString(),
            (tx.type || 0).toString(),
            data.slice(2, data.length),
            (tx.gasLimit || zero).toString(),
            (tx.gasPrice || zero).toString(),
            (tx.maxFeePerGas || zero).toString(),
            (tx.maxPriorityFeePerGas || zero).toString()
        );
        //console.log(s);
        return '0x' + s;
    }

    parentPort?.on('message', (msg)=>{
        //msg.port.postMessage(wasnSign(msg.tx, msg.key));
        //msg.port.postMessage(jsSign(msg.tx, msg.key));
        /*
        console.log('------------- 0 -------------');
        console.log(msg);
        console.log('------------- 1 -------------');
        console.log(msg.port);
        console.log('------------- 2 -------------');
        console.log(msg.args);
        console.log('------------- 3 -------------');
        */
       //console.log(msg.args.split("|"));
        //let begin = new Date().getTime();
        // @ts-ignore
        let s = "0x" + sign_tx(...msg.args.split("|"));
        //let end = new Date();
        //console.log(`\x1b[1;30m     [*${id}] (${end.getSeconds()}.${end.getMilliseconds()})  sign spd: ${end.getTime() - begin} ms\x1b[0m`);
        msg.port.postMessage(s);
        //msg.port.postMessage("111111");
    });
}