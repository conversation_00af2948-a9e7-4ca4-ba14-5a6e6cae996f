import { BigNumber, ethers, UnsignedTransaction, utils } from "ethers";
import { Worker, isMainThread, parentPort, workerData, MessageChannel } from "node:worker_threads";
import { serialize } from "@ethersproject/transactions";
import { BytesLike, hexlify, arrayify, SignatureLike, splitSignature } from "@ethersproject/bytes";

export default class SignPrefab {
    //channel : MessageChannel;
    worker : Worker;

    constructor(id = 0){
        const file = "./SignWorker.ts";
        //this.worker = new Worker(__filename.replace('Prefab.ts', 'Worker.js'), {
        this.worker = new Worker(require.resolve(file), {
            workerData: id.toString(),
            execArgv: /\.ts$/.test(file) ? ["--require", "ts-node/register"] : undefined,
        });
        this.worker.on('error', (e)=>{
            console.log("worker error: ", e);
        });
        this.worker.on('exit', (code)=>{
            console.log("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
            console.log(`!!!!!  Worker stopped with exit code ${code}`);
        });

    }

    sign(tx : UnsignedTransaction, key:string){
        const {port1, port2} = new MessageChannel();
        return new Promise<string>((resolve, reject)=>{
            this.worker.postMessage({tx: tx, key:key, port: port1}, [port1]);
            port2.on('message', (message)=>{
                port1.close();
                port2.close();
                resolve(message);
            });
        });
    }
    zero = BigNumber.from('0');

    sign_full(tx : UnsignedTransaction, key:string){
        if(key[0] == "0" && key[1] == "x") key = key.slice(2, key.length);
        const data = tx.data?.toString() || "";
        const input_data = key + "|"
                + (tx.nonce || 0).toString() + "|"
                + (tx.to || "") + "|"
                + (tx.value || this.zero).toString() + "|"
                + (tx.chainId || 0).toString() + "|"
                + (tx.type || 0).toString() + "|"
                + data.slice(2, data.length) + "|"
                + (tx.gasLimit || this.zero).toString() + "|"
                + (tx.gasPrice || this.zero).toString() + "|"
                + (tx.maxFeePerGas || this.zero).toString() + "|"
                + (tx.maxPriorityFeePerGas || this.zero).toString();

        //console.log("input_data: ", input_data);

        const {port1, port2} = new MessageChannel();
        return new Promise<string>((resolve, reject)=>{
            this.worker.postMessage({args : input_data, port: port1}, [port1]);
            port2.on('message', (message)=>{
                resolve(message);
                //port1.close();
                port2.close();
            });
        });
    }

}