import * as cp from 'child_process';
import { UnsignedTransaction } from "ethers";
import path from 'path';
import tools from '../tools';
import SignPrefab from "./SignPrefab";
//import precise from 'precise';

export default class SignController {
    public poolSignPrefab : SignPrefab[] = [];
    public poolSignCp : cp.ChildProcess[] = [];

    total = 0;

    constructor(){
        this.newPrefab();
        //this.newChild()
    }

    n = cp.fork(path.resolve(__dirname,  "./SignChild.ts"));

    testChild(){
        this.n.on("message", (m)=>{
            //console.log('PARENT got message back:', m);
            console.timeEnd('time');
        });
        setInterval(()=>{
            console.time('time');
            console.log("child_process test");
            this.n.send("test");
        }, 100);
    }

    newPrefab(){
        let newWorker = new SignPrefab(this.total);
        this.poolSignPrefab.push(newWorker);
        this.total++;
        return newWorker;
    }

    private get(){
        //console.log("this.poolSignPrefab.length: ", this.poolSignPrefab.length);
        if(this.poolSignPrefab.length > 0){
            //pop is faster then shift
            return this.poolSignPrefab.pop() || this.newPrefab();
        } else {
            return this.newPrefab();
        }
    }


    private recycle(prefab : SignPrefab){
        this.poolSignPrefab.push(prefab);
    }

    async sign(tx:UnsignedTransaction,key:string){
        const p = this.get();
        const res = await p.sign_full(tx, key);
        this.recycle(p);
        return res;
    }




}