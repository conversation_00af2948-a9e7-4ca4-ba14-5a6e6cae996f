import { BigNumber, UnsignedTransaction, ethers, utils } from "ethers";
import bot from "../bot";
import { FindPathResult, MixGas } from "./type/DataType";
import Lazy from "./lazy/LazyController";
import { macro } from "./macro";
import { ExWallet } from "./comp/EthersWrapper";
import WasmSign from "./comp/WasmSign";

export default class BotEoaRouter {

    wallet?: ExWallet;
    walletAddress = "";

    private _nonce = 0; //缓存nonce，不要直接使用
    private updating = false;

    public stableBalance : {[addr:string]:number} = {};

    opKey = "";
    router = "";

    init(){
        this.opKey = bot.config.eoaRouter.opKey;
        this.router = bot.config.eoaRouter.router.toLocaleLowerCase();
        
        setTimeout(()=>{
            this.wallet = bot.newWallet(this.opKey);
            this.walletAddress = this.wallet?.ins().address;
            bot.handle.nonce.update(this.walletAddress);
            this.updateStableBalance();
        }, 6000);

        setInterval(()=>{
            bot.handle.nonce.update(this.walletAddress, true);
            this.updateStableBalance();
        }, 1000 * bot.config.blockTime * 20);
    }

    async onTrash(amount:BigNumber, path:string[], mixGas:MixGas){
        this.swap(amount, path, mixGas, this.router, await this.getNonce());
    }

    async onMev(paths:FindPathResult[], mixGas:MixGas){
        for(const p of paths){
            this.swap(p.amount, p.tokenOuts, mixGas, this.router, await this.getNonce());
        }
    }

    async getNonce(){
        if(!this.updating){
            this._nonce = await bot.handle.nonce.useSync(this.walletAddress);
            this.updating = true;
            setTimeout(() => { this.updating = false; }, bot.config.blockTime * 1000);
        } else {
            this._nonce++;
        }
        return this._nonce;
    }

    async updateStableBalance(){
        if(!this.wallet) throw('[updateStableBalance] error wallet');

        const {stableTokens} = bot.config;
        let sum = 0;
        for(let i = 0; i < stableTokens.length; i++){
            let b = await this.getBalance(stableTokens[i], this.walletAddress);
            this.stableBalance[stableTokens[i]] ??= 0;
            this.stableBalance[stableTokens[i]] = b;
            sum += b;
        }
        //5分钟打印一次
        //if(bot.handle.blockNum % (300 / bot.config.blockTime) == 0){
            for(const [k,v] of Object.entries(this.stableBalance)){
                const config = bot.config.tokens[k];
                Lazy.ins().log1(`${macro.COLOR.Cyan}[EOA] ***** (${config.name.padStart(10, " ")}) ${v.toFixed(4)} ${macro.COLOR.Off}`);
            }
            Lazy.ins().log1(`${macro.COLOR.BCyan}[EOA] ***** total ${sum.toFixed(4)} ${macro.COLOR.Off}`);
        //}
    }

    async getBalance(token:string, holder:string){
        if(!this.wallet) throw('[getBalance] error wallet');
        //console.log(`[getBalance] ${token}, ${holder}`)
        const abiCoder = new ethers.utils.AbiCoder();
        const abi = ["function balanceOf(address account)"];
        const ifaec = new utils.Interface(abi);
        let res = await this.wallet.ins().call({
            to : token,
            data : ifaec.encodeFunctionData("balanceOf", [holder])
        });
    
        //console.log(res);
        //return res;
        const bn :BigNumber = abiCoder.decode(["uint256"], res)[0];
        return bot.token(token).toNum(bn);
    }

    swap(amount:BigNumber, path:string[], mixGas:MixGas, router:string, nonce:number){
        if(!this.wallet) throw('error wallet');
        const tokenIn = bot.token(path[0]);
        const balance = this.stableBalance[tokenIn.address];
        if(tokenIn.toNum(amount) > balance) amount = tokenIn.toBigNumber(balance);

        const data = bot.client.iRouter.encodeFunctionData("swapExactTokensForTokensSupportingFeeOnTransferTokens", 
            [amount, amount, path, this.walletAddress, BigNumber.from((new Date().getTime()/1000 + 1000).toFixed())]);
        /*
        const tx : ethers.providers.TransactionRequest = {
            from : this.walletAddress,
            to : router,
            gasLimit : 1500000,
            data : data,
            nonce : nonce,
            type : bot.handle.block.type,
            chainId : bot.handle.block.chainId
        };
        mixGas.attach(tx);
        const signData = await this.wallet.ins().signTransaction(await this.wallet.ins().populateTransaction(tx));
        */
        const signData = this.signTxSync(data, nonce, mixGas, 1500000, this.router);


        if(bot.mode == macro.BOT_MODE.TEST || bot.mode == macro.BOT_MODE.DEBUG) return;

        bot.handle.sendTransactionAll(signData, undefined, (r)=>{
            Lazy.ins().log(`${macro.COLOR.Green}[EOA] success n:${nonce} tx: ${r.transactionHash}${macro.COLOR.Off}`);
        }, undefined, (e)=>{
            Lazy.ins().log(`${macro.COLOR.Red}[EOA] fail swap n:${nonce}${macro.COLOR.Off}`)
        })
    }

    signTxSync(
        data:string,
        nonce:number,
        mixGas : MixGas,
        gasLimit = macro.abi.botPumpGasLimit,
        address = bot.config.bot.address){
            Lazy.ins().logTs1(`op: (EOA)  signSync begin, nonce: ${nonce}, ${mixGas.display()}`);
            const tx : UnsignedTransaction = {
                to : address,
                gasLimit : gasLimit,
                data : data,
                nonce : nonce,
                chainId : bot.handle.block.chainId,
            };
            mixGas.attach(tx);
            return WasmSign.sign_full(tx, this.opKey);
    }

}