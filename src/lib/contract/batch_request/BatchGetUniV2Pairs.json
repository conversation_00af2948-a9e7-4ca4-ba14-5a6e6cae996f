{"abi": [{"inputs": [{"internalType": "address", "name": "factory", "type": "address"}, {"internalType": "uint256", "name": "from", "type": "uint256"}, {"internalType": "uint256", "name": "step", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}], "bytecode": {"object": "0x608060405234801561001057600080fd5b5060405161035238038061035283398101604081905261002f916101bc565b600061003b8383610222565b90506000816001600160401b0381111561005757610057610235565b604051908082528060200260200182016040528015610080578160200160208202803683370190505b50905060005b8281101561013f576001600160a01b038616631e3dd18b6100a7838861024b565b6040518263ffffffff1660e01b81526004016100c39190610266565b602060405180830381865afa1580156100e0573d6000803e3d6000fd5b50505050604051601f3d908101601f191682016040526101039190810190610274565b8282815181106101155761011561029d565b6001600160a01b039092166020928302919091019091015280610137816102b3565b915050610086565b506000816040516020016101539190610339565b60405160208183030381529060405290506020810180590381f35b60006001600160a01b0382165b92915050565b61018a8161016e565b811461019557600080fd5b50565b60008151905061017b81610181565b8061018a565b60008151905061017b816101a7565b6000806000606084860312156101d4576101d4600080fd5b60006101e08686610198565b93505060206101f1868287016101ad565b9250506040610202868287016101ad565b9150509250925092565b634e487b7160e01b600052601160045260246000fd5b8181038181111561017b5761017b61020c565b634e487b7160e01b600052604160045260246000fd5b8082018082111561017b5761017b61020c565b805b82525050565b6020810161017b828461025e565b60006020828403121561028957610289600080fd5b60006102958484610198565b949350505050565b634e487b7160e01b600052603260045260246000fd5b600060001982036102c6576102c661020c565b5060010190565b6102608161016e565b60006102e283836102cd565b505060200190565b60006102fa826000815192915050565b80845260209384019383018060005b8381101561032e57815161031d88826102d6565b975060208301925050600101610309565b509495945050505050565b6020808252810161034a81846102ea565b939250505056fe", "sourceMap": "408:979:22:-:0;;;442:943;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;509:16;528:11;535:4;528;:11;:::i;:::-;509:30;;636:25;678:8;-1:-1:-1;;;;;664:23:22;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;664:23:22;;636:51;;739:9;734:114;758:8;754:1;:12;734:114;;;-1:-1:-1;;;;;801:26:22;;;828:8;835:1;828:4;:8;:::i;:::-;801:36;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;801:36:22;;;;;;;;;;;;:::i;:::-;787:8;796:1;787:11;;;;;;;;:::i;:::-;-1:-1:-1;;;;;787:50:22;;;:11;;;;;;;;;;;:50;768:3;;;;:::i;:::-;;;;734:114;;;;1050:28;1092:8;1081:20;;;;;;;;:::i;:::-;;;;;;;;;;;;;1050:51;;1309:4;1292:15;1288:26;1358:9;1349:7;1345:23;1334:9;1327:42;466:96:30;503:7;-1:-1:-1;;;;;400:54:30;;532:24;521:35;466:96;-1:-1:-1;;466:96:30:o;568:122::-;641:24;659:5;641:24;:::i;:::-;634:5;631:35;621:63;;680:1;677;670:12;621:63;568:122;:::o;696:143::-;753:5;784:6;778:13;769:22;;800:33;827:5;800:33;:::i;928:122::-;1019:5;1001:24;845:77;1056:143;1113:5;1144:6;1138:13;1129:22;;1160:33;1187:5;1160:33;:::i;1205:663::-;1293:6;1301;1309;1358:2;1346:9;1337:7;1333:23;1329:32;1326:119;;;1364:79;197:1;194;187:12;1364:79;1484:1;1509:64;1565:7;1545:9;1509:64;:::i;:::-;1499:74;;1455:128;1622:2;1648:64;1704:7;1695:6;1684:9;1680:22;1648:64;:::i;:::-;1638:74;;1593:129;1761:2;1787:64;1843:7;1834:6;1823:9;1819:22;1787:64;:::i;:::-;1777:74;;1732:129;1205:663;;;;;:::o;1874:180::-;-1:-1:-1;;;1919:1:30;1912:88;2019:4;2016:1;2009:15;2043:4;2040:1;2033:15;2060:194;2191:9;;;2213:11;;;2210:37;;;2227:18;;:::i;2260:180::-;-1:-1:-1;;;2305:1:30;2298:88;2405:4;2402:1;2395:15;2429:4;2426:1;2419:15;2446:191;2575:9;;;2597:10;;;2594:36;;;2610:18;;:::i;2643:118::-;2748:5;2730:24;2725:3;2718:37;2643:118;;:::o;2767:222::-;2898:2;2883:18;;2911:71;2887:9;2955:6;2911:71;:::i;2995:351::-;3065:6;3114:2;3102:9;3093:7;3089:23;3085:32;3082:119;;;3120:79;197:1;194;187:12;3120:79;3240:1;3265:64;3321:7;3301:9;3265:64;:::i;:::-;3255:74;2995:351;-1:-1:-1;;;;2995:351:30:o;3352:180::-;-1:-1:-1;;;3397:1:30;3390:88;3497:4;3494:1;3487:15;3521:4;3518:1;3511:15;3538:233;3577:3;-1:-1:-1;;3639:5:30;3636:77;3633:103;;3716:18;;:::i;:::-;-1:-1:-1;3763:1:30;3752:13;;3538:233::o;4225:108::-;4302:24;4320:5;4302:24;:::i;4339:179::-;4408:10;4429:46;4471:3;4463:6;4429:46;:::i;:::-;-1:-1:-1;;4507:4:30;4498:14;;4339:179::o;4673:732::-;4792:3;4821:54;4869:5;3844:6;3878:5;3872:12;3862:22;3777:114;-1:-1:-1;;3777:114:30;4821:54;4018:19;;;4070:4;4061:14;;;;4198;;;5111:1;5096:284;5121:6;5118:1;5115:13;5096:284;;;5197:6;5191:13;5224:63;5283:3;5268:13;5224:63;:::i;:::-;5217:70;-1:-1:-1;4626:4:30;4617:14;;5300:70;-1:-1:-1;;5143:1:30;5136:9;5096:284;;;-1:-1:-1;5396:3:30;;4673:732;-1:-1:-1;;;;;4673:732:30:o;5411:373::-;5592:2;5605:47;;;5577:18;;5669:108;5577:18;5763:6;5669:108;:::i;:::-;5661:116;5411:373;-1:-1:-1;;;5411:373:30:o", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052600080fdfea2646970667358221220150545cdf61b693dd4c055ba6818aa41a70e5a7e855574ace0d053e3662cd4c164736f6c63430008150033", "sourceMap": "408:979:22:-:0;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.21+commit.d9974bed\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"factory\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"from\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"step\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"}],\"devdoc\":{\"details\":\"This contract is not meant to be deployed. Instead, use a static call with the       deployment bytecode as payload.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/BatchGetUniV2Pairs.sol\":\"BatchGetUniV2Pairs\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"details\":{\"constantOptimizer\":true,\"cse\":true,\"deduplicate\":true,\"inliner\":true,\"jumpdestRemover\":true,\"orderLiterals\":true,\"peephole\":true,\"yul\":false},\"runs\":200},\"remappings\":[\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"src/BatchGetUniV2Pairs.sol\":{\"keccak256\":\"0x88004ee39ea2f47c07619355374559e1aaba7f44a682fd1f78e8966be0439b9d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://dbdb97723065f79e889b494b5452acef94e91fc82d97a908f0f570144d11e0be\",\"dweb:/ipfs/QmPppkW5BECKygA7LgX5iMpzvyUZ85emDVhbHyyyAutmK8\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.21+commit.d9974bed"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "factory", "type": "address"}, {"internalType": "uint256", "name": "from", "type": "uint256"}, {"internalType": "uint256", "name": "step", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"runs": 200, "details": {"peephole": true, "inliner": true, "jumpdestRemover": true, "orderLiterals": true, "deduplicate": true, "cse": true, "constantOptimizer": true, "yul": false}}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/BatchGetUniV2Pairs.sol": "BatchGetUniV2Pairs"}, "libraries": {}}, "sources": {"src/BatchGetUniV2Pairs.sol": {"keccak256": "0x88004ee39ea2f47c07619355374559e1aaba7f44a682fd1f78e8966be0439b9d", "urls": ["bzz-raw://dbdb97723065f79e889b494b5452acef94e91fc82d97a908f0f570144d11e0be", "dweb:/ipfs/QmPppkW5BECKygA7LgX5iMpzvyUZ85emDVhbHyyyAutmK8"], "license": "MIT"}}, "version": 1}, "ast": {"absolutePath": "src/BatchGetUniV2Pairs.sol", "id": 43632, "exportedSymbols": {"BatchGetUniV2Pairs": [43631], "IFactory": [43569]}, "nodeType": "SourceUnit", "src": "31:1357:22", "nodes": [{"id": 43551, "nodeType": "PragmaDirective", "src": "31:23:22", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 43569, "nodeType": "ContractDefinition", "src": "56:214:22", "nodes": [{"id": 43556, "nodeType": "FunctionDefinition", "src": "81:58:22", "nodes": [], "functionSelector": "574f2ba3", "implemented": false, "kind": "function", "modifiers": [], "name": "allPairsLength", "nameLocation": "90:14:22", "parameters": {"id": 43552, "nodeType": "ParameterList", "parameters": [], "src": "104:2:22"}, "returnParameters": {"id": 43555, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 43554, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 43556, "src": "130:7:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 43553, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "130:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "129:9:22"}, "scope": 43569, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 43561, "nodeType": "FunctionDefinition", "src": "144:54:22", "nodes": [], "functionSelector": "5d8c32a9", "implemented": false, "kind": "function", "modifiers": [], "name": "totalPairs", "nameLocation": "153:10:22", "parameters": {"id": 43557, "nodeType": "ParameterList", "parameters": [], "src": "163:2:22"}, "returnParameters": {"id": 43560, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 43559, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 43561, "src": "189:7:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 43558, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "189:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "188:9:22"}, "scope": 43569, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 43568, "nodeType": "FunctionDefinition", "src": "203:65:22", "nodes": [], "functionSelector": "1e3dd18b", "implemented": false, "kind": "function", "modifiers": [], "name": "allPairs", "nameLocation": "212:8:22", "parameters": {"id": 43564, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 43563, "mutability": "mutable", "name": "index", "nameLocation": "229:5:22", "nodeType": "VariableDeclaration", "scope": 43568, "src": "221:13:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 43562, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "221:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "220:15:22"}, "returnParameters": {"id": 43567, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 43566, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 43568, "src": "259:7:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 43565, "name": "address", "nodeType": "ElementaryTypeName", "src": "259:7:22", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "258:9:22"}, "scope": 43569, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IFactory", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [43569], "name": "IFactory", "nameLocation": "66:8:22", "scope": 43632, "usedErrors": [], "usedEvents": []}, {"id": 43631, "nodeType": "ContractDefinition", "src": "408:979:22", "nodes": [{"id": 43630, "nodeType": "FunctionDefinition", "src": "442:943:22", "nodes": [], "body": {"id": 43629, "nodeType": "Block", "src": "499:886:22", "nodes": [], "statements": [{"assignments": [43580], "declarations": [{"constant": false, "id": 43580, "mutability": "mutable", "name": "distance", "nameLocation": "517:8:22", "nodeType": "VariableDeclaration", "scope": 43629, "src": "509:16:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 43579, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "509:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 43584, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 43583, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 43581, "name": "step", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43576, "src": "528:4:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 43582, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43574, "src": "535:4:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "528:11:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "509:30:22"}, {"assignments": [43589], "declarations": [{"constant": false, "id": 43589, "mutability": "mutable", "name": "allPairs", "nameLocation": "653:8:22", "nodeType": "VariableDeclaration", "scope": 43629, "src": "636:25:22", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 43587, "name": "address", "nodeType": "ElementaryTypeName", "src": "636:7:22", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 43588, "nodeType": "ArrayTypeName", "src": "636:9:22", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "id": 43595, "initialValue": {"arguments": [{"id": 43593, "name": "distance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43580, "src": "678:8:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 43592, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "664:13:22", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_address_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (address[] memory)"}, "typeName": {"baseType": {"id": 43590, "name": "address", "nodeType": "ElementaryTypeName", "src": "668:7:22", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 43591, "nodeType": "ArrayTypeName", "src": "668:9:22", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}}, "id": 43594, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "664:23:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "636:51:22"}, {"body": {"id": 43619, "nodeType": "Block", "src": "773:75:22", "statements": [{"expression": {"id": 43617, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 43606, "name": "allPairs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43589, "src": "787:8:22", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 43608, "indexExpression": {"id": 43607, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43597, "src": "796:1:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "787:11:22", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 43615, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 43613, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43574, "src": "828:4:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 43614, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43597, "src": "835:1:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "828:8:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"id": 43610, "name": "factory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43572, "src": "810:7:22", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 43609, "name": "IFactory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43569, "src": "801:8:22", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IFactory_$43569_$", "typeString": "type(contract IFactory)"}}, "id": 43611, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "801:17:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IFactory_$43569", "typeString": "contract IFactory"}}, "id": 43612, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "819:8:22", "memberName": "allPairs", "nodeType": "MemberAccess", "referencedDeclaration": 43568, "src": "801:26:22", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_uint256_$returns$_t_address_$", "typeString": "function (uint256) view external returns (address)"}}, "id": 43616, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "801:36:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "787:50:22", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 43618, "nodeType": "ExpressionStatement", "src": "787:50:22"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 43602, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 43600, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43597, "src": "754:1:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 43601, "name": "distance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43580, "src": "758:8:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "754:12:22", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 43620, "initializationExpression": {"assignments": [43597], "declarations": [{"constant": false, "id": 43597, "mutability": "mutable", "name": "i", "nameLocation": "747:1:22", "nodeType": "VariableDeclaration", "scope": 43620, "src": "739:9:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 43596, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "739:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 43599, "initialValue": {"hexValue": "30", "id": 43598, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "751:1:22", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "739:13:22"}, "loopExpression": {"expression": {"id": 43604, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "768:3:22", "subExpression": {"id": 43603, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43597, "src": "768:1:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 43605, "nodeType": "ExpressionStatement", "src": "768:3:22"}, "nodeType": "ForStatement", "src": "734:114:22"}, {"assignments": [43622], "declarations": [{"constant": false, "id": 43622, "mutability": "mutable", "name": "_abiEncodedData", "nameLocation": "1063:15:22", "nodeType": "VariableDeclaration", "scope": 43629, "src": "1050:28:22", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 43621, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1050:5:22", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 43627, "initialValue": {"arguments": [{"id": 43625, "name": "allPairs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43589, "src": "1092:8:22", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}], "expression": {"id": 43623, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "1081:3:22", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 43624, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1085:6:22", "memberName": "encode", "nodeType": "MemberAccess", "src": "1081:10:22", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 43626, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1081:20:22", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "1050:51:22"}, {"AST": {"nativeSrc": "1121:258:22", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1121:258:22", "statements": [{"nativeSrc": "1271:43:22", "nodeType": "YulVariableDeclaration", "src": "1271:43:22", "value": {"arguments": [{"name": "_abiEncodedData", "nativeSrc": "1292:15:22", "nodeType": "YulIdentifier", "src": "1292:15:22"}, {"kind": "number", "nativeSrc": "1309:4:22", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1309:4:22", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "1288:3:22", "nodeType": "YulIdentifier", "src": "1288:3:22"}, "nativeSrc": "1288:26:22", "nodeType": "YulFunctionCall", "src": "1288:26:22"}, "variables": [{"name": "dataStart", "nativeSrc": "1275:9:22", "nodeType": "YulTypedName", "src": "1275:9:22", "type": ""}]}, {"expression": {"arguments": [{"name": "dataStart", "nativeSrc": "1334:9:22", "nodeType": "YulIdentifier", "src": "1334:9:22"}, {"arguments": [{"arguments": [], "functionName": {"name": "msize", "nativeSrc": "1349:5:22", "nodeType": "YulIdentifier", "src": "1349:5:22"}, "nativeSrc": "1349:7:22", "nodeType": "YulFunctionCall", "src": "1349:7:22"}, {"name": "dataStart", "nativeSrc": "1358:9:22", "nodeType": "YulIdentifier", "src": "1358:9:22"}], "functionName": {"name": "sub", "nativeSrc": "1345:3:22", "nodeType": "YulIdentifier", "src": "1345:3:22"}, "nativeSrc": "1345:23:22", "nodeType": "YulFunctionCall", "src": "1345:23:22"}], "functionName": {"name": "return", "nativeSrc": "1327:6:22", "nodeType": "YulIdentifier", "src": "1327:6:22"}, "nativeSrc": "1327:42:22", "nodeType": "YulFunctionCall", "src": "1327:42:22"}, "nativeSrc": "1327:42:22", "nodeType": "YulExpressionStatement", "src": "1327:42:22"}]}, "evmVersion": "paris", "externalReferences": [{"declaration": 43622, "isOffset": false, "isSlot": false, "src": "1292:15:22", "valueSize": 1}], "id": 43628, "nodeType": "InlineAssembly", "src": "1112:267:22"}]}, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 43577, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 43572, "mutability": "mutable", "name": "factory", "nameLocation": "462:7:22", "nodeType": "VariableDeclaration", "scope": 43630, "src": "454:15:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 43571, "name": "address", "nodeType": "ElementaryTypeName", "src": "454:7:22", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 43574, "mutability": "mutable", "name": "from", "nameLocation": "479:4:22", "nodeType": "VariableDeclaration", "scope": 43630, "src": "471:12:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 43573, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "471:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 43576, "mutability": "mutable", "name": "step", "nameLocation": "493:4:22", "nodeType": "VariableDeclaration", "scope": 43630, "src": "485:12:22", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 43575, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "485:7:22", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "453:45:22"}, "returnParameters": {"id": 43578, "nodeType": "ParameterList", "parameters": [], "src": "499:0:22"}, "scope": 43631, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "abstract": false, "baseContracts": [], "canonicalName": "BatchGetUniV2Pairs", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 43570, "nodeType": "StructuredDocumentation", "src": "272:135:22", "text": " @dev This contract is not meant to be deployed. Instead, use a static call with the\n       deployment bytecode as payload."}, "fullyImplemented": true, "linearizedBaseContracts": [43631], "name": "BatchGetUniV2Pairs", "nameLocation": "417:18:22", "scope": 43632, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 22}