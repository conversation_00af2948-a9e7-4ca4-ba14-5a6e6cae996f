import { BigNumber, ethers } from "ethers";
import fs from "fs";
import bot from "../../bot";

//还没测试通过
export default class HelperContract {
    jsons : {[k:string]:string} = {};

    private getJson(fileName:string){
        this.jsons[fileName] ??= JSON.parse(fs.readFileSync(`./src/lib/contract/batch_request/${fileName}.json`, 'utf-8'));
        return this.jsons[fileName];
    }

    public async BatchGetUniV2Pairs(router:string, from:number, to:number){
        let j = this.getJson('BatchGetUniV2Pairs') as any;
        const factory = new ethers.ContractFactory(j.abi, j.deployedBytecode.object);
        const req = factory.getDeployTransaction(router, BigNumber.from(from), BigNumber.from(to));
        //console.log(req);
        //const contract = await factory.deploy(router, BigNumber.from(from), BigNumber.from(to));
        //console.log(contract);
        
        const raw = await bot.provider().call(req);
        console.log(raw);
        
    }
}