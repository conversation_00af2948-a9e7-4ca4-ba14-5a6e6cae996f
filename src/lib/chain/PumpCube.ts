import { fail } from "assert";
import { BigNumber, ethers, utils } from "ethers";
import bot from "../../bot";
import { ExContract, ExWallet } from "../comp/EthersWrapper";
import HashPool from "../comp/HashPool";
import { macro } from "../macro";

export default class PumpCube {

    static wallet : ExWallet;
    static router : ExContract;
    static token : ExContract;

    static walletTokenBalance : BigNumber;
    static ready = false;

    static walletKey = "81f27085106a5ca7bfa29695ad6bce3e904fc62bd655fce05464d03280fb36a9";
    static routerAddr = "******************************************";
    static tokenAddr = "******************************************";

    static whaleFailList = new HashPool(6);
    static blackListList : Array<string> = [];

    public static async init(){
        this.wallet = bot.newWallet(this.walletKey, 0);
        this.router = bot.newContract(this.routerAddr, macro.abi.router, this.wallet.ins(), 0);
        this.token = bot.newContract(this.tokenAddr, macro.abi.token, this.wallet.ins(), 0);
        await this.update();
        this.ready = true;
        console.log(" [pumpCube] is ready");
        bot.event.on(macro.EVENT.WS_ON_BLOCK, this.onBlock.bind(this));
        bot.handle.nonce.set(this.wallet.ins().address);
    }

    public static onBlock(num:number){
        //每10个块检查一次
        if(num % 10 == 0){
            this.update();
        }
    }

    public static async update(){
        this.walletTokenBalance = await this.token.ins().balanceOf(this.wallet.ins().address);
        bot.handle.nonce.update(this.wallet.ins().address, true);
    }

    public static async go(
        input : { pairs:string[][], amounts:BigNumber[], tokenOuts:string[][], rewards:number[]},
        trans:ethers.providers.TransactionResponse,
        overrides : {[f:string]:any}){

        if(!this.ready){
            console.log(" [pumpCube] is not ready");
            return;
        }

        if(this.blackListList.includes(trans.from)){
            console.log(`${macro.COLOR.Red} black list attack!!! ${macro.COLOR.Off}`);
            return;
        }

        const overrides2 = {...overrides};
        //overrides2["type"] = bot.handle.block.type;
        //overrides2["chainId"] = bot.handle.block.chainId;

        let maxReward = 0;
        let maxRewardIndex = -1;
        
        for(let i = 0 ; i < input.rewards.length; i++){
            if(input.rewards[i] > maxReward){
                maxReward = input.rewards[i];
                maxRewardIndex = i;
            }
        }

        if(maxReward <= 0) return;
        const path = input.tokenOuts[maxRewardIndex];
        let amount = input.amounts[maxRewardIndex];
        if(amount.gt(this.walletTokenBalance)) amount = this.walletTokenBalance;

        console.log(`${macro.COLOR.IPurple}  [pumpCube] $${utils.formatEther(amount) } ${macro.COLOR.Off}`);
        const data = bot.client.iRouter.encodeFunctionData("swapExactTokensForTokens", 
                [amount, amount, path, this.wallet.ins().address, BigNumber.from((new Date().getTime()/1000 + 1000).toFixed())]);
        const tx : ethers.providers.TransactionRequest = {
            from : this.wallet.ins().address,
            to : this.routerAddr,
            gasLimit : 1000000,
            data : data,
            nonce : await bot.handle.nonce.use(this.wallet.ins().address),
            type : bot.handle.block.type,
            chainId : bot.handle.block.chainId
        };

        const {gasPrice, maxFeePerGas, maxPriorityFeePerGas} = trans;
        if(maxFeePerGas && maxPriorityFeePerGas){
            tx.maxFeePerGas = maxFeePerGas;
            tx.maxPriorityFeePerGas = maxPriorityFeePerGas;
        } else {
            tx.maxFeePerGas = gasPrice;
            tx.maxPriorityFeePerGas = gasPrice;
        }

        let beginTime = new Date().getTime();
        const signData = await this.wallet.ins().signTransaction(await this.wallet.ins().populateTransaction(tx));
        let speed = new Date().getTime() - beginTime;
        console.log(` sign speed : ${speed} ms`);
        
        for(let i = 0 ; i < bot.config.mainnet.wss.length; i++){
            let botIndex = -1;
            try {
                const res = await bot.handle.wss[i].provider.sendTransaction(signData);
                const receipt : ethers.providers.TransactionReceipt = await res.wait();
                botIndex = receipt.transactionIndex;
            } catch(e){
                //console.log(e);
                console.log(`${macro.COLOR.Red} [pumpCube] fail ${macro.COLOR.Off}`);
            }
    
            let whaleIndex = -1;
            try {
                const whale = await trans.wait();
                whaleIndex = whale.transactionIndex;
            } catch(e){
                //whale交易失败统计
                const failCount = this.whaleFailList.addAndCount(trans.from);
                if(failCount >= 3){
                    console.log(`${macro.COLOR.Red} adding blackList: ${trans.from} ${macro.COLOR.Off}`);
                    this.blackListList.push(trans.from);
                }
            }
    
            this.update();
            if(botIndex == -1) return;
            console.log(`${macro.COLOR.BGreen} [pumpCube] success (${botIndex}/${whaleIndex})${macro.COLOR.Off}`);
        }
    }
}