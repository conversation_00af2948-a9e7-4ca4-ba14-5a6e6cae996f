import { BigNumber, ethers, utils } from "ethers";
import bot from "../../bot";
import { ExContract, ExWallet } from "../comp/EthersWrapper";
import { macro } from "../macro";

export default class PumpDfk {

    static wallet : ExWallet;
    static router : ExContract;
    static token : ExContract;

    static walletTokenBalance : BigNumber;
    static ready = false;

    static walletKey = "81f27085106a5ca7bfa29695ad6bce3e904fc62bd655fce05464d03280fb36a9";
    static routerAddr = "******************************************";
    static tokenAddr = "******************************************";

    public static async init(){
        this.wallet = bot.newWallet(this.walletKey, 0);
        this.router = bot.newContract(this.routerAddr, macro.abi.router, this.wallet.ins(), 0);
        this.token = bot.newContract(this.tokenAddr, macro.abi.token, this.wallet.ins(), 0);
        await this.update();
        this.ready = true;
        console.log(" [pumpDfk] is ready");
        bot.event.on(macro.EVENT.WS_ON_BLOCK, this.onBlock.bind(this));
        bot.handle.nonce.set(this.wallet.ins().address);
    }

    public static onBlock(num:number){
        //每10个块检查一次
        if(num % 10 == 0){
            this.update();
        }
    }

    public static async update(){
        this.walletTokenBalance = await this.token.ins().balanceOf(this.wallet.ins().address);
        bot.handle.nonce.update(this.wallet.ins().address);
    }

    public static async go(
        input : { pairs:string[][], amounts:BigNumber[], tokenOuts:string[][], rewards:number[]},
        trans:ethers.providers.TransactionResponse,
        overrides : {[f:string]:any}){
        if(!this.ready){
            console.log(" [pumpDfk] is not ready");
            return;
        }

        const overrides2 = {...overrides};
        overrides2["nonce"] = bot.handle.nonce.use(this.wallet.ins().address);

        let maxReward = 0;
        let maxRewardIndex = -1;
        
        for(let i = 0 ; i < input.rewards.length; i++){
            if(input.rewards[i] > maxReward){
                maxReward = input.rewards[i];
                maxRewardIndex = i;
            }
        }

        if(maxReward <= 0) return;
        const path = input.tokenOuts[maxRewardIndex];
        let amount = input.amounts[maxRewardIndex];
        if(amount.gt(this.walletTokenBalance)) amount = this.walletTokenBalance;

        console.log(`${macro.COLOR.IPurple}  [pumpDfk] $${utils.formatEther(amount) } ${macro.COLOR.Off}`);
        
        let botIndex = -1;
        try {
            const res = await this.router.ins().swapExactTokensForTokens(
                amount,
                amount,
                path,
                this.wallet.ins().address,
                BigNumber.from((Number(new Date())/1000 + 1000).toFixed()),
                overrides);
            const receipt : ethers.providers.TransactionReceipt = await res.wait();
            botIndex = receipt.transactionIndex;
        } catch(e){
            console.log(`${macro.COLOR.Red} [pumpDfk] fail ${macro.COLOR.Off}`);
            this.update();
            return;
        }

        let whaleIndex = -1;
        try {
            const whale = await trans.wait();
            whaleIndex = whale.transactionIndex;
        } catch(e){}

        console.log(`${macro.COLOR.BGreen} [pumpDfk] success (${botIndex}/${whaleIndex})${macro.COLOR.Off}`);
        this.update();
    }
}