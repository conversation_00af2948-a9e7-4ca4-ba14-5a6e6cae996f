import Config from "./Config";

export default class ConfigHoo extends Config {

    mainnet = {
        data : "",
        //wss://ws-mainnet.hoosmartchain.com (ali-jp 5ms) (aws-jp 4ms)
        wss : ["https://http-mainnet.hoosmartchain.com"]
        //wss : ["wss://ws-mainnet.hoosmartchain.com", "wss://ws-mainnet.hoosmartchain.com", "wss://ws-mainnet.hoosmartchain.com", "b|https://http-mainnet.hoosmartchain.com"],
    };
    eth = "******************************************";
    stablePumpToken = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 1.2;
    gasDelta = 0.001;
    gasMax = 251.32485;

    feedMin = 0.4;
    feedMax = 0.5;

    bot = {
        active : true,
        //address : "******************************************",
        //address : "******************************************",
        address : "******************************************"
    }
    pump = {
        active: true,
        greed: true,
        gasMax :15.348,
        countMin:2,
        countMax:4,
    }

    tokens = {
        "******************************************" : { name: "whoo", ignore:true, cex:true, keep:10 },
        "******************************************" : { name: "usdt", ignore:true, cex:true, keep:10 },
        "******************************************" : { name: "usdc", ignore:true, cex:true },
        "******************************************" : { name: "eth", cex: true },
        "******************************************" : { name: "btc", cex:true},

        "******************************************" : { name: "pud" },
        //"******************************************" : { name: "elk" },
        "******************************************" : { name: "elk" },
        "******************************************" : { name: "bnb", cex:true},
        "******************************************" : { name: "link", cex:true},
        "******************************************" : { name: "dot", cex:true},
        "******************************************" : { name: "fil", cex:true},
        "******************************************" : { name: "flow", cex:true},
        "******************************************" : { name: "uni", cex:true},
        "******************************************" : { name: "shib", cex:true},
        "******************************************" : { name: "sand", cex:true},
        "******************************************" : { name: "ldt", maxLp:1},
        "0xbB680D2672b369d39FaC17487Be8dFc44DD42053" : { name: "sol", cex:true },
    };
    routers = {
        "0x0357B5e995f5F7A690e82896c74D00CFd17b3924" : {name: "pud"},
        "0x358382C226Da7Ba5E672F06a7E263Bd926eB0265" : {name: "elk"},
        "0xC3B459B8879B24B1fBcc60Fd205e10A0d41De92C" : {name: "unknow"},
        "0x9a45723AA0F705B1DCf9bd8C2099d3663fB967f9" : {name: "evo1"},
        "0xe3A3a5Ed3359FA5E0dd941D9d0E52C6222D364DD" : {name: "evo2"},
        "0x764f1fbfbcDd07C040748A3dDF393c9fa3e657F3" : {name: "u1"},
        "0x368B672D5B153dA0b968f773363615B452E8f6bf" : {name: "u2"},
        "0x8D313dcA645d03F598eb594EF82897B6e4d01e9A" : {name: "u3"},
    };
    blackList = [
        "0x48571DeC253b4Dd62AcCc6A6FAB6001c723f0B9f", //小单交易
        "0x530aDF738BB814bC7543ec4547b40f8E35BA3aF5",
        "0xc0441FDaB8598df0a17aE07F0856fE58e4014c0E",
        "0xc05496Ca2c3d80d6b0AeD4969d75Ca0215F0A5f1",
        "0x93BC143ca1Aeb735BA0270EB3B49059C585452aE",
    ];
}