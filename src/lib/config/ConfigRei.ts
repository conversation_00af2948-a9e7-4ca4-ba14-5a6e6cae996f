import Config from "./Config";

export default class ConfigRei extends Config {
    mainnet = {
        data : "https://rpc.rei.network",
        wss : ["wss://rpc.rei.network"]
    };
    blockTime = 5;
    eth = "******************************************";
    stableTokens = ["******************************************"];

    gasMin = 1;
    gasMax = 2000;
    feedAutoTimeHour = 6;
    
    feedMin = 0.4;
    feedMax = 0.5;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxNoPendingBlock = 1000;

    bot = {
        active:true,
        //crazy : true,
        //address : "******************************************",
        //address : "******************************************", //swap with fee, support v1
        address : "******************************************", //0412+0715
    }
    pump = {active:true, gasMax:2000, countMin:1, countMax:7}
    trash = {active:true, gasMin:1.012, delay:500}

    tokens = {
        "******************************************" : {name:"wrei", ignore:true,keep:100, price:0.0224},
    };
    routers = {
        "******************************************" : {name: "oort", fee:99750},
        "******************************************" : {name:"cola"},
        "0x5f6c2928d8e319003c8465b28173fc14eedf623f" : {name:"u2", fee:99750}
    };
    gasWatch = [
        "0xEFB8F5a7479A6532a71f70919C00a18aE8A164d6"
    ]
    tokenBlackList = {}

    whiteListPair = {}

    blackListPump = []
}