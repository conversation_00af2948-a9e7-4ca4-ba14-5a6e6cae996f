import Config from "./Config";

export default class ConfigLat extends Config {
    mainnet = {
        data : "https://rpc.onuschain.io",
        wss : ["wss://ws.onuschain.io"] //sg
    };

    eth = "******************************************";
    stableTokens = ["******************************************"];

    blockTime = 3; //每个区块的时间

    gasMin = 1;
    gasDelta = 0.0200012;
    gasMax = 1000;

    feedMin = 0.025;
    feedMax = 0.03;
    feedPumpMulti = 1; //pump的填充gas百分比
    feedAutoTimeHour = 1;


    bot = {
        active : true,
        crazy: false,
        address : "******************************************", //swap with fee, support v1
    };

    pump = {
        active:true,
        gasMax :1000.2,
        countMin:2,
        countMax:2,
        rewardMin:0.001,
    }

    trash = {active:true, rewardMin:0.001, bid:true, delay:500}

    tokens = {
        "******************************************" : {name:"wonus", ignore:true, price:0.5, keep:5, limit:5},
    };
    routers = {
        "******************************************" : {name: "dipole"},
    };
    gasWatch = []
}