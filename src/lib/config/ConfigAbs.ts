import Config from "./Config";

export default class ConfigAbs extends Config {
    mainnet = {
        data : "https://api.mainnet.abs.xyz",
        wss : ["wss://api.mainnet.abs.xyz/ws"]
        //wss : ["https://api.mainnet.abs.xyz"]
    };
    blockTime = 12;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 0.01001;
    gasMax = 500;
    feedAutoTimeHour = 6;
    onlyActivePair = true;

    feedMin = 0.0026;
    feedMax = 0.003;
    feedPumpMulti = 1; //pump的填充gas百分比

    bot = {
        active:false,
        crazy:false,
    address : "******************************************", //swap with fee, support v1, testPair pump2
    }
    pump = {active:false, gasMax:501, countMin:1, countMax:7}
    trash = {active:true, bid:false, delay:100, rewardMin:10}

    tokens = {
        "******************************************" : {name:"weth",   ignore:true, keep:0.0001, price:3000},
    };
    tokenBlackList = {}

    routers = {
        "******************************************" : {name:"abs"},
    };

    gasWatch = [
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}