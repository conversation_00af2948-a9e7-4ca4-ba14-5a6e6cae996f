import Config from "./Config";

export default class ConfigKub extends Config {
    mainnet = {
        data : "https://dataseed.bitkubchain.io",
        //"wss://wss.bitkubchain.io/"
        wss : ["wss://wss.bitkubchain.io/", "wss://wss.bitkubchain.io/"]
    };

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];

    blockTime = 3; //每个区块的时间

    feedMin = 0.2;
    feedMax = 0.18;
    feedPumpMulti = 1; //pump的填充gas百分比
    feedAutoTimeHour = -1;

    bot = {
        active : true,
        crazy: true,
        //address : "******************************************", //pump fee3
        //address : "******************************************", //pump fee3 + kub + gas saver
        //address : "******************************************", //fix pump online 20230114
        //address : "******************************************", //fix pump online 20230114 with _pump_kub
        //address : "******************************************", //pairV1 for kub 0115
        address : "******************************************", //swap with fee, support v1, testpair, pump2, testPairPatch
    };

    pump = {
        active:true,
        gasMax :1000.2,
        countMin:1,
        countMax:1,
        rewardMin:0.001,
    }

    trash = {active:true, rewardMin:0.001, delay:500}

    tokens = {
        "******************************************" : {name:"kkub", ignore:true, keep:30, price:1.6, limit:100},
        "******************************************" : {name:"kusdt", ignore:true, keep:30, limit:100},
    };
    routers = {
        "0xAb30a29168D792c5e6a54E4bcF1Aec926a3b20FA" : {name: "diamon", version:1},
        "0x6E9e62018A013b20bcB7C573690Fd1425dDD6b26" : {name: "moon"},
        "0xe010e2f0cEd4121d05C1C3d7eF523c5332827e5C" : {name: "next"},
        "0x0dEa45Fe319BC57098572049c6cFBD8bc0f2855C" : {name: "dk2"},
        "0x344CCD746b6Bc9c77c28C40dCB05E64D71567D46" : {name: "megaland"},

        //helper没有factory
        "0x7e4e5b29754cb111ad23cb1da1d9c0d70010b3f7" : {name: "dk" }, //DiceKingdomCallHelper
        "0x00bd353fdd7a2044fbcce83769cc7331f903e3f3" : {name: "moonHealperV2"},
        "0x33d395763f959840dD4BA571b71aa46C9C55598D" : {name: "moonHelper"},
    };
    gasWatch = [
    ]
}