import { macro } from "../macro";
import Config from "./Config";

export default class ConfigOec extends Config {
    mainnet = {
        //8545
        data : "https://exchainrpc.okex.org", //查询服务器
        //wss : ["https://exchainrpc.okex.org"],
        //websocket: "wss://exchainws.okex.org:8443",
        //wss : ["wss://exchainws.okex.org"],
        wss : ["https://exchainrpc.okex.org", "txpool|https://exchainrpc.okex.org"],
        //wss : ["ws://127.0.0.1:8546", "txpoolOnly|https://exchainrpc.okex.org"]
        //wss : ["wss://oktc-mainnet.blastapi.io/4a439050-5646-49ba-b4ca-2f0d058884ab", "txpoolOnly|https://exchainrpc.okex.org"],
        //wss : ["wss://oktc-mainnet.blastapi.io/a97ea43f-29c1-40c3-9308-21aeb3ca622a", "txpoolOnly|https://exchainrpc.okex.org"],
        //wss : ["ws://127.0.0.1:8546"]
        //wss : ["b|wss://okt-chain.api.onfinality.io/ws?apikey=aedb7318-17d6-4881-b3ad-8f9dfd2d9fc5", "txpoolOnly|https://exchainrpc.okex.org"]
        //wss://oktc-mainnet.blastapi.io/a97ea43f-29c1-40c3-9308-21aeb3ca622a
    };
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];
    //stableTokens = ["******************************************"];

    gasMin = 10.101;
    gasDelta = 0.0001;
    gasMax = 2000.9;
    feedAutoTimeHour = 6;
    //onlyActivePair = true;

    //disableSubscribeLog = true;

    feedMin = 1.4;
    feedMax = 2;
    feedPumpMulti = 1;

    blockTime = 3; //每个区块的时间
    maxOperator = 4;

    bot = {
        active : true,
        //sharingan : true,
        //address : "******************************************", //备用
        //address : "******************************************", //弃用
        //address : "******************************************", //修复wokt的问题
        //address : "******************************************", //新用户，增加了关闭log
        //address : "******************************************", //新的多pathswap
        //address : "0x6a5Aaf2624Eb19a4980e976271Cfc2B5889766AB", //多币种path
        //address : "0x46fF2dEDdbF4509b593267B0C1c164d79557098b", //多币种path+判断持仓是否小于输入
        //address : "0x2b93aE583301B813181A1b6c07CED569a354ab25",
        //address : "0x155738aA8C444145763B0082e450B1Fe07b3440C",
        //address : "0x2364Ea0C6109832f15ec3E0a342ae5E11F8DE5C8",
        //address : "0x7b9374A61e375e9365bd34c0b4e791e67E7F1c90",
        //address : "0x53647ba1B561087bC75664B9ad7AF40B9575822b",
        //address : "0x2D0239D98dAdbfa86471340ca63C67b715fb700A", //new pair checker
        //address : "0xC00fe586AfF35186d8Fb25AFDFEEd802085Cf728", //pump fee2
        //address : "0x6d69b554863afE63F45fC1D38D92216E05b87819", //pump fee3
        //address : "0x6ED1A1ede00C6ce95ac274054b0Df0d757a102D0", //pump fee4
        //address : "0x95F181d27c4DB2F0E691845FBC60eac1997758Ae", //pump 流动性提示
        //address : "0x8f8E2f32e4Fe74547E40A1aFc935c281E1B01880", //pump 流动性提示2
        //address : "0x956BFbdCd8Cf358dFC13325eF74f38d7FaE87Ef9", //pump 新的checkpair
        //address : "0x333d131a9971e7835b4AB633c051A3d2035250E2", //0115 support v1
        //address : "0xf418D08926d655cace2CC89fE93eEb4f3972CD07", //0115 support v1
        //address : "0x830A0b30648A78F8B1b466F7635Ac9b0486AaE41", //swap with fee, support v1, jswap
        //address : "0x305d399fc0572873e608e065d96086d65985aa90", //newBase for routerSwap
        //address : "0x2ef8d0759ACA6F6fe0393c8cabc3fcbf58360c1B", //0412+1125
        //address : "0xe93410E4eb14F230E2d5E3faa2178828B19cb890", //0412+0715
        address : "0xb85350180Ad939659C1649De0001Ec5Bfd4bDA35", //1115
    };
    bot2 = {
        active : false,
        //address: "0xb2dD46045C8161406764e3c0165067E769952f8C",
        //address: "0x33163A8118102E199f88A7132374A84BC424eF4E", //新的多pathswap
        //address : "0xcaffdb4552684fa4A1B064B9577323381D91C561", //多币种搬砖
        //address : "0x26325364df1f53Fa4e59a719085ba0a9C4e71797",
        //address : "0xC385656DC3dac32a781D3f3661CB3fa959Fb0588"
        //address : "0xa45d54d5Cc54409e42dFA3148AfBD6f392e6F953",
        //address : "******************************************", //swap with fee, support v1, jswap
        address : "******************************************", //0412+0715
    };
    pump = {
        active:true,
        gasMax :100.1,
        countMin:1,
        countMax:1,
        rewardMin:0.003,
        maxPump : 15,
    }

    trash = {active:true, rewardMin:0.001, bid:true, delay:100, gasMin:10.02}

    tokens = {
        "******************************************" : { name: "wokt", isEth:true, ignore: true, cex: true, price:7, keep:1 },
        "******************************************" : { name: "usdt", ignore: true, cex: true, keep:10, limit:1200 },
        /*
        "******************************************" : { name: "che", max: 8000, cex:true },
        "******************************************" : { name: "bxh", max: 10000 },
        "******************************************" : { name: "bnb", max: 0.06, cex:true },
        "******************************************" : { name: "hep", max: 40000 },
        "******************************************" : { name: "celt", max: 40000, cex: true },
        "******************************************" : { name: "blade", max: 7000 },
        "******************************************" : { name: "zeni", max:1 },
        "******************************************" : { name: "nuls", max: 70, cex: true },
        "******************************************" : { name: "o3", max: 800 },
        "0x52Bf8ccD76Aa5d117324750B8F1187FCA4E5332a" : { name: "eb", max: 5000, cex:true},
        "0xcDDA4c3131a1dA06D26Ea56A107FC9f1F7945394" : { name: "los", max: 180000 },
        */
        //"******************************************" : { name: "ufo", max: 1000 },
        //"******************************************" : { name: "org", max: 5000 },

        //"******************************************" : { name: "usdc", ignore: true, cex: true },
        //"******************************************" : { name: "usdk", ignore: true, cex: true },
        //"******************************************" : { name: "btck", ignore: true, cex: true },
        //"******************************************" : { name: "ethk", ignore: true, cex: true, price:2000 },
        //"******************************************" : { name: "busd", ignore: true, cex: true },
        //"******************************************" : { name: "okb",  cex: true },

        //"******************************************" : { name: "oktd" },
        //"******************************************" : { name: "ethkd" },
        //"******************************************" : { name: "btckd" },

        //"******************************************" : { name: "jf", max: 10000 },
        //"******************************************" : { name: "bac", max: 1000 },
        //"******************************************" : { name: "car", max: 1000 },
/*
        "******************************************" : { name: "zksk", cex: true },
        "******************************************" : { name: "hdt", max: 150000 },
        "******************************************" : { name: "fin", max:80 },
        //"******************************************" : { name: "koa", max: 100000 },
        //"******************************************" : { name: "rnft" },
        "******************************************" : { name: "crystal", max: 15000 },
        "******************************************" : { name: "cgs", cex:true },
        "******************************************" : { name: "ofi" },
        "******************************************" : { name: "stk" },
        "0x60fa9b096E1142Bf360628De03d6B3603C1D10f7" : { name: "pgo" },
        "0x08963Db742Ab159F27518D1D12188f69AA7387FB" : { name: "lowb" },
        "0x01d346F740E27B0f3f5513bC4D2e132E3A43Cc6e" : { name: "xensa" },
        "0x3212606F74Cc59656E1EC6f587FCA61bA3B85eb0" : { name: "sfgk", cex:true },
        "0xC95EFaf132507d81805f8Cfb90E4863939310105" : { name: "acmd" },
        "0x87eD798cE6C257FAEAec30e6bf1B22C4a3679aD4" : { name: "milk" },
        "0xd0C6821aba4FCC65e8f1542589e64BAe9dE11228" : { name: "fluxk", cex:true },
        "0x2218E0D5E0173769F5b4939a3aE423f7e5E4EAB7" : { name: "sushik", cex:true },
        "0x6F620EC89B8479e97A6985792d0c64F237566746" : { name: "wpc" },
        "0x0e816C86894D62ba0d1B4Dcf73406DdA593Da49C" : { name: "chip" },

        "0x7A47ab305b8a2A3F4020d13FA9EF73cDdCc0e7D4" : { name: "wing", max:40, cex:true },
        "0xcC137b0713E0DC63b1fA136272014F2A54Dd7aCB" : { name: "skill", cex:true, max:15 },
        "0x133Bb423d9248a336D2b3086b8F44A7DbFF3a13C" : { name: "sil", max: 0.7 },
        "0x97513e975a7fA9072c72C92d8000B0dB90b163c5" : { name: "babyDodge", max: 1000000 , cex: true},
        //"0x55Bb511bB528cBddD227E3d0Ca83D370fe448e93" : { name: "lhb" },
        "0x5c0b96bf2527CD0f67A4A492cA03ee069d395DD4" : { name: "sb", ignore: true },
        "0x2632256f09Ef59B428C48622dc042198B9be2609" : { name: "squidk", ignore : true },
        "0x493d8CBd9533E57D4BEfb17cc2eC1dB76828261d" : { name: "ast" },
        "0x2c9a1d0E1226939EdB7BBb68C43a080c28743C5C" : { name: "vemp" },
        "0x7AC8fD3B804D70596e326641e184f864F55f2430" : { name: "jdao" },
        "0xab0d1578216A545532882e420A8C61Ea07B00B12" : { name: "kst" },
        "0x39071D9E7a52e8685F1385d6fA17babfc58ce9C3" : { name: "klt" },
        "0x8B9415ddb643830281A2D1E389d8E024C7D2EdeF" : { name: "samu" },
        "0x1e9Fd164c40Ddd18739c2769182b7eb997aB8F19" : { name: "isl", max: 300000 },
        //"0xE1C110E1B1b4A1deD0cAf3E42BfBdbB7b5d7cE1C" : { name: "elk" },
        "0xeEeEEb57642040bE42185f49C52F7E9B38f8eeeE" : { name: "elk", cex:true},
        "0x408b1Bf3a7096D3c32215efc5fdF56aA3b3A48F9" : { name: "mst" },
        "0x5c5477b41Efa582759065d3029451E7693973030" : { name: "zac" },
        "0xd9e9A7F6bBDeb9649Be71fe889DFA8368262737B" : { name: "kt" },
        "0x287bd66753f0ca8e9c2171d782df39e4c28cfd65" : { name: "bala"},
        "0xabc732f6f69a519F6d508434481376B6221eb7d5" : { name: "dotk", cex:true},
        "0xfA520efC34C81bfC1E3DD48b7fE9fF326049f986" : { name: "ltck", cex:true},
        "0xbeb67de6cc5af652b2d9b0235750ed70f5a2cb0d" : { name: "linkk",cex:true},
        "0x2ceC1C3f71Db54C99D6df687d8AC968cCe80CC85" : { name: "tpt"},
        "0x1fE622E91e54D6AD00B01917351Ea6081426764A" : { name: "mark", max: 600000},
        "0xd9e9a7f6bbdeb9649be71fe889dfa8368262737b" : { name: "kt" },
        "0x49ac18d093c6210b2c64db7f6698407801e128cd" : { name: "itfx"},
        "0x77df6ebec3316792d4ea5bc0f8286c27905aa8e8" : { name: "auctionk"},
        
        //"0xbBCa3d08fd90bF03435AaC49882ce088006Ddd35" : { name: "libra" },
        //"0x8d4A00794A3e1C6a816BeeF5F21284d8acf9D176" : { name: "babyshib" },
        
        "0x8057687cbb5d8dde94a0e11557d5355c4aecd322" : { name: "mhunt", max:3800 },
        //"0x1062FDf250B44697216D07e41Df93824519F47aa" : { name: "crypl" },
        "0x1b8a091Cef252D2b9534c99E53e01a7682CC87AC" : { name: "okcmoon" },
        "0x80fE5D62cB5A1C61924227AA6c5c6804dA6D4E07" : { name: "esg" },
        "0x2E157378aeF88Eb0B2654ba4918Ef55A1709a814" : { name: "kt" },
        "0x748dEF2e7fbB37111761Aa78260B0ce71e41d4CA" : { name: "coco" },
        "0x1cC4D981e897A3D2E7785093A648c0a75fAd0453" : {name:"okXen"},

        "0x3b462f30058a3D6a0B9c9e0588F81a3838Df2b00" : { name:"babyokx", ignore:true },

        "0x97b05e6c5026d5480c4b6576a8699866eb58003b" : {name:"stokt", cex:true },
        */
        
    };

    routers = {
        "0xfF4CEf88372a9139d92141049caD7288F37A6cAB" : {name: "js_v2", fee:99000, type : macro.ROUTER_TYPE.V2_ONLY_ROUTER},

        "0xc97b81B8a38b9146010Df85f1Ac714aFE1554343" : {name: "okc"},
        "0xe45A870B3a6312B1113dE32722fceD89a346fcc6" : {name: "okc2"},

        "0x865bfde337C8aFBffF144Ff4C29f9404EBb22b15" : {name: "che",},
        "0xc3364A27f56b95f4bEB0742a7325D67a04D80942" : {name: 'ks'},
        
        "0x069A306A638ac9d3a68a6BD8BE898774C073DCb3" : {name: 'js'},

        "0xB0Cf54a0856c4F882f343cA315DD98d7C7F78d07" : {name: 'my'},
        "0x1b02dA8Cb0d097eB8D57A175b88c7D8b47997506" : {name: "sushi"},
        "0x6eC2A127057c13Dff1D04e0ae131a756aa4033d4" : {name: "pnd"},
        "0x9F843d9BA2A386BDA2845507450Fd47934fb3D03" : {name: "ai"},
        "0x56cdDEAa7344498a24E3303333DCAa46fDeD1707" : {name: "bxh", fee:99775},
        "0x226E3Bf1E4615AB8a53b5B828589457CB31E22Be" : {name: "kyi"},
        "0xCe0fd6c8bCE8A44CE69978B01488AA3fab737f26" : {name: "lsland"},
        "0x4652ab8e8821F234407b1f1cB0ac8dD7E617BfF8" : {name: "elk"},
        "0xCa2AF2A9b1EF83e5023FeD514AA5A622CFAF8d4B" : {name: "king"},
        "0x32d8c121ade1f6f916e975cfed6dcf9b40c0d293" : {name: "coco"},
        "0xCD4f01b62480926CC4614ea44FB1f1E5F449485e" : {name: "pho"},
        "0xec5bBf69C6BE29a7566F9b7D8125321DF2c82797" : {name: "pipi"},
        "0xE156181ca4bEA81260C1D1CdA7d083b224aED989" : {name: "pipi2"},
        "0x17EAEbC66fe64cBe9cC6b0B7CFb13B6b65b4f1Df" : {name: "samu"}, //没什么交易量
        "0x4EA9e4209ec81C763d806A66D7413B552c833e62" : {name: "tn", fee:99750},
        "0x34686CBF7229ed0bff2Fbe7ED2CFC916317764f6" : {name: "dfyn"},
        "0xF92dC46c2F373480cbC7Dacb0A003C4a2c23ea78" : {name: "u1", fee:99750},
        "0x257d864FD7787f64D214b6039314F24fC1522d5f" : {name: "u5"},
        "0xB564c162182Ed5Dc61F41491B69EdBc0B175AA94" : {name: "u6",  fee:99800},
        "0x876d0407a4D97fa73101Bc1a5c1376fF7F3e13E4" : {name: "u13", fee:99750},
        "0x2d6eF6938a2400c5Bc037D85828e4701D844E032" : {name: "u14"},
        "0x1434832Ebcb3B8CE99142Fda053D7195F23F3884" : {name: "u15", fee:99800},
        "0xc5B58628575dF1d27931E359220D2328e829FA5B" : {name: "u16", fee:100000},
        "0x66B140229D1a28A88847B980AA151CF8637cD5c9" : {name: "u18"},
        "0x4b127646aedcd6a0cbd8b31215b133cee67f78a1" : {name: "kis" },
        //"0xb45A2DDA996C32E93B8c47098E90Ed0E7ab18E39" : {name:"transmit"},

        //https://github.com/okx/OKX-DEX-Aggregator-ABI/blob/main/DexRouter.json
        "0xf6aab105cb9e66e03cad2c2f3f8558242593385c" : {name: "okx_multi", skipUpdate:true},



    };
    tokenBlackList = {
        //"0xbbca3d08fd90bf03435aac49882ce088006ddd35" : "libra", //fee
        //"0xE24b533D2170E808B64e7e22cC4006d19dfAd70e" : "babyOkx",
        //"0x21982869fd950B9480e6Bd9c4930a8F9DBe9c718" : "okking",
        //"0x43A817a2CD46e600b77392764bb3b1e97CAaB25E" : "okmoon",
        //"0x8Da6DA06A4902015cFe823b24c5Ed486016Ba3Cb" : "yyds",
    };
    gasWatch = [
        "0x8906AdA9a84b792a7E658f90b795A65eFc0DA3E8",
        "0x57eA0b3e097765085e1779407ec301532871b3d0",
        "0xFa5dF3Abf55Dcc8aBc97aD0F560A057CdA51b42B",
        "0xC2c6C1b16e55c0Aca9b10D2f7C7D6Da8A95Dd034",
        "0xE92e1b601071E78C5c634E2CF9c42Ee8F35C9b19",
        "0xc7FDF02F2657444399d4F34b1687f5D0a650d345",
        "0x68697da9EBa9cfC1F1d1D330700415597A45d1E9",
        "0xD90ECD141444084bF1154a38B1b0c58e02148610",
        "0x723bBb8b1c82a17455eb6A3C0eC5d6F70F865E9F",
        "******************************************", //中心化机器人
        "0x28dDbebe289aB3310f1B5A95824D56D013c98653", //wokt夹子
        "0xD980A7eBA84Ce8f2011a41Fbff446aD6B935dEf0", //usdt
        "0xe658B3295803C1Adb2DD0cC0cb0E990Db777b2f7",
        "0xcbdaf1b4ba7fbc093b5b6858bfae436471762c64", //夹子
        "0xAB34e1460ABeBa572b03c87dE225614Eff86c483",
        "0x08eb513Ee0841A6EAb0DFbdbd2d170301aa7D6Aa", //okt
        "0x256E0BD652892D71ad0Dc0b7eD332af1656Cecb4",
        "0x8Ac784d4D152D8cAFcE7d823fC7ab4866080AE0B", //okt
        "0x977210A96236c6B4908f806632515ABec41b5797", //2号

        "0x2aD71A5403C4E551465068280B4c2dF62C07661F",//小单佬
        "0x73F25470fb3B4d0Fe98B86F4d2b988E147BE7dA0",//神仙
        "0x524944935EDd81da89774f57A708920d5CD25c00",//搬砖

        "0xdfb47c0a1E6a850C301E16FF17F5Af9c68a9e94D",//残渣
        "0x9Dca6d2B7D49ebb54Aa6A47556A2746D6CeF255a",//搬砖

        "0x36DFFdc92E70fbad6e8BFeb736A73AB7316ddb8d",//搬砖+残渣
        "0x44f6244b8794ef4c5c1435d6b672fa5a58de88b6",//搬砖
        "0x36703e80f04e4893f648c8b204564a59ca41afd5",//搬砖
        "0x2f4fc1f36651a268f251a9100158f46f49fad3bb",
        "0x24155Ab98C7800e85C78DbE9B24154d2e34Da321", //20皮
        "0x8BA906414B704bbB6d92c9554700D2BDD59f105E", //残渣
        "0x1c4fd58276f4d4f78e55887ad8204345392343a0",//搬砖+残渣
        "0xaaBA1CA37f380Ad52743fE8f8640c986d77830C2",
        "0x672363B14536A821d6B9cdB52C250Deb7b7Af5F8",
        "0x84a7059096A393f09F3D3F5771E4f25e2d1474Ce",

        "0x4b74dD41330453dadDc6b9e2BA7F0A11EB13B31D",
        "0x2d39408Ae846204923cd15167C23acba07fd2D39",
        "0xc714D0D7fb6543BdA674FF05b312Bc456F5CcfC9",
    ]

    whiteListPair = {
        /*
        "0x45AE267070B3f98f3719669976Ed4568a6b29e27" : {fee0:0,   fee1:1500}, //usdt-pho
        "0x11Deb887c1A26c774eD531b395E1C4840381081E" : {fee0:0,   fee1:1500}, //usdt-pho
        "0x37e7207360b8120Fd8FD4F8216013Ce06fA6cF70" : {fee0:0,   fee1:1500}, //WOKT-pho
        "******************************************" : {fee0:0,   fee1:5000}, //usdt-qlb
        */
        //"******************************************" : {fee0:500, fee1:0}, //BABYOKX-WOKT //okc
        /*
        "******************************************" : {fee0:500, fee1:0}, //BABYOKX-OKB // 500/100000 fee //okc
        "******************************************" : {fee0:500, fee1:0}, //BABYOKX-WOKT //js_v2

        "******************************************" : {fee0:500, fee1:0}, //BABYOKX-ETHK //okc
        "******************************************" : {fee0:500, fee1:0}, //BABYOKX-BTCK //okc
        "******************************************" : {fee0:500, fee1:0}, //BABYOKX-WOKT //js_v1
        "******************************************" : {fee0:500, fee1:50}, //BABYOKX-USDT //js_v2

        "******************************************" : {fee0:500, fee1:1500}, //qlb-pho
        */
    }

    blackList = [
        "******************************************",
        //"******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************", //xen
        "******************************************", //中心化，假单太多，但是偶尔会有大单
    ]

    blackListPump = [
        "******************************************",
        //"******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        //"******************************************",
        "******************************************",
        "******************************************", //xen
    ]
}