import Config from "./Config";

export default class ConfigFitfi extends Config {
    mainnet = {
        data : "https://rpc.step.network",
        wss : ["https://rpc.step.network",]
        //wss : ["wss://rpc.step.network/ws"]
    };
    blockTime = 5;
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];

    gasMin = 1.1;
    gasMax = 2000;
    feedAutoTimeHour = 6;
    
    feedMin = 5;
    feedMax = 7;
    feedPumpMulti = 1; //pump的填充gas百分比

    bot = {
        active:true,
        crazy : false,
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************", //swap with fee, support v1, testpair, pump2
        address : "******************************************", //0412+0704
    }
    pump = {active:true, gasMax:200, countMin:1, countMax:7}
    trash = {active:true, gasMin:10.1, delay:0}

    tokens = {
        "******************************************" : {name:"wfitfi", ignore:true,keep:100, price:0.04},
        "******************************************" : {name:"usdc", ignore:true, keep:5},
        "0x7db4072d6e26bbf35129e826d656f230f791cd2f" : {name:"spex", ignore:true},
    };
    routers = {
        "0xA4196322aA900ACc92cD5Cd978aB47e77EfA07eb" : {name: "step"},
    };
    gasWatch = [
        "0xEFB8F5a7479A6532a71f70919C00a18aE8A164d6",
        "0x74Aa6977536F498c5aD4F30C02718c6C1988a112"
    ]
    tokenBlackList = {
    }
    whiteListPair = {
        "0x94449d70daed63004df6802c16f9e325151b7462" : {fee0:1000, fee1:0}, //SPEX-WFITFI
        "0x947ddc9ef3d88fa7f81d78fb839b97c3d906c311" : {fee0:1000, fee1:0}, //SPEX-USDT
        "0x4bf2ed821f28bb1a2004c10a251b7ac82cb6ddcc" : {fee0:1000, fee1:0}, //SPEX-USDC
    }

    blackListPump = [
        "0xf806d3668db5d2129d9d804a516ff7e4c7675c84",
    ]
}