import Config from "./Config";

export default class ConfigCanto extends Config {
    mainnet = {
        data : "",
        wss : ["canto|ws://127.0.0.1:18546", "https://canto.slingshot.finance"]
    };
    blockTime = 6;
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];

    gasMin = 100;
    gasMax = 1000;
    feedAutoTimeHour = 6;
    
    feedMin = 1;
    feedMax = 1.5;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxNoPendingBlock = 300;

    bot = {
        active:true,
        crazy:false,
        //address : "******************************************",
        address : "******************************************", //pump fee3
    }
    pump = {active:false, gasMax:200, countMin:1, countMax:7}
    trash = {active:true, bid:false, delay:0, rewardMin:0.005}

    tokens = {
        "******************************************" : {name:"wcanto", ignore:true, keep:100, price:0.17},
        "******************************************" : {name:"note", ignore:true, keep:10},
    };

    routers = {
        "******************************************" : {name: "sling", fee:100000},
        "0x0e2374110f4Eba21f396FBf2d92cC469372f7DA0" : {name: "forte"},
        "0xe6e35e2AFfE85642eeE4a534d4370A689554133c" : {name: "canto"},
        "0xe076DcC56147B72d3272b2539A36C74b3a3178f5" : {name: "leon"},
        "0xB6E050060C831d048e928c82151f2681D9BB8c5d" : {name: "u1"},
    };

    gasWatch = [
        "0x8A1d036bE71C9C4A6C3d951Cc2A3Ee028D12d3fa",
        "0xc6DF7B5E384Db0b45b18935C60c6EBC293950311"
    ]
    blackListPump = [
    ]
}