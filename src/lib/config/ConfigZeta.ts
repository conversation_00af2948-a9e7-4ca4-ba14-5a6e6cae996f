import { macro } from "../macro";
import Config from "./Config";

export default class ConfigZeta extends Config {
    //eddy空投
    //https://rewards-indexer-eddy-nyp2gy7iua-uc.a.run.app/api/v1/indexer/wallet-rewards?walletAddress=******************************************
    mainnet = {
        data : "https://zetachain-evm.blockpi.network/v1/rpc/public",
        //wss : ["https://zetachain-evm.blockpi.network/v1/rpc/public"],
        //wss : ["b|wss://zetachain-mainnet.blastapi.io/cdbf037f-538e-496d-8a41-696c453f07b8"],
        //wss : ["b|wss://zetachain-mainnet.blastapi.io/79d51d53-2a7f-435e-9e2d-570c0cb7207e"], //master
        wss : ["b|wss://zetachain-evm.blockpi.network/v1/ws/e927b280cadc576eb29330451da98a492207143a"]
        //wss : ["txpool|https://zetachain-mainnet-archive.allthatnode.com:8545"]
    };
    blockTime = 5;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 10.1;
    gasMax = 1000.1;
    feedAutoTimeHour = 2;
    disableSubscribeLog = true;

    feedMin = 0.4;
    feedMax = 0.5;
    feedPumpMulti = 1; //pump的填充gas百分比
    maxOperator = 2;

    bot = {
        active:true,
        crazy:false,
        address : "******************************************", //1115+0123
    }
    pump = {active:true, gasMax:1000, countMin:1, countMax:1, rewardMin: 0.01}
    trash = {active:true, bid:false, delay:100, rewardMin:0.01}

    tokens = {
        "******************************************" : {name:"wzeta", ignore:true, keep:0.1, isEth:true, price:0.25},
        "******************************************" : {name:"usdt.eth", ignore:true, keep:0.1, eq:["******************************************"]},
        "******************************************" : {name:"usdc.eth", ignore:true, keep:0.1, eq:["******************************************"]},
        "******************************************" : {name:"usdc.bsc", ignore:true, keep:0.1, eq:["******************************************"]},
        "******************************************" : {name:"usdt.bsc", ignore:true, keep:0.1, eq:["******************************************"]},
    };

    routers = {
        "******************************************" : { name: "eddy", type : macro.ROUTER_TYPE.V2_ONLY_ROUTER },
        "******************************************" : { name: "abs" },
        "******************************************" : { name: "sushi" },
        "******************************************" : { name: "zeta" },
        
    };

    gasWatch = [
        "******************************************"
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}