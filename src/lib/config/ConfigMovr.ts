import { macro } from "../macro";
import Config from "./Config";

export default class ConfigMovr extends Config {
    mainnet = {
        //wss://moonriver.public.blastapi.io          //sg
        //wss://moonriver-rpc.dwellir.com             //de
        //wss://moonriver.api.onfinality.io/public-ws //aws-jp
        data : "https://rpc.api.moonriver.moonbeam.network",
        wss : [
            //"ws://127.0.0.1:9944",
            "wss://wss.api.moonriver.moonbeam.network",
            //"b|wss://wss.api.moonriver.moonbeam.network",
            //"wss://moonriver-rpc.dwellir.com",
            //"wss://moonriver.api.onfinality.io/public-ws",
            //"b|wss://moonriver.blastapi.io/fccb123f-dbda-4b13-8687-842aee20b52c",
            //"b|https://rpc.api.moonriver.moonbeam.network",
            //"b|https://moonriver-mainnet.gateway.pokt.network/v1/lb/62a74fdb123e6f003963642f"
        ], //de
        
        //wss : ["ws://127.0.0.1:9944", "b|wss://moonriver.blastapi.io/fccb123f-dbda-4b13-8687-842aee20b52c"], //de
        
        //wss : ["wss://wss.api.moonriver.moonbeam.network", "wss://moonriver.api.onfinality.io/public-ws", "b|wss://moonriver-rpc.dwellir.com", "b|wss://moonriver.api.onfinality.io/ws?apikey=136f13de-2977-4862-a6c2-d4cac6ce7091", "https://moonriver-mainnet.gateway.pokt.network/v1/lb/62a74fdb123e6f003963642f"], //(ali-jp 14ms) (aws-jp 10ms)
        
        //wss : ["https://moonriver-rpc.dwellir.com"]
    };

    eth = "******************************************";
    stableTokens = [
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        //"******************************************",
    ];
    blockTime = 10;

    gasMin = 3;
    gasDelta = 0.0123;
    gasMax = 100;
    feedAutoTimeHour = 12;

    feedMax = 4;
    feedMin = 3.5;

    bot = {
        active : false,
        crazy : false,
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************", //new pair checker
        //address : "******************************************", //pump fee3
        //address : "0x66956c2AEf897dDB2A35C4eAa27d23BC6650Ac61", //pump 流动性适配
        //address : "0xe3b7F986D1EA072603Fd53Bc69A39f0768763B5A", //testPairPatch
        //address : "******************************************", //0412 + 0715
        address : "******************************************", //1115+0123 修复v1
        sharingan : true,
    };

    pump = {
        active:   true,
        rewardMin: 0.1,
        gasMax:   20,
        countMin: 1,
        countMax: 6,
    }

    trash = { active:true, bid:true, delay:500, bidMinUsd:10, rewardMin: 5 }

    tokens = {
        "******************************************" : { name: "wmovr",    isEth:true, ignore:true, price:8, keep:0.5, eq:["******************************************", "******************************************", "******************************************"]},
        "******************************************" : { name: "wmovr.sea", isEth:true,ignore:true, price:8, keep:0.5, eq:["******************************************", "******************************************", "******************************************"]},
        "******************************************" : { name: "wmovr.pad", isEth:true, ignore:true, price:8, keep:0.5, eq:["******************************************", "******************************************", "******************************************"]},
        "******************************************" : { name: "wmovr.new", isEth:true, ignore:true, price:8, keep:0.5, eq:["******************************************", "******************************************", "******************************************"]},

        "******************************************" : { name: "usdc.multi", ignore:true, cex:true, keep:5, price:0.4 },

        "******************************************" : { name: "usdt.multi", ignore:true, cex:true },
        
        "******************************************" : { name: "frax", ignore:true, cex:true, keep:5 },

        
        "******************************************" : { name: "usdc.m", ignore:true, cex:true},
        "******************************************" : { name: "busd", ignore:true, cex:true},
        "******************************************" : { name: "dai", ignore:true, cex:true},
        "******************************************" : { name: "mai", ignore:true, cex:true},
        "******************************************" : { name: "cusd", ignore:true, cex:true},
        "******************************************" : { name: "mim", ignore:true, cex:true},
        "******************************************" : { name: "eth.m", cex:true},

        "******************************************" : { name: "rome", cex:true, ignore:true},
        "******************************************" : { name: "bnb", cex:true },
        "******************************************" : { name: "ftm", cex:true },
        "******************************************" : { name: "eth", cex:true },
        "******************************************" : { name: "matic", cex:true },
        "******************************************" : { name: "fxs", cex:true},

        "******************************************" : { name: "replay" },
        "******************************************" : { name: "roar" },
        "******************************************" : { name: "xcRmrk", ignore:true},
        "******************************************" : { name: "wbtc", cex:true},
        "******************************************" : { name: "zlk", cex:true},
        "******************************************" : { name: "xcksm", cex:true, ignore:true,},
    };
    tokenBlackList = {
    };
    routers = {
        "******************************************" : {name: "solar", fee:99750}, //getAmountsOut比v2多一个参数
        "******************************************": {name: "imp", type:macro.ROUTER_TYPE.V1},
        "******************************************" : {name: "zen", type:macro.ROUTER_TYPE.V1}, //v1
        "******************************************" : {name: "zen", type:macro.ROUTER_TYPE.V1}, //not v2
        
        "******************************************" : {name: "u1", eth : "******************************************"},
        "******************************************" : {name: "u2", eth : "******************************************"},
        "******************************************" : {name: "u3"},
        "******************************************" : {name: "huck"},
        "******************************************": {name: "u6"},
        "******************************************": {name: "u7"},
        "******************************************": {name: "u8"},
        "******************************************": {name: "u9", fee:99800, eth: "******************************************"},
        "******************************************": {name: "u10"},
        "******************************************": {name: "u11"},
        "******************************************": {name: "u12", eth: "******************************************"},
        "******************************************": {name: "u13"},
        "******************************************": {name: "u14", fee:99750, eth:"******************************************"},
        "******************************************": {name: "u19"},
        "******************************************": {name: "u20"},
        "******************************************": {name: "u22"},
        "******************************************": {name: "u23"},
        "******************************************": {name: "u24"},
        "******************************************": {name: "zircon"},
        "******************************************": {name: "zircon2"},
        "******************************************": {name: "u24", fee:99750},
        "******************************************": {name: "u25"},
        "******************************************": {name: "u26"},
        "******************************************": {name: "u27"},
        "******************************************": {name: "u28"},
        "******************************************": {name: "u29"},
        //todo: 0x46B3fDF7b5CDe91Ac049936bF0bDb12c5d22202e
        
    };

    gasWatch = [
        "0xABbd7d756F3953e7a80271559435b52cE7fB4503", //夹子
        "0xDa7696E57F4825D4ef27c58Ad3213f72b3454867", //夹子2
        "0x99999990d1771f26a83f2adfb926a77c25a15c74", //frax夹子
        "0x31509147Da8B9F9815c410A7396f79a9A62F75D5", //2号
        "0xC070CeF28bA62D5410125FdEC14E6c56243F9597",
        "0xB21C708bFdEAFe3d84c7bC65576eb7BFe890322a",
        "0x68068501b1b27d0de067e2433c0554f2b9a52bfa", //大残渣
        "0x6b4597D32a763637f72D5806ce13eB853eB4B5d2",
        "0xD68f12B2CbBB42ef4AbBB5771F1568CdD4f9181B",
        "0xBC012f5bbAE30ea6b1e3d1f33F88C7Aaee4aec95",
    ]

    blackList: string[] = [
        "0xF2ba2EAD3D26C213721114a02E0C56D8631AE388", //操控rome价格
        "0x0b3203D084A9De317Fc8011AEFD6CFf4fC8314Ab",
        "0x7FE332a833B1765759C2bb36D573546ba599f517", //
        //"0xe1Fa699860444Be91D366C21DE8FeF56E3dEC77A", //高gas交易 66
        //"0xE738A6942b49784Ac1435850cCf6C4B102a1c909", //高gas交易 66
    ]

    blackListPump = [
        "0xD0c44BFe57153FABBDd02C98568ed88925eca578",
        "0x0b3203D084A9De317Fc8011AEFD6CFf4fC8314Ab"
    ];
}