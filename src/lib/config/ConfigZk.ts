import Config from "./Config";

export default class ConfigZk extends Config {
    mainnet = {
        data : "https://mainnet.era.zksync.io",
        //ca 43ms
        //io 23ms
        //ca_u 41ms
        //de 112ms
        //fr 112ms
        wss : ["wss://mainnet.era.zksync.io/ws"]
    };

    eth = "******************************************";
    stableTokens = [ "******************************************"];

    blockTime = 3; //每个区块的时间

    gasMin = 0.25;
    gasDelta = 0.0000012;
    gasMax = 1;

    feedMin = 0.001;
    feedMax = 0.002;
    feedPumpMulti = 1; //pump的填充gas百分比
    //feedAutoTimeHour = 100;

    bot = {
        active : false,
        crazy: false,
        address : "******************************************", //swap with fee, uniswapv1, test pair, pump2, pumpSafe, testPairPatch
    };

    pump = {
        active:true,
        gasMax :10000.2,
        countMin:2,
        countMax:2,
        rewardMin:0.005,
    }

    trash = {active:true, rewardMin:0.005, bid:true, delay:500}

    tokens = {
        "******************************************" : {name:"weth", price:1800, ignore:true, keep:1, limit:1},
        "******************************************" : {name:"usdc", ignore:true, keep:200, limit:300},
        
    };
    routers = {
        "******************************************" : {name: "mute"}, //pair fee
        "******************************************" : {name: "space"},
        "******************************************" : {name: "gem", fee:99000}, //pair fee
        //"******************************************" : {name:"sync"},
    };


    blackList = []
    blackListPump = []
    gasWatch = []
}