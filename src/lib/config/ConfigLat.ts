import Config from "./Config";

export default class ConfigLat extends Config {
    mainnet = {
        data : "https://openapi2.platon.network/rpc",
        wss : ["wss://openapi2.platon.network/ws"] //sg
    };

    eth = "******************************************";
    stableTokens = ["******************************************"];

    blockTime = 3; //每个区块的时间

    gasMin = 1;
    gasDelta = 0.0200012;
    gasMax = 1000;

    feedMin = 0.025;
    feedMax = 0.03;
    feedPumpMulti = 1; //pump的填充gas百分比
    feedAutoTimeHour = 1;


    bot = {
        active : true,
        crazy: false,
        address : "******************************************", //swap with fee, support v1
    };

    pump = {
        active:true,
        gasMax :1000.2,
        countMin:2,
        countMax:2,
        rewardMin:0.001,
    }

    trash = {active:true, rewardMin:0.001, bid:true, delay:500}

    tokens = {
        "******************************************" : {name:"wlat", ignore:true, price:0.02, keep:5, limit:5},
        "******************************************" : {name:"usdt", ignore:true, },
        "******************************************" : {name:"usdc", ignore:true, },
        "0x5901481e486395239434525745f37f496b41dd41" : {name:"usdc", ignore:true, },
        "0x6a2d262d56735dba19dd70682b39f6be9a931d98" : {name:"usdt", ignore:true, },
        "0x9befec61ba58a01cc6290bbaeaa288c40bade10c" : {name:"dai",  ignore:true, },
        "0x3795c36e7d12a8c252a20c5a7b455f7c57b60283" : {name:"dai",  ignore:true, },
    };
    routers = {
        "0x78b674FBC75c43c3D91A35fFFA938268040C1990" : {name: "dipole", fee:99750},
    };
    gasWatch = []
}