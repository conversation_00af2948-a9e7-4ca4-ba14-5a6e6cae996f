import { macro } from "../macro";
import Config from "./Config";

export default class ConfigGlmr extends Config {
    mainnet = {
        data : "https://rpc.api.moonbeam.network",
        //wss://moonbeam.public.blastapi.io
        //wss://moonbeam-rpc.dwellir.com
        //wss://moonbeam.api.onfinality.io/public-ws
        //wss : ["wss://wss.api.moonbeam.network"]
        //wss : ["ws://127.0.0.1:19944"]
        
        //de
        wss : [
            "wss://wss.api.moonbeam.network",
            //"ws://127.0.0.1:9944",
            //"wss://wss.api.moonbeam.network",
            //"b|wss://moonriver.public.blastapi.io",
            //"b|wss://moonbeam-rpc.dwellir.com",
            //"b|https://rpc.ankr.com/moonbeam",
            //"b|https://rpc.api.moonbeam.network",
            //"b|https://moonbeam-mainnet.gateway.pokt.network/v1/lb/629a2b5650ec8c0039bb30f0"  //de
        ],
        /*
        wss : [
            "wss://wss.api.moonbeam.network",
            "b|wss://wss.api.moonbeam.network",
            "b|wss://wss.api.moonbeam.network",
            "b|wss://wss.api.moonbeam.network",
            "b|wss://wss.api.moonbeam.network",
            "wss://moonbeam.api.onfinality.io/public-ws",
            //"b|wss://moonbeam-rpc.dwellir.com",
            //"b|wss://moonbeam.blastapi.io/605ac6ab-a37e-419b-bacd-cb51bd9112e9", //vg
            "b|wss://moonbeam.api.onfinality.io/ws?apikey=3b7d7d38-ac14-42a8-aee6-a5ef105d5f98",
            "https://rpc.ankr.com/moonbeam",
            "https://rpc.api.moonbeam.network",
            "https://moonbeam-mainnet.gateway.pokt.network/v1/lb/629a2b5650ec8c0039bb30f0"
        ]*/ //jp
        
        
    };

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************", "******************************************"];
    blockTime = 10;

    gasMin = 100;
    gasDelta = 0.0123;
    gasMax = 1000000; //需要8gas
    feedAutoTimeHour = 6;
    onlyActivePair = false;

    feedMin = 80;
    feedMax = 100;
    feedPumpMulti = 1; //pump的填充gas百分比

    bot = {
        active : false,
        crazy : false,
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************", //new pair checker
        //address : "******************************************", //pump fee3

        //address : "******************************************", //support v1
        //address : "0x5E56BA9F1c8F6f416F2E58A2398B7df995F06920", //support v1 remove catch Error(string memory reason)
        //address : "0xE7fa4105cC4C3e566922da4dBd47A3AE5171b4De", //test pair patch
        //address : "0x6973e9309bbd55609702ff578d5B2175E51CFC57", //0412 + 0715
        address : "0x4743b508CEf6AF1713B28cacffA0e0d3Eb525465", //1115 + 0123
        sharingan : true,
    };

    bot2 = {
        active:false,
        //address: "0x8bBF444997A8a46524813CA41F969135F0aCe464",
        //address: "0x405A1CC63b5590dd83a527b8034247165fC9abD6",
        //address : "******************************************"
        //address : "******************************************",  //swap with fee, support v1
        address : "******************************************", //0412 + 0715
    }

    pump = {
        active: true,
        gasMax :1000,
        countMin:2,
        countMax:7,
        rewardMin:0.005
    }

    trash = { active:true, bid:false , delay:500}

    tokens = {
        "******************************************" : { name: "wglmr",     isEth:true, ignore:true, cex:true, price:0.1, keep:3, limit:10000, eq:["******************************************"]},
        "******************************************" : { name: "wglmr.pad", isEth:true, ignore:true, cex:true, price:0.1, keep:3, limit:10000, eq:["******************************************"]},

        "******************************************" : { name: "usdc.wh",    ignore:true, keep:10,}, //d6
        "******************************************" : { name: "frax", cex:true, ignore:true, keep:10}, //d18

        "******************************************" : { name: "usdc.multi", ignore:true, keep:100,}, //d6
        "******************************************" : { name: "usdc.mad", max:20000},
        "******************************************" : { name: "xcausd",  max:3500, ignore:true}, //v3

        
        "******************************************" : {name: "wglmr.u15", ignore:true, price:0.5, keep:100},
        "******************************************" : { name: "xcUsdt", cex:true, ignore:true},
        
        "******************************************" : { name: "busd.multi", cex:true, ignore:true},

        "******************************************" : { name: "usdc.celer", ignore:true, keep:20},
        "******************************************" : { name: "usdt.multi", ignore:true, cex:true},
        "******************************************" : { name: "usdt.celer", ignore:true, cex:true},
        "******************************************" : { name: "eth.wm", cex:true, ignore:true},
        "******************************************" : { name: "wbtc.wm", cex:true, ignore:true},
        
        "******************************************" : { name: "eth.mad", max:1.5, maxLp:2},
        "******************************************" : { name: "btc.mad", max:0.008},
        "******************************************" : { name: "well", max:100000},
        "******************************************" : { name: "stella", max:4000, maxLp:2},
        //"******************************************" : { name: "xcDot", ignore:true, cex:true },
        "******************************************" : { name: "xcIntr", max:4000},
        "******************************************" : { name: "xcIbtc", max:0.04, ignore:true, cex:true},
        "******************************************" : { name: "flare", max:150000},
        "******************************************" : { name: "xcAca", max:28},
        "******************************************" : { name: "ldo", max:50, maxLp:1},

        //"******************************************" : { name: "matic", cex:true},
        //"******************************************" : { name: "eth", cex:true},
        //"******************************************" : { name: "bnb", cex:true},
        //"******************************************" : { name: "ftm", cex:true, maxLp:2},
        //"******************************************" : { name: "avax", cex:true},
        //"******************************************" : { name: "movr", cex:true},

        "******************************************" : {name:"mbdxn", maxLp:2},

        //"******************************************" : { name: "ath", ignore:true }, //容易被攻击，需要更新套娃逻辑
        //"******************************************" : { name: "ape", ignore:true},
        //"******************************************" : { name: "rtide", ignore:true},
        //"******************************************" : { name: "crystal", ignore:true},
        
        
        "******************************************" : { name: "mai", cex:true},
        //"******************************************" : { name: "wmovr", cex:true},
        //"0x31DAB3430f3081dfF3Ccd80F17AD98583437B213" : { name: "luna", maxLp:2},
        
        //"0xFFfffFFecB45aFD30a637967995394Cc88C0c194" : { name: "poop", max: 35000, maxLp:2 }, //被锁
        
        //"0xA423E7eEB60547d9C7b65005477b63ae7CE67E62" : {name: "etf", maxLp:2},
        "0xb564A5767A00Ee9075cAC561c427643286F8F4E1" : {name: "mbXen", maxLp:2},
        "0x3Fd9b6C9A24E09F67b7b706d72864aEbb439100C" : {name: "mbXen", maxLp:2},
    };

    tokenBlackList: { [k: string]: string; } = {
        "0x4EEaa1fd27c50C64E77272BCdDe68c28F0A3c3D7" : "orb", //fee
    }
    
    routers = {
        //0xB569949AB3F88d4aEc5224DBa96c0B6170b95D4A //stella聚合, AggregatorV3:0x80a04f0e5a43c2d14f66ee7aba86945ce1ec2336
        "0x70085a09d30d6f8c4ecf6ee10120d1847383bb57" : {name: "stella", fee:99750},
        "0x96b244391D98B62D19aE89b1A4dCcf0fc56970C7" : {name: "beam"},
        "0xd3B02Ff30c218c7f7756BA14bcA075Bf7C2C951e" : {name: "flare", fee:99750},
        "0xeb237cf62eda6a179561952840f17a7056d647f6" : {name: "zlk", type:macro.ROUTER_TYPE.V1}, //v1
        "******************************************" : {name: "zlk2", type:1},
        "******************************************" : {name: "u1"},
        "******************************************" : {name: "u2"},
        "******************************************" : {name: "u3"},
        "******************************************" : {name: "u4"},
        "******************************************" : {name: "pad", fee:99800, eth:"******************************************"},
        "******************************************" : {name: "u6", eth: "******************************************"},
        "******************************************" : {name: "u7", fee:99750},
        "******************************************" : {name: "u8"},
        "******************************************" : {name: "u11"},
        "******************************************" : {name: "u12"},
        "******************************************" : {name: "u13"},
        "******************************************" : {name: "u14"},

        "******************************************" : {name: "u15", eth: "******************************************"},
        "******************************************" : {name: "u16"},
        "******************************************" : {name: "u17"},
        "******************************************" : {name: "u18", fee:99900},
        "******************************************" : {name: "u19"},
        "******************************************" : {name: "frax"},
        "******************************************" : {name: "zen"},
        "******************************************" : {name: "u21", fee:99750},
        "******************************************" : {name: "u24"},
        
    }

    gasWatch: string[] = [
        "******************************************",
        "******************************************", //glmr搬砖
        "******************************************", //usdc搬砖
        //"******************************************", //2号
        //"******************************************", //2号
        "0x1804f3dFF993f83399A2A02824a3A939EfA20273", //2号
        "0xe1fd2c8cebf8b1ec5741e65f64ea40411ce193ff", //usdc
        //"0x953963ca8fb8f96a3325a43f050ac249e722a011", //残渣
        "0xc72f012306ea0f17c0cbff0914c1c523a414d51b", //残渣
        "0xb55d01c33e86c981cd576e16e576067357fc8d23", //残渣
        "0x2b731E8e2C72cC14628346EB1Bc11ebF1A4ef2e6",
        "0x245EeD18fd12891190BA0b69c1fD82A299874c7D",
        "0x9A7376935632926A5F66bE7EdC895c3519f5F985", //套娃
        "0x755b6f345045b05272e439ad8ada84547014b529",
    ]

    blackList = [
        //"0x0B919e8e21DdFaDBe8D9A0F7687b69c20058a48C",
        "0xF0c09d91aA30E491e1A3A20C6FD35838257E2648",
        "0x1d3286A3348Fa99852d147C57A79045B41c4f713",
        "0xdFBC7b76803B709BD61635acde1B831A823E0d4e",
        "0xd1F4047d537D7E53416a84b872d90eD5751480B1",
        //"0x75a8816b106f973424D2a2ceB5B277179d1a79bf",
        "0x1bd4dD1E9Aa9fDCc7272bb610c64c688256fC210",
        "0x08fC367978AB26BDEDC27f0038065eC942F754B4",
        "0x54e6627177165Aa7b6F31752c744c306FaC993dB",
        "0x40d244d7Ce6F42F650e79df70a345168f9e0309d", //hacker
        "0x2f4f6199ff4DaE3338451465be5AA4FAf875BED1",
        "0xF26AC8133df406fde283f44fe6F361dd96C4Effe",
        "0x975a75B4dF38b81E21e19DE1003c2Dc98C5B4203",
        "0xac8777e09acae7816ce3c598ca85293dda3e3b72",
        "0x05310818a2232cA5aC779BE04077F518b4B2E886",
        "0xFfeC615535a6Bb503578b992662b7024e428acA6", //hacker
        "0x124d821300a480E9B018703B05989f99520112cF",
        "0xDAC550740337f8549873Cc1d15fA52B48d120eB8",
        //"0xF4e3fF6EbAf4c5556feAAF902FFc04197bfbfcae"
    ]

    blackListPump = [
        //"0x0B919e8e21DdFaDBe8D9A0F7687b69c20058a48C",
        "0xF0c09d91aA30E491e1A3A20C6FD35838257E2648",
        //"0x1d3286A3348Fa99852d147C57A79045B41c4f713", //机器人
        "0xdFBC7b76803B709BD61635acde1B831A823E0d4e",
        "0xd1F4047d537D7E53416a84b872d90eD5751480B1",
        //"0x75a8816b106f973424D2a2ceB5B277179d1a79bf",
        "0x1bd4dD1E9Aa9fDCc7272bb610c64c688256fC210",
        "0x08fC367978AB26BDEDC27f0038065eC942F754B4",
        "0x54e6627177165Aa7b6F31752c744c306FaC993dB",
        "0x40d244d7Ce6F42F650e79df70a345168f9e0309d", //hacker
        "0x2f4f6199ff4DaE3338451465be5AA4FAf875BED1",
        "0xF26AC8133df406fde283f44fe6F361dd96C4Effe",
        "0x975a75B4dF38b81E21e19DE1003c2Dc98C5B4203",
        "0xac8777e09acae7816ce3c598ca85293dda3e3b72",
        "0x05310818a2232cA5aC779BE04077F518b4B2E886",
        "0x124d821300a480E9B018703B05989f99520112cF",
        //"0xF4e3fF6EbAf4c5556feAAF902FFc04197bfbfcae"
    ]
}