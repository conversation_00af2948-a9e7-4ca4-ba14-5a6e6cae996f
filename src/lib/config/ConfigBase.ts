import { macro } from "../macro";
import Config from "./Config";

export default class ConfigBase extends Config {
    mainnet = {
        data : "https://mainnet.base.org",
        //data : "ws://127.0.0.1:8546",
        //aws-vg 5ms
        //wss : ["wss://base-mainnet.blastapi.io/cdbf037f-538e-496d-8a41-696c453f07b8"] //aws-vg
        //wss : ["wss://broken-white-aura.base-mainnet.discover.quiknode.pro/0e5c2c70b33461f5f2f814a4a5b41ffd14ae2bfe/"],
        //wss : ["https://mainnet.base.org"],
        //wss : ["https://developer-access-mainnet.base.org"]
        wss : ["ws://127.0.0.1:8546"]
    };
    blockTime = 2;
    eth = "******************************************";
    stableTokens = ["******************************************"];

    gasMin = 0.00101;
    gasDelta = 0.0033;
    gasMax = 0.00101;

    feedAutoTimeHour = 12; //单位小时
    onlyActivePair = false;

    feedMin = 0.015;
    feedMax = 0.03;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 1;
    securityLevel = 10;

    bot = {
        active:false,
        crazy:false,
        address : "******************************************", //0412 + 0715
        gasMax : 0.1,
    }
    pump = {
        active:false, gasMax:10, countMin:1, countMax:3, rewardMin: 0.01
    }
    trash = {
        active: true,
        bid:false,
        l2: {
            bid : false,
            payout : 0.60, //贡献的利润
            l1GasRpc:"https://eth.llamarpc.com"
        },
        delay:0,
        rewardMin: 0.05
    }

    eoaRouter = { //目前需要先手动approve一下router
        active:false,
        router: "******************************************",
        opKey:"81f27085106a5ca7bfa29695ad6bce3e904fc62bd655fce05464d03280fb36a9",
    }

    tokens = {
        "******************************************" : {name:"weth", ignore:true, keep:0.01, price:1900},
    };
    tokenBlackList = {}

    routers = {
        //"******************************************" : { name:"baseZap"},
        "******************************************" : { name: "baseswap", fee:99750},
        "******************************************" : { name: "rocket", },
        "******************************************" : { name: "syn", fee:99750 },
        "******************************************" : { name: "swapbase", },
        
        //"******************************************" : { name: "hzd", type: macro.ROUTER_TYPE.V3},
        //"******************************************" : { name:"dackie" }, //v3
        //"******************************************" : { name: "throne" }, //v3
        //"******************************************" : { name: "cex" }, //v3
        //"******************************************" : { name: "diamond_v3?" },

        "******************************************" : { name: "cb", },
        "0x5B2A756740Ea91dD1d6bEf5551CA29cF6fADF9f9" : { name: "kokos" },
        "0xE11b93B61f6291d35c5a2beA0A9fF169080160cF" : { name: "velo", type: macro.ROUTER_TYPE.V2_STABLE, stableFee:99970, volatileFee:99750},
        "0xb588A34aFA5be3F3Af703159fb6586A2cAF04Ba8" : { name: "oasis", fee:99000 },
        "0xF83675ac64a142D92234681B7AfB6Ba00fa38dFF" : { name: "lfg", },
        "0x70e44d51439f92044e40559dcd6a193c98b60a1e" : { name: "dbx", },
        "0xBb5e1777A331ED93E07cF043363e48d320eb96c4" : { name: "ice", },
        "0x7f2ff89d3C45010c976Ea6bb7715DC7098AF786E" : { name: "alien"},
        //"0x0681363e5da35a248e1abb5cddd6db9cdac9a771" : { name: "diamond" },
        "0x5568e4f19b9063e0e0386bf66b3eef2b65327486" : { name: "baso", type: macro.ROUTER_TYPE.V2_STABLE, stableFee:99980, volatileFee:99800},
        "0x10cc46af6964fba2b7266ddf2cd8a8585b71a210" : { name: "bakery"},
        "0x0a64d8858061e1c5e2bc63f2c267cb87b47dea57" : { name: "cloud" },
        "0xd3ea3bc1f5a3f881bd6ce9761cba5a0833a5d737" : { name: "leet" },
        "0xba88b20dd982cf50701cf29755bbc3dfa353821f" : { name: "based", fee:99750 },
        "0xbb5e1777a331ed93e07cf043363e48d320eb96c4" : { name: "ice"},
        
    };

    gasWatch = [
    ]
    whiteListPair = {
        //"0x7d8100072ba0e4da8dc6bd258859a5dc1a452e05" : {fee0:1000, fee1:1000}, //test
    }
    blackListPair = [];

    blackList = [
    ]

    blackListPump = [
    ]
}