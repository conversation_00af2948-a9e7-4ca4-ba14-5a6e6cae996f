import Config from "./Config";

export default class ConfigPolygon extends Config {

    mainnet = {
        //https://docs.polygon.technology/docs/develop/network-details/network/
        
        //wss : ["wss://ws-matic-mainnet.chainstacklabs.com"]
        data : "https://rpc.ankr.com/polygon", //fork
        wss : [
               //"ws://127.0.0.1:8546",
               "wss://ws-matic-mainnet.chainstacklabs.com",
               "b|wss://polygon-mainnet.blastapi.io/4a439050-5646-49ba-b4ca-2f0d058884ab",
               "b|wss://rpc.ankr.com/polygon/ws/c959fef5bb6d732dc90f685ebea9bf31b73f21ba843c15ccf222df5704d112c3",
               "b|https://polygon-mainnet.public.blastapi.io",
            ],
        

        //wss : ["wss://rpc-mainnet.maticvigil.com/ws/v1/d974401af2ac21ba740b2066f441e37e9df9d9ee"],
        //wss : ["https://rpc-mainnet.maticvigil.com"],
        //wss : ["wss://polygon-mainnet-api.bwarelabs.com/ws/cd79f675-b3f3-4053-bad6-7e4c45de3514", "wss://ws-matic-mainnet.chainstacklabs.com", "https://rpc-mainnet.maticvigil.com"],
        //wss : ["wss://rpc.ankr.com/polygon/ws/c959fef5bb6d732dc90f685ebea9bf31b73f21ba843c15ccf222df5704d112c3", "wss://polygon-mainnet-api.bwarelabs.com/ws/cd79f675-b3f3-4053-bad6-7e4c45de3514", "https://polygon-rpc.com"],
        //websocket : "wss://polygon-mainnet-api.bwarelabs.com/ws/72723c7b-d13e-4f07-acda-f310cc2f136c", //bull2
        //websocket : "wss://polygon-mainnet-api.bwarelabs.com/ws/70b317f1-83d1-488d-b3a6-c5589158268e",
        //websocket : "wss://polygon-mainnet-api.bwarelabs.com/ws/be7a24d6-9801-44ed-83e9-ec19472397ce",
        //websocket : "wss://ws-matic-mainnet.chainstacklabs.com",
        //wss : ["https://rpc.ankr.com/polygon"]
        //wss : ["https://polygon-rpc.com"],
    };
    eth = "******************************************";
    stablePumpToken = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************"];
    gasMin = 55;
    gasMax = 2400.231;
    gasDelta = 0.01234;

    feedMin = 1.8;
    feedMax = 2;
    feedAutoTimeHour = 12;
    
    onlyActivePair = true;

    bot = {
        active : true,
        //address : "******************************************",
        //address : "******************************************", //修复了用eth兑换时whale验证失败的问题
        //address : "******************************************", //new smart path
        //address : "******************************************", //多币种
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************", //new pair checker //******************************************
        address : "******************************************", //base proxy
    };
    bot2 = {
        active : true,
        //address: "******************************************",
        //address : "******************************************", //new smart path
        //address : "******************************************",
        //address: "******************************************",
        //address : "0x7d5CD2c96a21dE1AaC7BFa8DaC5A227D9065Eb26",
        address : "0xE971946a7d4De0Dc1AEBB86EE5d403EAAE73d487", //base proxy
    };

    pump = {
        active:false,
        greed:false,
        rewardMin : 0.5,
        allPath : true,
        gasMax :550,
        countMin:1,
        countMax:7,
    };

    trash = { active:false , delay:1000}

    tokens = {
        "******************************************" : { name: "wmatic", ignore: true, cex:true, keep:50, price:1.2 },
        "******************************************" : { name: "usdt", ignore: true, cex:true, keep:100 },
        "******************************************" : { name: "dai", ignore: true, cex:true, keep:100 },
        "******************************************" : { name: "usdc", ignore: true, cex:true, keep:100 },
        "******************************************" : { name: "weth", ignore: true, cex:true, keep:0.1, price: 1100},


        "******************************************" : { name: "gaia",},
        "******************************************" : { name: "gns", max:800},
        "******************************************" : { name: "abi",},
        "******************************************" : { name: "gfarm2" },
        
        "******************************************" : { name: "blok", max: 80000, cex:true },
        "******************************************" : { name: "champ", max: 12000 },
        /*
        "******************************************" : { name: "pbr", max: 1500 },
        "******************************************" : { name: "guru", max: 200 }, //太久没交易
        "******************************************" : { name: "mtlc", max: 3000 },
        "******************************************" : { name: "mot", max: 1000, ignore:true }, //多次亏本
        "******************************************" : { name: "orare", max: 1000 },
        "0x0bD49815EA8e2682220BCB41524c0dd10Ba71d41" : { name: "pym", max: 14000 },
        "0xe8377A076adAbb3F9838afB77Bee96Eac101ffB1" : { name: "msu" },
        "0x04b33078Ea1aEf29bf3fB29c6aB7B200C58ea126" : { name: "safle" },
        "0x7Dc47Cfb674bEb5827283F6140F635680A5cE992" : { name: "bolly" },
        "0x071AC29d569a47EbfFB9e57517F855Cb577DCc4C" : { name: "gfc" },
        "0x580A84C73811E1839F75d86d75d88cCa0c241fF4" : { name: "qi", cex:true },
        "0x835BA011Ec70e695879185dF3d46939DBfBeF7E5" : { name: "aabi" },
        "0x385Eeac5cB85A38A9a07A70c73e0a3271CfB54A7" : { name: "ghst", lazy: true }, //多搬砖机器人
        "0x300211Def2a644b036A9bdd3e58159bb2074d388" : { name: "ciotx" },
        "0x032F85b8FbF8540a92B986d953e4C3A61C76d39E" : { name: "tcp" },
        "0xa2CA40DBe72028D3Ac78B5250a8CB8c404e7Fb8C" : { name: "fear" },
        "0x5c4b7CCBF908E64F32e12c6650ec0C96d717f03F" : { name: "bnb", cex:true },
        "0xEe9801669C6138E84bD50dEB500827b776777d28" : { name: "o3", max: 4000 },
        "0x1441729568ab2A9871677edfeb13fBFCc7157a26" : { name: "prxy" },
        "0x2F800Db0fdb5223b3C3f354886d907A671414A7F" : { name: "bct" },

        "0xBbba073C31bF03b8ACf7c28EF0738DeCF3695683" : { name: "sand", ignore: true, cex:true }, //亏损
        "0xc6c855ad634dcdad23e64da71ba85b8c51e5ad7c" : { name: "ice", ignore: true, cex:true }, //亏损
        "0xb79a6c0571e72daa9d8383200cf2330d42aeadbe" : { name: "gringo"},
        "0x3b56a704c01d650147ade2b8cee594066b3f9421" : { name: "fyn"},
        "0xdd2af2e723547088d3846841fbdcc6a8093313d6" : { name: "gogo"},
        "0x228b5c21ac00155cf62c57bcc704c0da8187950b" : { name: "nxd" },
        "0xe1c110e1b1b4a1ded0caf3e42bfbdbb7b5d7ce1c" : { name: "elk" },
        "0xd0258a3fd00f38aa8090dfee343f10a9d4d30d3f" : { name: "voxel"},
        "0xefee2de82343be622dcb4e545f75a3b9f50c272d" : { name: "try"},
        "0xdf7837de1f2fa4631d716cf2502f8b230f1dcc32" : { name: "tel"},
        "0x430ef9263e76dae63c84292c3409d61c598e9682" : { name: "pyr", cex:true},

        "0x695FC8B80F344411F34bDbCb4E621aA69AdA384b" : { name: "nitro", cex:true },
        "0xaE204EE82E60829A5850FE291C10bF657AF1CF02" : { name: "aqr" },
        "0xA3c322Ad15218fBFAEd26bA7f616249f7705D945" : { name: "mv" },
        "0xe26cda27c13f4f87cFFc2F437C5900b27eBb5bbB" : { name: "rbls"},
        "0xb371248Dd0f9E4061ccf8850E9223Ca48Aa7CA4b" : { name: "hny"},
        "0x428aC1de3FC08c0F3A47745C964f7d677716981F" : { name: "ibz"},
        "0x561f7AE92a00C73DC48282d384e0A4FC1f4bC305" : { name: "reb"},
        "0xD3b71117E6C1558c1553305b44988cd944e97300" : { name: "yel"},
        "0x84342e932797FC62814189f01F0Fb05F52519708" : { name: "nht"},
        "0xd0cfd20E8bBDb7621B705a4FD61de2E80C2cD02F" : { name: "ssgtx"},
        "0x6c2B8f476f6774ea697Db4B0956b48Eb65C64bAE" : { name: "qus"},
        "0x4287F07CBE6954f9F0DecD91d0705C926d8d03A4" : { name: "trace"},
        "0x692AC1e363ae34b6B489148152b12e2785a3d8d6" : { name: "trade"},
        
        "0x7cC15fEf543f205Bf21018F038F591C6BaDa941c" : { name: "polycub" },
        "0xA1388e73c51B37383B1Ab9dCe8317eF5A0349CC5" : { name: "verse"},
        "0xE0339c80fFDE91F3e20494Df88d4206D86024cdF" : { name: "elon"},
        "0x428360b02C1269bc1c79fbC399ad31d58C1E8fdA" : { name: "defit"},
        "0x672255E73E9FcB8d8971b6e2622057bAa84B5Afe" : { name: "play"},
        "0x7FBc10850caE055B27039aF31bD258430e714c62" : { name: "ubt"},
        "0x5E430F88D1BE82EB3eF92b6fF06125168fD5DCf2" : { name: "moda"},
        "0xCD966B72CFF52Dc349089b6b6f5865B5743b4E08" : { name: "tomi"},
        "0x3068382885602FC0089aeC774944b5ad6123ae60" : { name: "pdshare"},
        "0xD52f6CA48882Be8fbaa98ce390db18e1dbe1062d" : { name: "ore"},
        "0xE6469Ba6D2fD6130788E0eA9C0a0515900563b59" : { name: "ust", cex:true},
        "0xE06Bd4F5aAc8D0aA337D13eC88dB6defC6eAEefE" : { name: "ixt"},
        "0xD1f9c58e33933a993A3891F8acFe05a68E1afC05" : { name: "sfl"},
        "0xf0f9D895aCa5c8678f706FB8216fa22957685A13" : { name: "rvlt"},
        "0x34d4ab47Bee066F361fA52d792e69AC7bD05ee23" : { name: "aurum"},

        "0xBe0cA422C616FcBAE6DCCFf974fc20CaB98327DA" : {name:"bmt"},
        "0x190CEC0657a02E9eAB1C1DF5d59f9139131cf539" : {name:"grvs"},
        */
    };
    routers = {
        "0xa5E0829CaCEd8fFDD4De3c43696c57F7D7A678ff" : {name: "quick"},
        "0x1b02dA8Cb0d097eB8D57A175b88c7D8b47997506" : {name: "sushi"},
        "0xf38a7A7Ac2D745E2204c13F824c00139DF831FFf" : {name: "elk"},
        //"0x68b3465833fb72a70ecdf485e0e4c7bd8665fc45" : "uniswapV3"
        "0xA102072A4C07F06EC3B4900FDC4C7B80b6c57429" : {name: "dfyn"},
        "0xC0788A3aD43d79aa53B09c2EaCc313A787d1d607" : {name: "ape", fee:99800},

        "0x10f4A785F458Bc144e3706575924889954946639": {name : "unknow1"},
        "0xdBe30E8742fBc44499EB31A19814429CECeFFaA0": {name : "unknow2", fee:99750},
        "0x711a119dCee9d076e9f4d680C6c8FD694DAaF68D": {name : "unknow3", fee:99750},
        "0x94930a328162957FF1dd48900aF67B5439336cBD": {name : "unknow4", fee:99760},
        "0xBCA055F25c3670fE0b1463e8d470585Fe15Ca819": {name : "unknow5"},
        "0x324Af1555Ea2b98114eCb852ed67c2B5821b455b": {name : "unknow6", fee:99820},
        "0xfE0E493564DB7Ae23a7b6Ea07F2C633Ee8f25f22": {name : "unknow7"},
        "0x93bcDc45f7e62f89a8e901DC4A0E2c6C427D9F25": {name : "unknow8",fee:99500},
        "0xE7bBd79a6B07E061F4228db52cAC14c5647E641A": {name : "unknow9",fee:99800},
        "0x0Ea86f859A1dF564cDa9A12A1a453de46fEf1217": {name : "unknow10",fee:99800},
        "0xAf877420786516FC6692372c209e0056169eebAf": {name : "unknow11",fee:99900},
        "0xaD340d0CD0B117B0140671E7cB39770e7675C848": {name : "unknow12"},

        "0x6AC823102CB347e1f5925C634B80a98A3aee7E03": {name : "unknow14",fee:99820},
        "0x03105929e82B1b8Fb6Eb76266CBd14C16a19D1f2": {name : "unknow16"},
        "0xfBE675868f00aE8145d6236232b11C44d910B24a": {name : "unknow17"},
        "0x57dE98135e8287F163c59cA4fF45f1341b680248": {name : "unknow19"},
        "0x15e4eb77713CD274472D95bDfcc7797F6a8C2D95": {name : "unknow20"},
        "0xe5C67Ba380FB2F70A47b489e94BCeD486bb8fB74": {name : "unknow21",fee:99800},
        "0x5f5acBd6c1c98D380AA12E614510BacF39Fbd531": {name : "unknow25"},
        "0x9055682E58C74fc8DdBFC55Ad2428aB1F96098Fc": {name : "unknow26",fee:99800},
        "0xb3eB6c96083F8a3407A3C6a011E807e152448ba0": {name : "unknow27"},
        "0x2BD8BdAd05d179BED8048830c10B03eaEa6E0E00": {name : "unknow29"},
        //"0x4237a813604bD6815430d55141EA2C24D4543e44": {name : "unknow30"},
        "0xD0b5335BE74480F9303B88f5B55ACD676598882A": {name : "unknow31"},
        "0x158d0b57Cc72509C3A9f476526887Ca8D7873fc4": {name : "unknow32",fee:99800},
        "0x270Ec6bE0C9D67370C2B247D5AF1CC0B7dED0d4a": {name : "unknow33"},
        "0xB80c1D8bF54F75a0ccCFfD4B277f6f37cD7241F4": {name : "unknow34",fee:99600},
        "0x1eEDFf2A89e58c5fC4650C2de45DDA0964C3D343": {name : "unknow35"},
        "0x1A9BCE072BE293036b9E1A30Ae363e2857B03b29": {name : "unknow37",fee:99750},
        "0x4DCa8E42634abdE1925ebB7f82AC29Ea00d34bA2": {name : "unknow38"},
        "0x6f5fE5Fef0186f7B27424679cbb17e45df6e2118": {name : "unknow39"},
        "0xD3B580c6653A36e09D4356448FF8714b6ffe9bdB": {name : "unknow40",fee:99850},
        "0xAaB112642A255DE389FE7DC6cFaD7732a0667e3B": {name : "unknow41"},
        "0xBd13225f0a45BEad8510267B4D6a7c78146Be459": {name : "unknow42"},
        "0x95DF772e09db393C024Efaec91bdDC4FBE680478": {name : "unknow43"},

        "0x751D346B92f3dce8813E6b6E248a11C534F4BdEa": { name: "unknow44"},
        "0xE236BC04B8A53A9C913D30Dc74547Bef33B60228": { name: "unknow45"},
        "0x5C6EC38fb0e2609672BDf628B1fD605A523E5923": { name: "unknow46",fee:99900},
        "0x9bc2152fD37b196C0Ff3C16f5533767c9A983971": { name: "unknow47"},

        "0xa5c17e5B45f22B15086c8d0246B98ebBC0edA05f": { name: "u49"}, //太多
        "0x8863f716706e9e4f13A52601A129DD1E1c3fA08B": { name: "u51"},
        "0x3a1D87f206D12415f5b0A33E786967680AAb4f6d": { name: "u52",fee:99800},
        "0x76d078d279355253b3c527f39bb7bf1cfED87628": { name: "u54"},
        "0xC02D3bbe950C4Bde21345c8c9Db58b7aF57C6668": { name: "u55"},
        "0x1D27711cfC94Fef1a0167a79EfE29Cc3D3486dCD": { name: "u56",fee:98000},
        "0x64e71E143aa724C66C038Ad287C0df23bf694080": { name: "u66"},
        "0xA274258b2d68Dca40955A2D5E7bb67756D30dB3e": { name: "u67",fee:99750},
        "0x7aE508d0148dc4d42d3523b00788139fA8436950": { name: "u70"},
        "0x3aB8a591d22147c7Ad2E2B2e166fc2E3A2041301": { name: "u72"},
        "0xB2855A6dAeeBDB72B0176A479A983066ae9775A6": { name: "u73"},
        "0xB245F2e2A694cDf08e69671125a2605803cAb006": { name: "u74"},
        "0x09Fd8B8ed6E30e583379Dc73b9261dF5E1A28b6F": { name: "u76"},
        "0xB6Feb0E3D88B9fb02EE4303c83aa74114CA46E56": { name: "u77"},
        "0x2fA91323d88DF750fA8b933e5325C484F5AD730A": { name: "u78",fee:99800},
        "0x4caCba24E733F5Ce108d23bBf9fE75516d929164": { name: "u79"},
        "0x0Aea21827CEDbf88F76C70E1f45A6A03D6590c79": { name: "u80"},
        "0xF677c11F16D60dc69819cb02A0A951FBaA06c5C2": { name: "u81"},
        "0x48C7976491Dab63af4b3ADd4EA7ccC0984E70cab": { name: "u82"},
        "0xA30621e53122977a5236FF7b10D896e3aaE6572E": { name: "u85",fee:99500},
        "0xF2eD4EE7f281A7b0C57755C5C330Fe13d77fd018": { name: "u86"},
        "0x9894B0F28CcfA0F5c5F74EAC88f161110C5F8027": { name: "u87"},
        "0x092667246c8De5b7e4Fcf7CB1005611311fBBe4A": { name: "u89"},
        "0x5D54689e49c5873dC87DD410dD780c940D17F466": { name: "u90"},
        "0x3d492a1Cf02112f201721544e13a5e239a5258d9": { name: "u91",fee:99800},
        "0x06e704DCad815747c57c6F0B48d457c95C4aFCe8": { name: "u93"},
        "0x0224085c86de47860269a790d9Ca83830e32B64a": { name: "u94"},
        "0xDbd37Ee4e1050608BDa4b952F9566CE29F70D7ff": { name: "u95",fee:99800},
        "0x9ad3f006b5D2f03fCCD2B9Fe2e47D3587B0BbA7A": { name: "u96"},
        "0x9C981B28E994b4498564064f8585D3c17F91fb14": { name: "u97"},
        "0x7225cA4511407B087bFc51C94E2fAe9C0aEc2e7f": { name: "u98"},
        "0x60913758635b54e6C9685f92201A5704eEe74748": { name: "u100"},
        "0x0962790b10e69137f70Ce48F0E8a65d98703B7AA": { name: "u102"},
        "0x56a20168571ef89F87Bb21f55daFb0413eaD8e79": { name: "u103",fee:99750},
        "0x4c8BCe2E8ba31Cf39476874189F21EC5B0E6F1f0": { name: "u109",fee:99800},
        "0x577cDab3113C134152907ea21f976f49359870E5": { name: "u110",fee:99750},
        "0x02e2aa08432b6FacAfa14f0639921189813F2fd6": { name: "u111"},
        "0xd13943E247f01F1950f390880A4D4D696A8e9e63": { name: "u114"},
        "0xCDF0078deC42c7aF6dEf5ab35B8503C80806a024": { name: "u120"},
        "0x1A409Ad4b8703f95F8e1aEe0A320496783E6e208": { name: "u130"},
        "0x49a3295B3cE22cF9b05a39094BCf466bD12de545": { name: "u131"},
        "0x70fc3329F554B0861341D1Be20c90589799b624F": { name: "u132"},
        "0xBc3E39A7DA290263a74338fd45Ff71F482AE123A": { name: "u135"},
        "0xbE75Dd16D029c6B32B7aD57A0FD9C1c20Dd2862e": { name: "u136"},
        "0xc6f8aa9AAcf3af8e4A3988eA109968bec06a4B9B": { name: "u137"},
        "0x1932d120E4cABeDb6b50f4ec269b86f33029Cdc9": { name: "u138"},
        "0x275eB6da9De810b2A3072d6Bbf4CD61f2269581E": { name: "u140"},
        "0x26FC5faAc90Fe0409b9b6a121bFa6A42aD38dfB8": { name: "u141"},
        "0xdF56FFD9022dF77d79EBBE094FeBAC9DA9e38AF2": { name: "u142"},
        "0x1041738e7c7696AFc08E3C05401aD504eE647aE4": { name: "u143",fee:99750},
        "0x5488A6Db5E6DE0ffAF58588213580220bD197115": { name: "u145"},
        "0x6651642FFBaAa0aaE2443F2eD4112fc54745A4A3": { name: "u147"},
        "0x7b4538EF4F6Fc75Ee8E9FDB872313fA6701f17a0": { name: "u150"},
        "0xA80e82bD8b7cd31776Fa63f67e96E9355A764FE7": { name: "u151",fee:99800},
        "0x193919f422a262b1EDFd8cA2776b8CfB000BdD3d": { name: "u152"},
        "0xb92F245cFf9C0e403f228C6d651090f1eB0a9CD8": { name: "u153"},
        "0x4Af72C5c1770D7caF4f2A66A48aB4Fd9A665Ea5b": { name: "u154"},
        "0x3Aef5C9188AE37D6d8bbb3777F4d95fb7cdbF57A": { name: "u155",fee:99800},
        "0xE52D0337904D4D0519EF7487e707268E1DB6495F": { name: "u156"},
        "0x0c8661F4165bdfC9573081AA3Dae222a007966bD": { name: "u159"},
        "0xd618716A6E134a761FACB8762336FE80580Da614": { name: "u160",fee:99000},
        "0x9e2443e6E0940b8eECbd017F82d0c220E9BBE9D0": { name: "u162"},
        "0xA09f2F015A796f3B7526BF936Fdd00078CEE715e": { name: "u163"},
        "0x81B4AcBDF3eFB7d9023D4CfeD245066f6a40D29e": { name: "u164"},
        "0xf68adD490bCa152455436f74394B1FF72BDFb822": { name: "u165"},
        "0x33dc8Be5d2Dc8EBC5a56Da8F78F8E5991Ecd4Cd8": { name: "u169",fee:99900},
        "0x34Afe9963285Cc373d911A14B597f6C6331b1110": { name: "u171"},
        "0xf9FaE96C83e9b60d0854db4a13dC1B8bB2b37048": { name: "u172"},
        "0x816c286E1A38ad3C7d656AB9304b2DC8Caa8ac72": { name: "u175",fee:99750},
        "0xE14df7cAe54555d038c4A441938e3Aaa0A0609a0": { name: "u178",fee:99950},
        "0xb2984A7822F1B4Cb9fcc03e0638c2Ca8c9B65031": { name: "u179"},
        "0x7086a416c2b23B2c26C2dEf02e422Cda770BFf1e": { name: "u180"},
        "0x85B5cc3ec95AE5D0b02E7c17e53F97C4B02a78e4": { name: "kilma"},
        "0xd47A4dBF580FB2C7D745aEDa3C9Ed1BF9AF0d5f1": { name: "o3"},
        "0x7E5E5957De93D00c352dF75159FbC37d5935f8bF": { name: "meerkat"},
        "0x844FA82f1E54824655470970F7004Dd90546bB28": { name: "twindex"},
        "0x1Bc01517Bda7135B00d629B61DCe41F7AF070C53": {name: "u181"},


    }
    gasWatch = [
        "0x75aD24286C0069236FE15F7C36B84c6990D6d234", //usdt
        "0x0d0106968749c0cE73FF5ef82736012AEcfD55da", //wmatic
        "0xd8478A963d4E36625d525EffFb748A10a60177b8",
        "******************************************", //usdt 75000
        "0xDe39FeFF64bE58B363e7b0DD417F353C8dE66729",//usdc
        "0x73D1044688C6e3D95C926c6D0c0A1cD7C165D90e", //usdc
        "0x24C16c41e09AAA194672ca3aE6f1171A9A8e9e9c", //dai
        "******************************************", //usdc 15w大户

        "******************************************", //wmatic
        "******************************************", //dai
        "******************************************", //wmatic
        "******************************************", //usdt

        "******************************************", //usdc ******************************************
        "******************************************", //weth
        "******************************************",
        "******************************************",
        "******************************************",

        "******************************************", //2号usdc
    ]

    blackListPump = [
        "******************************************",
        "******************************************"
    ]

}