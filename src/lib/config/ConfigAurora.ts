import Config from "./Config";

export default class ConfigAurora extends Config {
    mainnet = {
        data : "",
        // wss://mainnet.aurora.dev //没有订阅
        // https://mainnet.aurora.dev/84eLCvMaVG3MDML37uFLrwNBYaBJ9emMB1ghhacqcjQ
        wss : ["wss://mainnet.aurora.dev", "https://mainnet.aurora.dev/84eLCvMaVG3MDML37uFLrwNBYaBJ9emMB1ghhacqcjQ"],
    };
    gasMin = 0.05;
    gasDelta = 0.00001;
    gasMax = 1;

    feedMin = 0.0008;
    feedMax = 0.001;
    feedPumpMulti = 1; //pump的填充gas百分比
    feedAutoTimeHour = 6;

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************", "******************************************"];

    tokens = {
        "******************************************" : {name:"weth",  ignore:true, price:2000},
        "******************************************" : {name:"usdc.e", ignore:true},
        "******************************************" : {name:"usdt.e", ignore:true},
        "******************************************" : {name:"wnear", ignore:true, price:2.5},

        "******************************************" : {name:"atUst", ignore:true},
        
    };
    routers = {
        "******************************************" : {name: "tri"},
        "******************************************" : {name: "wanner"},
        "******************************************" : {name: "aurora"},
        "******************************************" : {name: "pad"},
        "******************************************" : {name: "mind"},
        //"******************************************" : {name: "izi"},
    };
    bot = {
        active : false,
        address : "******************************************", //support v1
    };

    trash = { active:true, rewardMin:0.002, delay:0, gasMin:0 }

    gasWatch = [
        "0x056fd8043eFFECD35d9E01296fE72a359C896992",
        "0xC162b76204B7Feb65aF7Da67C9bdf9311686bbeC",
        "0x0f5e1cae3839e22274d3bd7d19c326a10ffa4f49",
        "0x0e1ff2e33e8f909f1b4520fdf60e7d85177a4c64",
        "0x9034e89bc502cd453dc1b8dc5f8de259b2f9ad16"
    ]
}