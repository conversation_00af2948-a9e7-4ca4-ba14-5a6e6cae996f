import Config from "./Config";

export default class ConfigPom extends Config {
    mainnet = {
        data : "https://rpc.pomchain.io",
        wss : ["ws://127.0.0.1:3546", "https://rpc.pomchain.io"],
        //wss : ["https://mainnet-rpc.memescan.io"]
    };
    blockTime = 10;
    eth = "******************************************";
    stableTokens = ["******************************************"];

    gasMin = 1.01;
    gasMax = 20000;
    feedAutoTimeHour = 6;
    
    feedMin = 3.5;
    feedMax = 4;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxNoPendingBlock = 300;

    bot = { 
        active:true,
        crazy:false, 
        //address : "******************************************",
        //address : "******************************************", //pump fee
        //address : "******************************************", //pump fee2
        //address : "******************************************", //pump fee3
        //address : "******************************************", //new pair checker
        //address : "******************************************", //swap with fee, support v1
        //address : "******************************************", //test pair
        address : "******************************************", //testPairPatch
    }
    pump = {active:true, gasMax:200, countMin:2, countMax:2, rewardMin:0.00001}
    trash = {active:true, bid:true, delay:500, rewardMin:0.00001}

    tokens = {
        "******************************************" : {name:"wpom", ignore:true, keep:10000, price:0.003},
        //"0x27D789b89A441cCB6FCDB92650f1e667C965df7A" : {name:"pusd.e" },
        //"0x326d041519E24fE843ce313CFB8D0F07F653B2C7" : {name:"usdt" },
        //"0xE05f9F92F4CA96779456f7ba425b003838337d0b" : {name:"meme" },
    };

    routers = {
        "0x5322d6ed110c2990813e8168ae882112e64370ec" : {name: "pom"},
        "0x3ed87c90ce454b23c6b4e5a907f68ca04785800c" : {name : "wiz", fee:99800},
        "0x1b3813ac0863afff2b4e8716fcfeb5bf382b1dd1" : {name: "meme"},
        "0xc83a02a20df802c7634b8703eb699b989bb468b3" : {name: "u1"},
    };

    gasWatch = [
        "0x69716b283fdb4601f12025e8f627329d11c8acbb", //搬砖
        "0xeB627b2CC8dF168f36D58976e8204eFe1a9cd63A", //残渣
        "0x69716B283fdb4601f12025e8f627329d11c8acbB", //搬砖
        "0xc9321702874FCF6595cBd9F01462fAAf6f6b8663", //搬砖
        "0x530C64F125DfbCb456E3A13f27828C38024AaFDD", //搬砖
        "0x8f93f3277A0790bC63b79D039af948ddB38465B2", //残渣
        "0x86aAAa7fAeD7C1999BD1BF6C601C0E03F439bD07", //搬砖
    ]
    whiteListPair = {
        /*
        "******************************************" : {fee0:8000, fee1:0}, //POMG-WPOM
        "******************************************" : {fee0:8000, fee1:0}, //POMG-WPOM
        "******************************************" : {fee0:5000, fee1:0}, //POMG-MEMETIC
        "******************************************" : {fee0:0,    fee1:8000}, //PUSD.e-POMG
        "******************************************" : {fee0:3000, fee1:0}, //21BTC - USDT
        "******************************************" : {fee0:3000, fee1:0}, //21BTC - WPOM
        "******************************************" : {fee0:5000, fee1:0}, //BEE - Meme
        "******************************************" : {fee0:5000, fee1:0}, //BEE - WPOM

        //"******************************************" : {fee0:0, fee1:99699}, //PBUILD-MEMETIC
        //"******************************************" : {fee0:99699, fee1:80000}, //PBUILD-WPOM

        "******************************************" : {fee0:5990, fee1:0}, //MS-WPOM
        "******************************************" : {fee0:5990, fee1:0}, //MS-WPOM
        "******************************************" : {fee0:5990, fee1:0}, //MS-WPOM
        "******************************************" : {fee0:5970, fee1:0}, ////MS-MEMETIC
        "******************************************" : {fee0:6000, fee1:4000}, //MS-WIZT

        "******************************************" : {fee0:5000, fee1:0}, //PBUILD-WPOM
        //"******************************************" : {fee0:5000, fee1:0}, //PBUILD-WPOM //blacklist
        "0x73Ea4A13756f0Aec44D8034E10D016882E0b692F" : {fee0:5000, fee1:0}, //PBUILD-MEMETIC
        //"0x1B71e73d4508fE5eAb304b302a011656f59c29f5" : {fee0:5000, fee1:0}, //PBUILD-MEMETIC //blacklist

        "0xc847D33788a5F1Ff7f231D91a6Eb1C7Dc0503cFA" : {fee0:2090, fee1:0}, //POMV-WPOM
        "0xD51791b6F6d8218CC5e31cba3041582e6Ee40eD0" : {fee0:2090, fee1:0}, //POMV-MEMETIC

        "0x5791b2f6c7210A35C7Dfad1369d849788b9C32F4" : {fee0:0, fee1:4000}, //WPOM-WIZT
        "0x59bd77F29633b7365cC3B163d9B4Da91De446d0e" : {fee0:0, fee1:4000}, //WPOM-WIZT
        "0x731ce9190ebe73c250596066876620132b652e4d" : {fee0:0, fee1:4000}, //WizUSDT-WIZT
        "0xF6C9Af916690107fd4F44070939BeC8F34782086" : {fee0:0, fee1:4000}, //WizUSDC-WIZT
        
        "0x51B533eCe71a23F1e08cDa377b289470C6c37e6e" : {fee0:0, fee1:4130}, //WPOM-LOTTERY
        "******************************************" : {fee0:4130, fee1:0}, //LOTTERY-MEMETIC

        "******************************************" : {fee0:3150, fee1:0}, //POMQ-WPOM
        "******************************************" : {fee0:3150, fee1:0}, //POMQ-MAGIC

        "******************************************" : {fee0:5000, fee1:0}, //PBL-WPOM
        "******************************************" : {fee0:3000, fee1:5000}, //21BTC-PBL

        "******************************************" : {fee0:4000, fee1:0}, //WWT-WPOM
        "******************************************" : {fee0:4000, fee1:0}, //WWT-MEMETIC

        "******************************************" : {fee0:8000, fee1:0}, //QUACK-WPOM
        "******************************************" : {fee0:8000, fee1:5000}, //QUACK-BEE
        "******************************************" : {fee0:8000, fee1:5000}, //QUACK-DAO BY BEE
        "******************************************" : {fee0:5000, fee1:0}, //DAO BY BEE-WPOM
        "******************************************" : {fee0:5000, fee1:5000}, //INFINITY-QUACK
        "******************************************" : {fee0:5000, fee1:0}, //INFINITY-WPOM

        "******************************************" : {fee0:4000, fee1:0}, //UWU-MEMETIC
        "******************************************" : {fee0:4000, fee1:0}, //UWU-MEMETIC

        "******************************************" : {fee0:4000, fee1:0}, //MD-WPOM
        "0xF5f5FDF766de5283e02001BE18C92cD178b9582C" : {fee0:4000, fee1:0}, //MD-WPOM

        "0x1A6560F4669C19a0f5090ec79975c7A2239f3B12" : {fee0:0, fee1:1000}, //WPOM-MEMETIC

        "0x58E5B9e419d9e90688F8F58828AA29F78A2a59a2" : {fee0:4000, fee1:0}, //SDC-WPOM
        "0x2c0b06032f99564310c9182a0428e31a48de4a05" : {fee0:5150, fee1:0}, //SDC-WPOM
        "0xa3bb56684e7d9f126b7dff111584cf269b02636c" : {fee0:4000, fee1:4000}, //SDC-WIZT

        //"0xb70Cac5bEb5ba74d6dB52d9995f53f3eBe753676" : {fee0:70000, fee1:0}, //POMD-WPOM
        //"0x5Fbec963cF4827649A0DB206A8592E344A96CA82" : {fee0:70000, fee1:0}, //POMD-MEMETIC
        */
    }

    blackList = [
        "0x5D2238753F3ca5E649f9250C303d5c196A069F24"
    ]
    blackListPump = [
        "0x5D2238753F3ca5E649f9250C303d5c196A069F24"
    ]
}