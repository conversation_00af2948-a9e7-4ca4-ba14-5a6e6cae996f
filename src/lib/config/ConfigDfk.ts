import Config from "./Config";

export default class ConfigDfk extends Config {
    mainnet = {
        data : "",
        // https://subnets.avax.network/defi-kingdoms/dfk-chain/rpc
        // wss://subnets.avax.network/defi-kingdoms/dfk-chain/ws
        // https://avax-dfk.gateway.pokt.network/v1/lb/6244818c00b9f0003ad1b619/ext/bc/q2aTwKuyzgs8pynF7UXBZCU7DejbZbZ6EUyHr3JQzYgwNPUPi/rpc
        wss : ["ws://127.0.0.1:9650/ext/bc/q2aTwKuyzgs8pynF7UXBZCU7DejbZbZ6EUyHr3JQzYgwNPUPi/ws", "b|wss://subnets.avax.network/defi-kingdoms/dfk-chain/ws"],
        //wss : ["wss://subnets.avax.network/defi-kingdoms/dfk-chain/ws"],
    };

    eth = "******************************************";
    stableTokens = ["******************************************"];
    stablePumpToken = "******************************************";
    onlyActivePair = true;

    gasMin = 0.06;
    gasDelta = 0.00001;
    gasMax = 30000;

    bot = {
        active : false,
        //address : "******************************************",
        address : "******************************************",
    };

    pump = {
        active: true,
        greed: true,
        gasMax :30000,
        countMin:1,
        countMax:1,
    }

    tokens = {
        "******************************************" : {name:"wjewel", ignore:true, price:0.15},

    };
    routers = {
        "******************************************" : {name: "dfk"},
    };
}