import Config from "./Config";

export default class Config<PERSON><PERSON> extends Config {

    mainnet = {
        wss : ["wss://api.wanchain.org:8443/ws/v3/fac50d0597303148fed7f02149ed2e0480e9050bf221d534843a2c3890e01e3a"],
        data : "https://evmexplorer.velas.com/rpc",
    };
    eth = "******************************************";
    stableTokens = ["******************************************","******************************************","******************************************"];

    tokens = {
        "******************************************" : { name: "wwan" },
        "******************************************" : { name: "wanUsdt" },
        "******************************************" : { name: "wanUsdc" },
    };
    routers = {
        "******************************************" : {name: "wan"},
    };

}