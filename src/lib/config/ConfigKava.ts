import { type } from "os";
import { macro } from "../macro";
import Config from "./Config";

export default class ConfigKava extends Config {
    mainnet = {
        data : "https://evm.kava.io",
        //ws://127.0.0.1:8546
        //aws-vg 7ms
        wss : ["wss://wevm.kava.chainstacklabs.com", "wss://wevm.kava.io"],
        //wss : ["wss://wevm2.kava.io", "wss://wevm.kava.io"] //aws-vg
    };
    blockTime = 6;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 1;
    gasDelta = 0.0001;
    gasMax = 1000;
    feedAutoTimeHour = 0.1; //单位小时
    onlyActivePair = false;

    feedMin = 0.15;
    feedMax = 0.2;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 10;
    securityLevel = 10;

    bot = {
        active:true,
        crazy:false,
        address : "******************************************", //0412 + 0715
        gasMax : 1000,
    }
    pump = {active:true, gasMax:1000, countMin:1, countMax:3, rewardMin: 0.002}
    trash = {active:true, bid:true, delay:1, rewardMin: 0.1}


    tokens = {
        "******************************************" : {name:"wkava", ignore:true, keep:100, price:0.9},
    };
    tokenBlackList = {}

    routers = {
        "******************************************" : { name: "equ", type: macro.ROUTER_TYPE.V2_KAVA_EQU },
        "0x4310ed61E7E4fd50C2b44C92725C087abeB632a2" : { name: "surf", fee:99500 },
        "0x7a2c1D96C76B6EB62241df4d2fAEb9F0D3D59E10" : { name: "elk" }
    
    };

    gasWatch = [
    ]
    whiteListPair = {
        //"0x7d8100072ba0e4da8dc6bd258859a5dc1a452e05" : {fee0:1000, fee1:1000}, //test
    }
    blackListPair = [];

    blackList = [
    ]

    blackListPump = [
    ]
}