import Config from "./Config";

export default class ConfigCube extends Config {

    mainnet = {
        data : "",
        /*
            https://http-mainnet.cube.network
            wss://ws-mainnet.cube.network

            https://http-mainnet-sg.cube.network 
            wss://ws-mainnet-sg.cube.network

            https://http-mainnet-us.cube.network
            wss://ws-mainnet-us.cube.network
        */
       wss : ["wss://ws-mainnet-sg.cube.network", "wss://ws-mainnet-sg.cube.network", "wss://ws-mainnet-sg.cube.network"],
       //wss : ["wss://ws-mainnet-us.cube.network", "wss://ws-mainnet-us.cube.network", "wss://ws-mainnet-us.cube.network"],
       //wss : ["wss://ws-mainnet.cube.network", "wss://ws-mainnet.cube.network", "wss://ws-mainnet.cube.network"],
    };
    eth = "******************************************";
    stableTokens = ["******************************************"];
    stablePumpToken = "******************************************";
    blockTime = 3;
    gasMin = 200;
    gasDelta = 0.0123;
    gasMax = 2000.1;

    bot = {
        active : false,
        address : "******************************************",
    };

    pump = {
        active:true,
        greed:false,
        gasMax :488.1,
        countMin:1,
        countMax:1,
    }

    tokens = {
        "******************************************" : { name: "wcube", ignore:true, price:0.33 },
        "******************************************" : { name: "usdt", ignore:true },
    }
    routers = {
        "******************************************" : {name: "corn"}
    };

    blackList = [];

    blackListPump = [
        "******************************************",
        "******************************************",

        "0x9e22d64a4875BcFb9615D2a230B899743042d4D8",
        "0x4BF07Ec321ae49Da0dF0dd3D996Cad4a3A04410B",
        "0x069A0829559F487dBA89C98f63F1C366d49f6829",

        "0xCf1978cf90B629C23A6d59649faede6Cd7E5D28f",
        "0x88888888837bCA397BA1bef8bc6bCDfd00Cc2C3E",
        "0x2f1178bd9596ab649014441dDB83c2f240B5527C",
        "0x49a46d5e239e5F5C42416A196AE4a4BB408e180e",

        "0x761d5F26337e3f048Ce6fcc163487399d49cF593",
        "0x88b0125E1de94A98dD1F9ad6459Ca6965A43E8AE", //fake
        "0xc17d92D70B4ad884687292131b7ecBDCa3068888", //fake

        "0x94Bb8Bab56F4e235d51b9e1221ee514B9e8796e3", //usdt-ht
        "0x4f6594ece20eeb14997f27de3f7ee28a288654fa",
    ];

}