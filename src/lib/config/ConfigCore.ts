import { macro } from "../macro";
import Config from "./Config";

export default class ConfigCore extends Config {
    mainnet = {
        //wss://ws.coredao.org la:60ms ir:121ms de:140ms vg:17ms, 可能还有更快的美国区
        data : "https://rpc.coredao.org",
        //wss : ["https://rpc.coredao.org"]
        //data : "https://rpc-core.icecreamswap.com",
        //wss : ["http://127.0.0.1:8579"],
        //wss : ["wss://ws.coredao.org"],
        wss : ["wss://ws.coredao.org"],
        //wss : ["https://rpc.ankr.com/core"]
        //wss : ["https://rpc-core.icecreamswap.com"]
    };

    eth = "******************************************";
    stableTokens = [
        "******************************************",
        "******************************************",
                    //"******************************************",
                    //"******************************************",
                    //"******************************************",
                    //"******************************************",
                    ];

    blockTime = 3; //每个区块的时间

    gasMin = 1;
    gasDelta = 0.0200012;
    gasMax = 10000;

    feedMin = 4.5;
    feedMax = 5;
    feedPumpMulti = 0.8; //pump的填充gas百分比
    feedAutoTimeHour = 1;

    bot = {
        active : false,
        crazy: false,
        //address : "******************************************", //swap with fee, uniswapv1, test pair, pump2, pumpSafe, testPairPatch
        //address : "******************************************", //0412+0715
        address : "0x0a271ce4171ded56620c974363E0f2189CA7ab97", //1115
        sharingan: true,
    };

    pump = {
        active:true,
        gasMax :10000.2,
        countMin:3,
        countMax:3,
        rewardMin:0.005,
    }

    trash = {active:true, rewardMin:0.005, bid:true, delay:500}

    tokens = {
        "******************************************" : {name:"usdt.bsc", ignore:true, keep:5, limit:300, eq:["******************************************", "******************************************"]}, //d6
        "******************************************" : {name:"usdt.lfg", ignore:true, keep:5, limit:300, eq:["******************************************", "******************************************"]}, //d6
        "******************************************" : {name:"usdc.bsc", ignore:true, keep:5, limit:300, eq:["******************************************", "******************************************"]}, //d6
        
        "******************************************" : {name:"wcore",       isEth:true, ignore:true, price:0.8, keep:50, limit:2500, eq:["******************************************"]}, //d6
        "******************************************" : {name:"wcore.shdw",  isEth:true, ignore:true, price:0.8, keep:50, limit:2500, eq:["******************************************"]}, //d6
        "******************************************" : {name:"wcore.fruit", isEth:true, ignore:true, price:0.8, keep:50, },
        
        "******************************************" : {name:"wcore.u3", ignore:true, price:0.6},
        "******************************************" : {name:"usdt.ice", ignore:true, keep:200, limit:300}, // eq:["******************************************"]}, //18位, 精度必须相同

        //"******************************************" : {name: "ice", maxLp:2},
        "******************************************" : {name: "shaw"},
        "******************************************" : {name: "cid" },
        //"******************************************" : {name: "cpt", maxLp:2},
        "******************************************" : {name: "cuan" },
        "******************************************" : {name: "bow" },
        "******************************************" : {name: "rice" },
        "******************************************" : {name: "woof" },
        "******************************************" : {name: "xrice"},
        "******************************************" : {name: "miidas"},
        "******************************************" : {name: "crust"},
        "******************************************" : {name: "apple"},
        "******************************************" : {name: "acore"},
        "******************************************" : {name: "coy", maxLp:1},
    };
    routers = {
        "******************************************" : { name:"glyph", eth : "******************************************"},
        "******************************************" : {name: "cdao", fee:99750, type:macro.ROUTER_TYPE.V2_ONLY_ROUTER}, //factory:******************************************
        "******************************************" : {name: "coreswap", fee:99750}, //******************************************
        "******************************************" : {name: "ice"},
        //"******************************************" : {name: "ice_multi"}, //AkkaRouter
        "******************************************" : {name: "lfg"},
        "******************************************" : {name: "shdw", fee:99750, eth:"******************************************"},
        "******************************************" : {name: "u3", eth:"******************************************"},
        "******************************************" : {name: "u4"},
        "******************************************" : {name: "arch",  fee:99800},
        "******************************************" : {name: "wiz",   fee:99800},
        "******************************************" : {name: "cuan",  fee:99750},
        "******************************************" : {name: "u7"},
        "******************************************" : {name: "u13"},
        "******************************************" : {name: "u14"},
        "******************************************" : {name: "u15"},
        "******************************************" : {name: "u16"},
        "******************************************" : {name: "u18"},
        "******************************************" : {name: "u19"},
        "******************************************" : {name: "u20"},
        "******************************************" : {name: "u21"},
        "******************************************" : {name: "arch2", fee:99800},
        "******************************************" : {name: "lfg2"},
        "******************************************" : {name: "u23", fee:99800},
        "******************************************" : {name:"rice", fee:99750},
        "******************************************" : {name:"cdao3", fee:99750, eth:"******************************************"},
        "******************************************" : {name:"shazam", eth:"******************************************", type:macro.ROUTER_TYPE.V2_STABLE},
        "******************************************" : {name:"palma_t", fee:99750},
        "******************************************" : {name:"palma", fee:99750},
        "******************************************" : {name:"iron"},

        //"******************************************" : {name: "nsk"}, //v3
        "******************************************": {name: "u28",  fee:99700},
        "******************************************" : {name: "u24", fee:99750, eth:"******************************************"},
        
        "******************************************" : {name: "u26", fee:99750, eth:"******************************************"},
        "******************************************" : {name: "u27"},
        "******************************************" : {name: "eco"},
        "******************************************" : {name: "apple", fee:99750},
        "******************************************" : {name: "bee"},
        "******************************************" : {name: "cdao4", fee:99750, eth:"******************************************"},
        "******************************************" : {name: "bunny", fee:99750, eth:"******************************************"},
        "******************************************" : {name: "u28", eth:"******************************************"},
        //"******************************************" : {name: "sat"}, //跑路了
        "******************************************" : {name: "coy"},
        "******************************************" : {name: "crest", fee:99750},
        "******************************************" : {name: "btcc", fee:99750 },
        "******************************************" : {name: "spoon", type:macro.ROUTER_TYPE.V2_STABLE, stableFee:99980, volatileFee:99800},

        "******************************************" : { name: "u30", },
        "******************************************" : { name: "u31", fee:99750},
        "******************************************" : { name: "u32", fee:99750},
        "******************************************" : { name: "u33", fee:99750},
        "******************************************" : { name: "u34", },
        "******************************************" : { name: "u35", },
        "******************************************" : { name: "u36", fee:99750},
        "******************************************" : { name: "u37", fee:99750},
        "******************************************" : { name: "u38", fee:99750},
        "******************************************" : { name: "u39", fee:99750},
        "******************************************" : { name: "kyw", fee:99750},
        "******************************************" : { name: "u41", },
        "******************************************" : { name: "u42", },
        "******************************************" : { name: "u43", fee:99750, eth:"******************************************"},
        "0xbfd8b193db621543668dadef489b9a3eaca82d32" : { name: "u44", fee:99750, eth:"******************************************"},
        "0xc4217d38cedcf40ab89f653e507c87d48c996cbc" : { name: "u45", },
        //"******************************************" : { name: "nsk", },
        "0xE9bA194995E9286a94c61F6436EdaE368810543b" : { name:"angle", fee:99750},
        "0x0dA1e442397A378D7B99Cb570CDC4cB745F966b5" : { name:"woof", fee:99750},
        "******************************************" : { name:"woof2", fee:99750},
        "******************************************" : { name:"openx", fee:99750},
        "******************************************" : { name:"glyph", eth:"******************************************"},
        "******************************************" : { name:"u46", },
        "******************************************" : { name:"u47", },
         
    };

    whiteListMev = {
        //"******************************************,******************************************,******************************************" : true, //(wcore.shdw) WCORE -> YFI -> COY -> WCORE //反过来可以正确跑，强制设置成可跑，在黑名单里面过滤掉
    };

    whiteListPair = {
        //****************************************** usdt-core
        //****************************************** wcore-iceπ
        //****************************************** wcore-wcore

        "******************************************" : {fee0:0, fee1:980},  //WCORE-LFG

        //"******************************************" : {fee0:0, fee1:50},   //COY-YFI
        "******************************************" : {fee0:0, fee1:5050}, //WCORE-YFI

        /*
        "0x762172d20b593752ad9b93b8cbacacc0b6c297eb" : {fee0:0, fee1:5052}, //WCORE-NUWA
        "0x081595ae3d893f0ff031728542a8103e96f1ccf3" : {fee0:0, fee1:5052}, //WCORE-NUWA
        "0xaa0015d4771c9c76de34c6de7444c09fac9c33b1" : {fee0:0, fee1:5052}, //USDT-NUWA
        "0x4b97A643FeeAcdbA111De373FFc03e96eC2F6128"  : {fee0:5052, fee1:0}, //NUWA-LFG

        
        "0x77d1e8bfcb738039cbfe70390e8e9c9b28829167" : {fee0:4000, fee1:0}, //CoreShib-USDT
        
        "0xc7cfd6881c4d23dd8323d2e87baa51ceb3f01239" : {fee0:0, fee1:4000}, //WCORE-CoreShib
        "0xdece1d78f4a8613d2cd14b12647cb5e51adec592" : {fee0:0, fee1:4000}, //Coreinu-CoreShib
        "0x43b296b9dc382948a1684ecd42fbfb4e9a3cfca2" : {fee0:4000, fee1:0}, //CoreShib-BCR
        "0x1c696b1b16a823d2588d48a3430f43ec117ef5dd" : {fee0:4000, fee1:0}, //CoreShib-ICE
        "0xeeae479349dc065fd433bd8eecd7a9555f300cb0" : {fee0:4000, fee1:0}, //CoreShib-YPC
        "0xef2a967886655c8d946e1ccef823659ca706a12a" : {fee0:0, fee1:4000}, //HUC-CoreShib
        
        
        "0xbF73635D7c1F2F365288bC60899330f1D8d7429F" : {fee0:0, fee1:7000}, //WCORE-CoreWizSwap //有间隔时间
        "0xBb255ed973FDd2Ab9e4beB55104787D8C47E5683" : {fee0:0, fee1:7000}, //WCORE-CoreWizSwap //有间隔时间

        "0x60f7c8d1c87849ac598660178ae56777f2fc3ef0" : {fee0:0, fee1:6000}, //HUC-AIINU //测试是0

        "0x9f8EB8681950C6276fF1319279889249DCc2C172" : {fee0:0, fee1:6000}, //WCORE-AIINU //测试是0
        "0x1907006ce592091866088Fbad2A74E31DBA897b4" : {fee0:6000, fee1:0}, //AIINU-USDT //测试是0
        "0xF2621D0BBA36152C073075fF2B0f89a6d655FF4B" : {fee0:0, fee1:6000}, //Coreinu-AIINU //测试是0
        
        "0x5f09704B9b80e58DE9d9936e48F5fc842528FCcC" : {fee0:8000, fee1:0}, //FRY-LFG

        "0x8e8abeefe8ccfccb8bf2725be2ec9f5b3ac7ad13" : {fee0:3500, fee1:0}, //HKC-WCORE
        "0xa2a87fb4a3b82d6da116a3c943cdf2dc58c989cc" : {fee0:3500, fee1:0}, //HKC-Coreinu

        "0xb413f81894ba3e630c41746395021ba2753913f4" : {fee0:5000, fee1:0}, //CSHARKY-ICE
        "0xe5852cfbb9a2bc470ce22c2e5fcaebb2852a9931" : {fee0:5000, fee1:0}, //CSHARKY-LFG
        "0x70cc9447532fc4f0c8ac3c418b64f57cbbc2da31" : {fee0:0, fee1:5000}, //WCORE-CSHARKY

        "0x0a6350a65b127ddc2933d3b7741118ca44f0da7f" : {fee0:0, fee1:0}, //KUAI-LFG

        "0x4d88e2672f23F019A7c15719a703DcA2386562c2" : {fee0:0, fee1:0}, //CPT-Shdw
        "0xD4D3Fc590783c31aF8d4cEB50424522839DD8b73" : {fee0:0, fee1:0}, //WCORE-CPT
        "0x03d9ae04F5DA332026676402442618b8B8604277" : {fee0:0, fee1:0}, //WCORE-Shdw

        "0xD700e5B0E2E7c38aadb8D6668EB1321545d6b09f" : {fee0:10000, fee1:0}, //HAPPY-LFG
        "0x0307b5dEe76193fdfBcb98a11ABF3E2707d6BfF3" : {fee0:0, fee1:0}, //WCORE-HAPPY
        "0x019FB54b1caC102e1E929A759ca4d456789Bc537" : {fee0:0, fee1:0}, //USDT-HAPPY
        */

        //"0x8224438c5de9783659b52b6e097290106084865e" : {fee0:0, fee1:8000}, //WCORE-ROYALE
        /*
        "0x6932543112c90dfc265afb2918a6f2490deaf91f" : {fee0:0, fee1:8000}, //SCORE-ROYALE
        "0x0ed2334e2a134b7dc2e9fd2734fb8fb20679c60e" : {fee0:8000, fee1:0}, //ROYALE-ICE
        "0xb06d776712b3118637c067ce2c9814114bab8cd7" : {fee0:0, fee1:8000}, //USDT-ROYALE
        */

        /*
        "0x86b499cd0068657996213be3bdbc8fad491c4626" : {fee0:0, fee1:50000}, //WCORE-YFIDAO
        "0x020353028ad5dd8870b2320461753f8dd1d87d14" : {fee0:0, fee1:50000}, //FRY-YFIDAO
        "0xeec2d3487e829f5a8d636b54f82846a76d50ea7b" : {fee0:50000, fee1:0}, //YFIDAO-LFG
        */

        //"0x529a252b9b37f3c641f4af4899146920f23f0e72" : {fee0:0, fee1:90000}, //WCORE-SHGD
        //"0xf6b67218cc88786b06394ce26ddf4659219b725e" : {fee0:0, fee1:90000}, //WCORE-SHGD

        //"0xdd7d4cc57bacbbde3883bd4ca9073ac487ea9519" : {fee0:0, fee1:10000}, //WCORE-OSK //bad
        //"0xaeac2f098abc56f4135a46c79a3a864892fdf78e" : {fee0:10000, fee1:0}, //OSK-LFG //bad
    }

    blackList = [
        /*"0x30f377f7e58b460ae88d6655ed163c315a9c0109",
        "0x61972d902c8473c9bade44c627a098bef71f9f2b",
        "0x4666dfa4a8820f44ed89e7602037caffcfb75860",
        "0xe08cf5897800ed3df6cba2f1d499fb6571ca2e7c",
        "0xce68ce02c223523c1a6e4301a3ae3c427328becc", //假单
        "0x03c8d1e4e76e4da107220c0932428199929b5731", //假单
        "0x4e9412afb9197c2b0599afbecc8703d76a3b2a7f",
        "0xe91174b86ff22d982e508f07cf39e87f08793389",
        "0x51eb99cab124aa346d48525eff061b8512021730",
        "0x4a46a7b6fec46eabe3017ccdf52508ce050ee907",
        "0x159ca1fbdff7face72425727030c736e8d916d7a",
        "0x14b0fa3ca708999c7db237e184115841b6607980",
        "0xf5cbe97500098fe50afe96a1d5621f337c3fc146",
        "0xd20c14c3816062c0d825c729da3eb183b8a900f3",
        "******************************************",
        "0x0aefbaf0a5b531332afcd14f555b9d45db3985ac",
        "0xb5b8457b2e81eef591776f16117e12b3477a59c9",
        "0x9ac2ed6d4958c41485ef1cd4a042dc00d12663d9",
        "0xd1d8d0fbeb6a372442f5b138c48379281fc322e1",
        "0x4097e87d29b9589dcb01f75f072cf814b136c6b9",
        "0x897f98519faa2e5dae292d3b9ac73fab57b36fc7",
        "0x59b4934e7c67729784d3deb6086ac53f93766b14",
        "0x85a1528fbd61b78e6b0173960a17575afc438257",
        "0xe79ec33a21e85743bd9120b1645d02b157aef65a",
        "0x5dfa20b83ed9bbaf7a6cca98f6c0b2664e03e30b",
        "0x8043bc946753b762fb5cb2142ec211fab4c094fa",
        "0xd2b222bfa7538dd16c625cda879bc569c5910aa2",
        "0x1f8ded6d19f2df0711b7b222ce7e096c6ff873bd",
        */
       "0x280640A796A12c17F98DdcBf4849a7690136A057", //骗coy交易
    ]

    blackListPump = [
        /*"0xce68ce02c223523c1a6e4301a3ae3c427328becc"*/
    ]

    gasWatch = [
        "0xc3da629c518404860c8893a66ce3bb2e16bea6ec", //0xff040000 0xff030000 搬砖 收益: 0x31c798ac8159568d44c7f0c8678bf6fdc4caf57c
        "0xe268c2893ba808b6db3e155a64c37c33ad8cb9e6", //搬砖 收益: 0xa9d47958d143697ae64a2599dc1e6547b4fb8b21, 0x25de58c26e2b6e8c963a2a5be3340144571bb63f
        "0x1049ba351fca796f7df875f8bdf9324fb9338ee8", //搬砖+套娃
        "0x6aE9EFF149Eaac6daA6C344274Fef3b085B62a4E",
        "0x21a808b6e8f6929b9495ebda2bf86e04f64ca19c",
        "0x92010bb8c49f1e4051dac5303c91916dc9204d08", //0x4a6d0461
        "0xb57ba987e201393AbBc593473df9f608183Dfc20", //套娃+搬砖
        "0x310d184017ec1bd8d2cdffcb58ac65bd75bdc66b", //461
        "0xCB80AEDA5cA57A127aeA87429e88a23ac071aC06", //461
        "0x1049Ba351FcA796f7Df875F8BDF9324FB9338ee8",
        "0x47783857B875E88c061a168b39fa5dC949A26918", //套娃
        "0x5BF412263FC40559eCe92dBe37f04DEdE60e87c8", //反向套娃
        "0x4238e12dbbd467f50d04de240a29325ef629e0e4",
        "0x7693ddE33D5a5E2174d3DEbfE601B93e8885EffC",
        "0xbbb44584ca900a9D31Efcc35b187c46AA536d1c7",
        "0x0fB9d0707d40E4ced3676BFaF59c89Fb018F8D46", //套娃
        "0x2010c07f7b0C64BbBfE23D0733Cad3f0731c6988", //搬砖
        "0x78e65aA72AA0f58d89ec5EEE1978325952482801", //花式搬砖
        "0xde99e998e60c9e0086342a397a6c7b74191c9cea", //搬砖
        "0x702562E096673e55Eb318a82Fc4987519C28E88B", //套娃
        "0x20501A633aB246037d1b83549CFA44643151D731", //大户20231007
        "0xd6E1Ec333EFC4feFB4905096DaE72EfB6D5114ca", //大户2
        "0x7485F9bE0686f37daDa676F4fC488B8b888e7465",
        "0x4F90Bad6Ac7F7eE41bC2EBDbAE02bDB121C6369f", //套娃
        "0xC345B5E76337AE924123688F143b27c0e377dd51",
    ]
}