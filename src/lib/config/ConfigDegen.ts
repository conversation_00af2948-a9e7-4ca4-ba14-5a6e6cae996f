import Config from "./Config";

export default class ConfigDegen extends Config {
    mainnet = {
        //hk : 600ms
        //vg : 120ms
        //la : 80ms
        data : "https://rpc.degen.tips",
        //wss : ["wss://rpc.degen.tips"],
        wss : ["https://rpc.degen.tips"]
    };
    blockTime = 1;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 1.5;
    gasMax = 1000;
    gasDelta = 0.2;
    feedAutoTimeHour = 1;

    feedMin = 15;
    feedMax = 20;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 10;

    bot = {
        active:false,
        crazy:false,
        address : "******************************************", //1115, logic:******************************************
    }
    pump = {active:false, gasMax:1000, countMin:3, rewardMin: 0.000001, maxPump:15}
    trash = {active:true, bid:false, delay:0, rewardMin:0.000001}

    tokens = {
        "******************************************" : {name:"wdegen", ignore:true, keep:0.1, isEth:true, price:0.03},
    };

    routers = {
        "******************************************" : { name: "ds" },
        "******************************************" : { name: "dyod", fee:99750 },
        "******************************************" : { name: "frog", fee:99800},
        "******************************************" : { name: "quark"},
        //"******************************************" : { name: "twb"},
        //"******************************************" : { name:"twb2"},
        //"******************************************" : { name: "bdegen"},
        //"******************************************" : { name: "bdgen2"},
        "******************************************" : { name:"ddd" },
        //"******************************************" : { name:"bar"}
        //"******************************************" : { name: "astro" },
        //"******************************************" : { name: "www"},
        //"******************************************" : { name: "sbb" },
        //"0xf7E86fF3E39f09167A10788DbB24BC5Db8c681Ab" : { name: "alch" },
        //"0xc3DA629c518404860c8893a66cE3Bb2e16bea6eC" : { name: "dswap" },
        //"0xE7D1EEC5f2DC010a3050334943eAd2Bcd5b730eE" : { name:"lacost" },
        //"0xC5Fb9B3c59119FDab752dDe54B1D7d56780b6728" : { name:"saturn"},
        //"0xd7f8Bee968170C7CB4A3C9059ccFb75B6a3CEdF5" : { name:"pump" },
        //"0xc21e50A3aB2Df9c18aDa2A53d4d92Fa7c5939B88" : { name:"space" },
        //"0x0Bd6C9Fc6101EBAa10F6D383709be7F4488aBd02" : { name: "moon" },
        //"0xc2E0282e5A32409561B1bA960A760f59D0Eb6B5D" : { name:"ele"},
        //"0xEDf20D54Ac4cB2514186f0421767ab36004022aF" : { name:"racoon"},
        //"0x24928eA2aB66673B22ebfd276f1CA6A8C90aE648" : { name:"xi"},
        "0x187C28D22a3188D5d9Bf47515b9aa91456348857" : { name:"zenon" },
        "0x7828A69a05b0e5CCd43fB388241b6aeA05f04369" : { name:"u1"},
        "0x5D1c603E2E3D59973F2F391509076696aD8ed5bd" : { name:"u2"},
        "0xE7086bD8173A199e50a0457Ef8553FCd6b506eCD" : { name:"u3"},
        "0x4B1202640Ae13f8112f6f4E263c63089FA5E98E3" : { name:"u4"},
        "0x0767AD2C86d05fe4895e35F4377f48d52C167813" : {name:"u5"},
        "0xBb08b5f9b9b94C5cEe69e2FCAF5B0e412C7Ddb65" : {name:"u6"},
        "0x897e0396b8AD15680CAb5168B09e8B3306651149" : {name:"degenXV2"},
        //"0x9c0dF4b950ca19Db6fEC13ab79aD180a9C15a41E" : {name:"proxy"}, //v3
        //"0xB243ebf463473f1760d9AC3B547b99B37275270b" : {name:"balance"},

        //"0x3F85100AD95E8a2C2fA70Cc279E753644FEC5eB7" : {name:"dspirit"},
        //"0x31FEdB60cFF9De81D8137c41B3F6c9E9Bcad5A83" : {name:"dcircle"},
    };

    gasWatch = [
        "0x****************************************"
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}