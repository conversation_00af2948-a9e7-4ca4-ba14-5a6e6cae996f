import { macro } from "../macro";
import Config from "./Config";

export default class ConfigSei extends Config {
    mainnet = {
        data : "https://evm-rpc.sei-apis.com",
        wss : [
            "wss://evm-ws.sei-apis.com",
            //"wss://sei.drpc.org",
        ],
    };

    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 5;
    gasDelta = 0.12;
    gasMax = 10;
    blockTime = 1;
    maxOperator = 1;

    feedAutoTimeHour = 5;

    feedMin = 1;
    feedMax = 2;

    bot = {
        active : false,
        crazy : false,
        address : "******************************************", //1115
        sharingan : true,
    }
    pump = {
        active: false,
        rewardMin : 0.0001,
        gasMax :10000,
        countMin:3,
        countMax:7,
    }

    trash = {active:true, rewardMin:0.1, bid:false, delay:0}
    
    tokens = {
        "******************************************" : { name:"sei", ignore:true, cex:true, keep:1, price:0.5},
    };
    routers = {
        //"******************************************" : {name: "dragon", fee: 99700}, //v3
        "******************************************" : {name: "dragon"},
        "0x9f3B1c6b0CDDfE7ADAdd7aadf72273b38eFF0ebC" : {name: "yaka", type: macro.ROUTER_TYPE.V2_STABLE,stableFee:99960, volatileFee:99820},
        "0x5Cca43d5306CadC49B1227b9BBe5413786e2f85B" : {name: "port"},
        "0x6e8D0B4EBe31C334D53ff7EB08722a4941049070" : {name: "donke"}
    };
    gasWatch = [
        "0xc3DA629c518404860c8893a66cE3Bb2e16bea6eC",
        "0xe81bec6795e3A61341221F0A4D5C2cdFbE6a2Af5"
    ]
    blackList = [
    ]
}