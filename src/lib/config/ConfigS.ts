import { macro } from "../macro";
import Config from "./Config";

export default class ConfigS extends Config {
    mainnet = {
        data : "https://rpc.soniclabs.com",
        wss : ["wss://rpc.soniclabs.com"],
        //wss : ["https://rpc.soniclabs.com"]
    };
    blockTime = 3;
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];
    gasMin = 50;
    gasMax = 1000;
    gasDelta = 0.001;
    feedAutoTimeHour = 1;

    feedMin = 8;
    feedMax = 10;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 1;

    bot = {
        active:false,
        crazy:false,
        address : "******************************************", //1115, logic:****************************************** //更新支持factoryV2
    }
    pump = {active:true, gasMax:100, countMin:1, rewardMin: 0.000001, maxPump:15}
    trash = {active:true, bid:false, delay:0, rewardMin:0.000001}

    tokens = {
        "******************************************" : {name:"ws", ignore:true, isEth:true, price:0.5},
        "******************************************" : {name:"usdc", ignore:true,},
    };

    routers = {
        "******************************************" : { name:"shadowV2", type: macro.ROUTER_TYPE.V2_STABLE, stableFee:99850, volatileFee:99500},
        "******************************************" : { name:"metro", factory : "******************************************"},
        "******************************************" : { name:"u1", type: macro.ROUTER_TYPE.V2_STABLE, stableFee:99980, volatileFee:99000},

        "******************************************" : { name:"u2", factory : "******************************************", fee: 99800}, //factoryV2
        "******************************************" : { name:"u5", factory : "******************************************", fee: 99800},
        "******************************************" : { name:"u6", factory : "******************************************", fee: 99800},

        "0x4461f408Fcc2a877e6aaA6f7E308B5e2b7f4e3af" : { name:"u3", fee: 99750},
        "0xb4dC4e903be8D28BEa5b9E280DD090d153C4EBc7" : { name:"u4", fee: 99710},
        "0x591cf6942c422fA53E8D81c62a9692D7BeA72F61" : { name:"u7" },
        "0xc159d904ca8c2449df0ae4836197278f2f68c725" : { name:"u8", fee: 99820},
        "0x95a7e403d7cf20f675ff9273d66e94d35ba49fa3" : { name:"u9" },
        "0xf5f7231073b3b41c04ba655e1a7438b1a7b29c27" : { name:"u10", type: macro.ROUTER_TYPE.V2_STABLE, stableFee:99990, volatileFee:99000},

        //"0xaa52bb8110fe38d0d2d2af0b85c3a3ee622ca455" : { name:"u0"}, //v3
    }


    gasWatch = [
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}