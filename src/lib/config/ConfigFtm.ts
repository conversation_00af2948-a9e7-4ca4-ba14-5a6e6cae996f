import Config from "./Config";

export default class ConfigFtm extends Config {
    mainnet = {
        //websocket: "wss://wsapi.fantom.network/",
        //websocket : "wss://speedy-nodes-nyc.moralis.io/445b8d6e237c816a1d02e6e1/fantom/mainnet/ws", //public
        ws: "wss://fantom-mainnet-api.bwarelabs.com/ws/cd79f675-b3f3-4053-bad6-7e4c45de3514", //https://app.bwarelabs.com/dashboard 法兰克福
        wss : [],
        data : "https://rpc.ftm.tools/",
    };
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************"];
    bot = {
        active : true,
        maxDeal : 150,
        address : "******************************************",
    };
    token = {
        dai : {address:"******************************************", decimals:18},

        wftm  : {address:"******************************************", decimals:18},
        usdc : {address:"******************************************", decimals:6},
        fusdt : {address:"******************************************", decimals:6},

        //spookyswap
        ice : {address:"******************************************", decimals: 18, active:true},
        tomb : {address:"******************************************", decimals:18, active:true},

        exod: {address:"******************************************", decimals:9, active:true},
        hec  : {address:"0x5C4FDfc5233f935f20D2aDbA572F770c2E377Ab0", decimals:9, active:true},
        fao : {address:"0x875192DBE755d487e1f6E72D31cb58F6909e8d9d", decimals:9, active:true},
        fs  : {address:"0xC758295Cd1A564cdb020a78a681a838CF8e0627D", decimals:18, active:true},
        mim : {address:"0x82f0B8B456c1A451378467398982d4834b6829c1", decimals:18, active:true},
        scare:{address:"0x46e1Ee17f51c52661D04238F1700C547dE3B84A1", decimals:18, active:true},
        boo: {address:"0x841FAD6EAe12c286d1Fd18d1d525DFfA75C7EFFE", decimals:18, active:true},
        spa: {address:"0x5602df4A94eB6C680190ACCFA2A475621E0ddBdc", decimals:9, active:true},
        bear:{address:"0x3b1a7770A8c97dCB21c18a2E18D60eF1B01d6DeC", decimals:18, active:true},
        pump:{address:"0x8eDDA0107D661E82df660DBa01Ff1D40FA17B70c", decimals:9, active:true},
        credit:{address:"0x77128DFdD0ac859B33F44050c6fa272F34872B5E", decimals:18, active:true},
        shares2:{address:"0xc54A1684fD1bef1f077a336E6be4Bd9a3096a6Ca", decimals:18, active:true},
        omb2:{address:"0x7a6e4E3CC2ac9924605DCa4bA31d1831c84b44aE", decimals:18, active:true},
        tshare:{address:"0x4cdF39285D7Ca8eB3f090fDA0C069ba5F4145B37", decimals:18, active:true},
        treeb:{address:"0xc60D7067dfBc6f2caf30523a064f416A5Af52963", decimals:18, active:true},
        power:{address:"0x131c7afb4E5f5c94A27611f7210dfEc2215E85Ae", decimals:18, active:true},
        atlas:{address:"0x92df3eaBf7c1c2A6b3D5793f6d53778eA78c48b2", decimals:18, active:true},
        lqdr:{address:"0x10b620b2dbAC4Faa7D7FFD71Da486f5D44cd86f9", decimals:18, active:true},
        mst:{address:"0x152888854378201e173490956085c711f1DeD565", decimals:18, active:true},
        joe:{address:"0x9F47F313ACFd4bdC52F4373b493EaE7d5aC5b765", decimals:18, active:true},

        //spiritswap
        spirit: {address:"0x5Cc61A78F164885776AA610fb0FE1257df78E59B", decimals:18, active:true},
    };
    routers = {
        "0xF491e7B69E4244ad4002BC14e878a34207E38c29" : {name: "spooky"},
        "0x16327E3FbDaCA3bcF7E38F5Af2599D2DDc33aE52" : {name: "spirit"},
        //"0xc74d556a21e50579BA59a73740f7bD7064562F1e" : "unknow",
    };
}