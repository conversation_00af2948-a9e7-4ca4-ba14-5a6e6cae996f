import Config from "./Config";

export default class ConfigBch extends Config {
    mainnet = {
        data : "https://smartbch.fountainhead.cash/mainnet",
        wss : ["wss://smartbch.fountainhead.cash/ws/mainnet", "wss://smartbch-wss.greyh.at"],
    };

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];
    tokens = {
        "******************************************" : { name: "wbch" },
        "******************************************" : { name: "flexUsd" },
    };
    routers = {
        "******************************************" : {name: "mist"},
        "******************************************" : {name: "ben"},
        "******************************************" : {name: "tango"},
        "******************************************" : {name: "milk"},
    }
}