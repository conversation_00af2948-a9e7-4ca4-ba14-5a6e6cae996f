import Config from "./Config";

export default class ConfigT<PERSON> extends Config {
    mainnet = {
        data : "https://rpc.tomochain.com",
        wss : ["wss://ws.tomochain.com", "https://rpc.tomochain.com"]
    };
    eth = "******************************************";
    stablePumpToken = "******************************************";
    stableTokens = ["******************************************", "******************************************"];

    tokens = {
        "******************************************" : { name: "wtomo" },
        "******************************************" : { name: "usdt"}
    };
    routers = {
        "******************************************" : {name: "lua"}
    }
}