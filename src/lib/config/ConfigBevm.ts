import Config from "./Config";

export default class ConfigBevm extends Config {
    mainnet = {
        //hk : 600ms
        //vg : 120ms
        //la : 80ms
        data : "https://rpc-mainnet-1.bevm.io",
        wss : ["wss://rpc-mainnet-1.bevm.io"]
    };
    blockTime = 1;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 0.05;
    gasMax = 1;
    gasDelta = 0.0001;
    feedAutoTimeHour = 1;

    feedMin = 0.0005;
    feedMax = 0.0006;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 1;

    bot = {
        active:false,
        crazy:false,
        address : "******************************************", //1115, logic:******************************************
    }
    pump = {active:true, gasMax:100, countMin:1, rewardMin: 0.000001, maxPump:15}
    trash = {active:true, bid:false, delay:1, rewardMin:0.000001}

    tokens = {
        "******************************************" : {name:"wbtc", ignore:true, isEth:true, price:70000},
    };

    routers = {
        "******************************************" : { name: "u1", fee:99600 },
        "******************************************" : { name: "savm"},
        "******************************************" : { name: "dyo", fee:99750},
        //"******************************************" : { name: "savm2"}
        "******************************************" : { name: "u2", }
    };

    gasWatch = [
        "******************************************"
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}