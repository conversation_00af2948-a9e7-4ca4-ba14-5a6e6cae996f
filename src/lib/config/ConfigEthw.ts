import Config from "./Config";

export default class ConfigEthw extends Config {
    mainnet = {
        data : "https://mainnet.ethereumpow.org",
        //wss : ["ws://127.0.0.1:18546"],
        //"https://rpc.ethwnews.net"
        wss : ["ws://127.0.0.1:18546", "https://mainnet.ethereumpow.org",],
        //wss : ["https://mainnet.ethereumpow.org"],
    };

    eth = "******************************************";
    stableTokens = [
        "******************************************",
        "******************************************",
        //"******************************************",
        //"******************************************",
        //"******************************************",
    ];
    stablePumpToken = "******************************************";
    onlyActivePair = true;

    gasMin = 3.01;
    gasDelta = 2.0001;
    gasMax = 1000;

    feedMin = 1.8;
    feedMax = 2;
    feedPumpMulti = 1;
    blockTime = 12;
    feedAutoTimeHour = 6; //-1不自动加gas, 单位小时

    bot = {
        active : false,
        //address : "******************************************",
        //address : "******************************************" //new token checked
        //address : "******************************************" //pump fee
        //address : "******************************************", //pump fee2
        //address : "0xEC7D70D201d8c51A606C80D6AF50e71b714f19ac", //pump fee3
        //address : "0x16052f8F64779446fdBc63B22B4CB16E398d9cC3", //new checkpair
        //address : "0x42562E0e0a0A2E8B2e28A157c83aF8e885Da7Ff2", //test pair patch
        //address : "******************************************", //0412 + 0715
        address : "******************************************", //1115 + 0123
    };

    bot2 = {
        active: false,
        address : "******************************************"
    }

    pump = {
        active: true,
        gasMax :250,
        countMin:2,
        countMax:2,
    }

    trash = { active:true, gasMin:4.2800183, bid:true, delay:500 }

    tokens = {
        "******************************************" : {name:"weth",  isEth:true, ignore:true, price:1.5, keep:10, limit:100, eq:["******************************************"]},
        "******************************************" : {name:"wethw", isEth:true, ignore:true, price:1.5, keep:10, limit:100, eq:["******************************************"]},
        "******************************************" : {name:"usdt.lfg", ignore:true, keep:10,}, //d6

        "******************************************" : {name:"usdt.uni", ignore:true,}, //d6
        "******************************************" : {name:"usdc.uni", ignore:true,},

    
        "******************************************" : {name:"hswap" },
        "******************************************" : {name:"sea"},
        //"******************************************" : {name:"uniw", cex:true},
        "******************************************" : {name:"lfg", max: 180000, stable:"******************************************"},
        //"******************************************" : {name:"pow"},
        //"******************************************" : {name:"ppr"},
        "******************************************" : {name:"vvxen"},
        "******************************************" : {name:"eid"},
        "******************************************" : {name:"ohmw"},
        "******************************************" : {name:"twit"},
        //"******************************************" : {name:"powv"},
        //"******************************************" : {name:"slr"},

    };
    
    routers = {
        "******************************************" : {name: "uniw", fee:99830},
        "******************************************" : {name: "lfg", weth:"******************************************"},
        "******************************************" : {name: "cakew", weth:"******************************************"} ,
        "******************************************" : {name: "u1"},
        "******************************************" : {name: "u2"},
        "******************************************" : {name: "u3"},
        "******************************************" : {name: "u4", weth:"******************************************"},
        "******************************************" : {name: "u6"},
        "******************************************" : {name: "u7"},
        "******************************************" : {name: "u8"},
        "******************************************" : {name: "u10", weth:"******************************************"},
        "******************************************" : {name: "u11", fee:99750},
        "******************************************" : {name: "u12", fee:99750},
        "******************************************" : {name: "u13"},
        "******************************************" : {name: "u15", fee:100000, weth:"******************************************"},
        "******************************************" : {name: "u17", fee:100000},
        "******************************************" : {name: "u18", fee:99750} ,
        "******************************************" : {name: "u19"},
        "******************************************" : {name: "u20"},
        "******************************************" : {name: "u25"},
        "******************************************" : {name: "u26"},
        "******************************************" : {name: "u24", fee:99750} ,
        "******************************************" : {name: "zerg"},
        "******************************************" : {name: "pow2"}, //not v2
        "******************************************" : {name: "miner"},
        //"******************************************" : {name: "u25" }, //uniswap_v2 pair太多9w+
        "******************************************" : {name: "u26", weth:"******************************************"},
        "******************************************" : {name: "u27", weth:"******************************************", fee:99800 },
        //"******************************************" : {name: "u28" }, //uniswap_v2 pair太多9w+
        //"******************************************" : {name: "u29" },
        "******************************************" : {name: "u30"},
        "******************************************" : {name: "u31"},
    };

    gasWatch = [
        //"******************************************", //搬砖混合
        "******************************************", //搬砖eth1
        "******************************************", //搬砖eth2
        "******************************************",
        "******************************************",
    ];

    whiteListPair = {
        /*
        "******************************************" : {fee0:3000, fee1:0}, //UNIW-WETH

        "******************************************" : {fee0:0, fee1:2000}, //UNIW-PP
        "******************************************" : {fee0:2000, fee1:0}, //PP-WETH

        "******************************************" : {fee0:8081, fee1:0}, //♆-WETH
        "******************************************" : {fee0:8081, fee1:0}, //♆-USDT
        "******************************************" : {fee0:0, fee1:8081}, //vvXen-♆

        "******************************************" : {fee0:0, fee1:5000}, //PUSD-POWER
        "******************************************" : {fee0:0, fee1:5000}, //WETH-POWER

        "******************************************" : {fee0:0, fee1:1000}, //UNIW-POWV
        "******************************************" : {fee0:0, fee1:1000}, //USDC-POWV
        "******************************************" : {fee0:1000, fee1:0}, //POWV-WETH

        "******************************************" : {fee0:0, fee1:3000}, //UNIW-SLR
        "******************************************" : {fee0:5000, fee1:0}, //SLR-WETH

        "******************************************" : {fee0:2000, fee1:0}, //KPOW-WETH
        "******************************************" : {fee0:2000, fee1:0}, //KPOW-WETH
        "******************************************" : {fee0:2000, fee1:0}, //KPOW-UNIW
        "******************************************" : {fee0:2000, fee1:0}, //KPOW-vvXEN
        "******************************************" : {fee0:2000, fee1:0}, //KPOW-vvXEN
        */
    }

    blackList = [
        "******************************************", //ALFw3
        //"******************************************", //fake in
    ];

    blackListPump = [
        //"******************************************", //fake in
    ];
    
}