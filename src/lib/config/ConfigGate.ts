import Config from "./Config";

export default class ConfigGate extends Config {
    mainnet = {
        data : "",
        wss : ["wss://evm-ws.gatenode.cc"]
    };
    blockTime = 5;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    //stableTokens = ["******************************************"];
    gasMin = 1.1;
    gasMax = 2000;
    
    feedMin = 5;
    feedMax = 7;
    //feedAutoTimeHour = 6;
    //feedPumpMulti = 1; //pump的填充gas百分比

    bot = {active:true,  address : "******************************************"}
    pump = {active:true, gasMax:200, countMin:2, countMax:7}
    trash = {active:true, gasMin:10.1, delay:1000}

    tokens = {
        "******************************************" : {name:"wgt", ignore:true,keep:100, price:3},
    };
    routers = {
        "******************************************" : {name: "old_gate"},
    };
    gasWatch = [
        //"******************************************",
    ]
    tokenBlackList = {
    }

    blackListPump = [
    ]
}