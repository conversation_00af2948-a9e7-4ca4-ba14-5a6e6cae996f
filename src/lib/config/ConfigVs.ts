import Config from "./Config";

export default class ConfigVs extends Config {
    //wss不通
    mainnet = {
        data : "https://infragrid.v.network/ethereum/compatible",
        wss : ["https://infragrid.v.network/ethereum/compatible"]
    };
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];

    bot = {
        active : false,
        address : "******************************************", //swap with fee, support v1, pump2
    };

    eoaRouter = {
        active:true,
        router : "******************************************".toLocaleLowerCase(),
        opKey : ""
    }
    
    trash = {active:true, rewardMin:0, bid:false, delay:100}

    tokens = {
        "******************************************" : { name: "wvs", keep:10,  price:1.7 },
        "******************************************" : { name: "vusdt", keep:10 },
    };
    routers = {
        "******************************************" : {name: "van"},
    };

}