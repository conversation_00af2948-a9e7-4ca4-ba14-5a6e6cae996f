import Config from "./Config";

export default class ConfigBsc extends Config {
    mainnet = {
        data : "https://bsc-dataseed3.binance.org",
        wss : ["https://bscrpc.com"],
        //wss : ["ws://127.0.0.1:28546"],
    };
    gasMin = 5;
    gasDelta = 0.00001;
    gasMax = 300;

    onlyActivePair = true;

    feedMin = 0.03;
    feedMax = 0.04;
    feedPumpMulti = 1; //pump的填充gas百分比
    feedAutoTimeHour = 6;

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************", "******************************************"];

    bot = {
        active : true,
        address : "******************************************", //swap with fee, support v1
    };
    pump = {
        active:true, gasMax :201.2, countMin:1, countMax:2, rewardMin:100,
    }
    trash = {
        active:true, rewardMin:100, bid:true, delay:1000
    }

    tokens = {
        "******************************************" : {name:"wbnb",  ignore:true, price:350, keep:0.2},
        "******************************************" : {name:"busd", ignore:true, keep:100},
        "******************************************" : {name:"usdc", ignore:true, keep:100},
        "******************************************" : {name:"usdt", ignore:true, keep:100},

        //"0xaAfA10755b3B1DbF46e86d973c3f27f3671ED9db" : {name:"hvt", maxLp:1},
        //"0x8ec7E8C12947642A6BD232a73BBd7b2Ff310A588" : {name:"bx", maxLp:1},
        //"0xE9Cd2668FB580c96b035B6d081E5753f23FE7f46" : {name:"ama", maxLp:1},
        //"0xa477a79a118A84A0d371A53c8F46f8CE883EC1dD" : {name:"bbs", maxLp:1},
        //"0x121235cfF4c59EEC80b14c1d38B44e7de3A18287" : {name:"dks", maxLp:1},
        //"0x60322971a672B81BccE5947706D22c19dAeCf6Fb" : {name:"mdao", maxLp:1},
        //"0xaC60F566567fBC9c229E10e1fb73e69C8BbfD6e2" : {name:"hnt", maxLp:1},
        //"0x54e951E513bC09abaFf971641947Bc69e31068bD" : {name:"odys", maxLp:1},
    };
    routers = {
        "0x10ed43c718714eb63d5aa57b78b54704e256024e" : {name: "cake", fee:99750},
        "0x3a6d8ca21d1cf76f653a67577fa0d27453350dd8" : {name: "bis"},
        "0xcf0febd3f17cef5b47b0cd257acf6025c5bff3b7" : {name: "ape", fee:99800},
        "0x62c1a0d92b09d0912f7bb9c96c5ecdc7f2b87059" : {name: "mdx"},
        "0xb3ca4d73b1e0ea2c53b42173388cc01e1c226f40" : {name: "fst", fee:99600},
        "0xc9a0f685f39d05d835c369036251ee3aeaaf3c47" : {name: "dog"},

        "0xcbdf4cb386b42c1ab41f8b5009cb3d87f6dc623e" : { name: "u1", fee:99800},
        "0x1b6c9c20693afde803b27f8782156c0f892abc2d" : { name: "u2"},
        "0xf926e6fedc4944e44a115ebb3acfe00236f89aa5" : { name: "u3", fee:99800},
        "0x1b9248ad376800aca986da82837e610e43f7bbf6" : { name: "u5", fee:99750},
        "0x44bd27a4ea98b9855aafc8d3e5c9dc37b5ec6f23" : { name: "u6"},
        "0xbf4c0d66db59ae8276e0cb7e1ed36fc4ac8c1d68" : { name: "u7"},
        "0x05ff2b0db69458a0750badebc4f9e13add608c7f" : { name: "u8", fee:99800},
        "0xf29ace1fe5f36389d0dde450a0195a30c3770245" : { name: "u9", fee:99750},
        "0x470cbfb236860eb5257bbf78715fb5bd77119c2f" : { name: "u10", fee:99500},
        "0x1b02da8cb0d097eb8d57a175b88c7d8b47997506" : { name: "u11"},
        "0x306b5284311dd78e9c3e4591c04828c99f2f9690" : { name: "u12", fee:99750},
        "0x53e0e51b5ed9202110d7ecd637a4581db8b9879f" : { name: "u13", fee:99000},
        "0x2bf55d1596786f1ae8160e997d655dbe6d9bca7a" : { name: "u14", fee:99750},
        "0x6ab5b50c674ad724c3c3b6a8d852c5c07aa14362" : { name: "u15", fee:99750},
        "0x17f4a746a7bf05c3e24a2bb7d7d25e4d3e5bbe3e" : { name: "u16", fee:99750},
        "0xcde540d7eafe93ac5fe6233bee57e1270d3e330f" : { name: "u17"},
        "0x87674bbded56d8749e49b00413a621831a0fb266" : { name: "u18", fee:99800},
        "0xa3b81724638208faa03810b391dde879e5fb0c79" : { name: "u19", fee:99800},
        "0x493fd12fd51554a2c1817d6be2c7f3cb35ee8c2e" : { name: "u20"},

        "0x05e61e0cdcd2170a76f9568a110cee3afdd6c46f" : { name: "u21", fee:99800},
        "0xcec274180ed52b041e4359dfc016aac2642e82e8" : { name: "u22"},
        "0x47b604dce4af28ede480ec795fbc1daf3e4cf731" : { name: "u23"},
        "0x7f1f846bc6b252bdee65f61491a879f0ad7ee926" : { name: "u24"},
        "0x9869674e80d632f93c338bd398408273d20a6c8e" : { name: "u25"},
        "0x34dbe8e5faefabf5018c16822e4d86f02d57ec27" : { name: "u26", fee:99800},
        "0x591575579d607c8bdc5e1f15f1aa443fd5e5510a" : { name: "u27", fee:99750},
        "0x56f6ca0a3364fa3ac9f0e8e9858b2966cdf39d03" : { name: "u28"},
        "0xc25033218d181b27d4a2944fbb04fc055da4eab3" : { name: "u29", fee:99750},
        "0x1355a5e85bde471b9aa418f869f936446357748b" : { name: "u30", fee:99000},
        "0x85cd4913537ec4d187fc85150d28a40892304fe1" : { name: "u31", fee:99750},
        "0x8317c460c22a9958c27b4b6403b98d2ef4e2ad32" : { name: "u32"},
        "0x2f30bab00425d480b5e00c2172cd049f7f01eb64" : { name: "u34", fee:99800},
        "0xbe65b8f75b9f20f4c522e0067a3887fada714800" : { name: "u35"},
        "0xbd67d157502a23309db761c41965600c2ec788b2" : { name: "u36"},
        "0x933daea3a5995fb94b14a7696a5f3ffd7b1e385a" : { name: "u37", fee:99800},
        "0xb68825c810e67d4e444ad5b9deb55ba56a66e72d" : { name: "u38"},
        "0x7dae51bd3e3376b8c7c4900e9107f12be3af1ba8" : { name: "u39"},
        "0x7dd3b4d9de913a46e0b386df1f7d9fac46865754" : { name: "u40", fee:99750},
        "0x82e801e701bc8b3c0c0b43ca1cc2dbf8b1655bbc" : { name: "u41"},
        "0x989e555316f2cef1949d782fd875995f83b65fa0" : { name: "u42"},
        "0x0267db959951f145850910ccd394170a34525b1e" : { name: "u43", fee:99750},
        "0x9fd7764e2303e7748736d115304350ec64e403b2" : { name: "u44"},
        "0xe4d96cf7f47656200fbc3b6110dc8e785216ef0e" : { name: "u45", fee:99300},
        "0xd3a5d478c9a407b357269edb1557004704c4cee0" : { name: "u46", fee:99600},
        "0x0a04fa4a842fa21e202dd07e721b048b466edb47" : { name: "u47"},
        "0xd1d2052246fe24b19cd15601a5db4b2750bf1e53" : { name: "u48", fee:99750},
        "0x39255da12f96bb587c7ea7f22eead8087b0a59ae" : { name: "u49", fee:99000},
        "0xa63b831264183d755756ca9ae5190ff5183d65d6" : { name: "u50"},
        "0x325e343f1de602396e256b67efd1f61c3a6b38bd" : { name: "u51"},
        "0xe0463d690533e788f7baf2f9be2e7179cd373f96" : { name: "u52", fee:99800},
        "0xdb07ed70aa18ffc8b422bf3d8af947e937511fdf" : { name: "u53"},
        "0x9769ac7da62f2419fc99bfe7f1427bc501864871" : { name: "u54"},
        "0x98cc2cd55ca2092034146ebd8eb043f9f976623a" : { name: "u55"},
        
    };

    whiteListPair = {
        "0x231d9e7181E8479A8B40930961e93E7ed798542C" : {fee0:300, fee1:0}, //WBNB-FLOKI 0.3%
        "0xACC22e0Ff64E788e6AD569a57d130B9d957494dc" : {fee0:300, fee1:0}, //WBNB-FLOKI 0.3%
        "0xADBfE34e35EDb3D31F4Bd1AF5a513f4C57CDc5e9" : {fee0:300, fee1:0}, //WBNB-FLOKI 0.3%
    }

    gasWatch = [
    ]
}