import Config from "./Config";

export default class ConfigVlx extends Config {
    //wss不通
    mainnet = {
        data : "https://evmexplorer.velas.com/rpc",
        wss : ["wss://api.velas.com/rpc", "https://evmexplorer.velas.com/rpc"]
    };
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************"];
    
    tokens = {
        "******************************************" : { name: "wvlx" },
        "******************************************" : { name: "busd" },
        "******************************************" : { name: "usdt", cex:true },
        "******************************************" : { name: "usdc" },
    };
    routers = {
        "******************************************" : {name: "wag"},
        "******************************************" : {name: "astro"},
    };

}