import Config from "./Config";

export default class ConfigRsk extends Config {
    //https://rsk.getblock.io/mainnet
    //wss://rsk.getblock.io/websocket
    mainnet = {
        data : "https://rsk.getblock.io/mainnet",
        wss : ["https://public-node.rsk.co"],
    };

    eth = "******************************************";
    stableTokens = ["******************************************"];
    stablePumpToken = "******************************************";
    onlyActivePair = true;

    gasMin = 1;
    gasDelta = 0.00001;
    gasMax = 30000;

    bot = {
        active : false,
        address : "******************************************",
    };

    pump = {
        active: true,
        greed: true,
        gasMax :30000,
        countMin:1,
        countMax:1,
    }

    tokens = {
        "******************************************" : {name:"wech", ignore:true, price:0.1},
        "******************************************" : {name:"bUsdc", ignore:true, price:0.1},

    };
    routers = {
        "******************************************" : {name: "ech"},
        "******************************************" : {name: "dfy"},
    };
}