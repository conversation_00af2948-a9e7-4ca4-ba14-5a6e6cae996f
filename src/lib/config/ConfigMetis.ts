import { macro } from "../macro";
import Config from "./Config";

export default class ConfigMetis extends Config {
    mainnet = {
        data : "https://andromeda.metis.io/?owner=1088",
        //de 30ms
        //nl 197
        // ws://127.0.0.1:9546
        //wss : ["wss://andromeda-ws.metis.io"],
        //wss : ["https://andromeda.metis.io/?owner=1088"],
        wss : ["ws://127.0.0.1:9546", "wss://andromeda-ws.metis.io"]
    };

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];

    blockTime = 50; //每个区块的时间

    gasMin = 13;
    gasDelta = 0.0000012;
    gasMax = 1;

    feedMin = 0.02;
    feedMax = 0.04;
    feedPumpMulti = 1; //pump的填充gas百分比
    //feedAutoTimeHour = 100;

    maxOperator = 2;

    bot = {
        active : false,
        crazy: false,
        //address : "******************************************", //swap with fee, uniswapv1, test pair, pump2, pumpSafe, testPairPatch
        address : "******************************************", //0412
    };

    pump = {
        active:false,
        gasMax :1000.2,
        countMin:1,
        countMax:1,
        rewardMin:0.005,
    }

    trash = {active:true, rewardMin:0.005, bid:false, delay:0}

    tokens = {
        "******************************************" : {name:"weth", price:1800, ignore:true, keep:1, limit:1},
        "******************************************" : {name:"usdc", ignore:true, keep:200, limit:300},
        
        "******************************************" : {name:"metis", price:30, ignore:true},
        "******************************************" : {name:"usdt", ignore:true, },
        "******************************************" : {name:"dai",  ignore:true, }
        
    };
    routers = {
        "******************************************" : {name: "hermas", fee:99990, type: macro.ROUTER_TYPE.V2_STABLE}, //stable
        "******************************************" : {name: "nets"},
        "******************************************" : {name: "teth", fee:99800}
    };


    blackList = []
    blackListPump = []
    gasWatch = [
        "******************************************"
    ]
}