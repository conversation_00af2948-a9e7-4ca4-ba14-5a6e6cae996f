import Config from "./Config";

export default class ConfigMxc extends Config {
    mainnet = {
        data : "https://rpc.mxc.com",
        //wss : ["wss://rpc.mxc.com/ws"]
        wss : ["wss://rpc.mxc.com/ws", "txpool|https://rpc.mxc.com"]
    };
    blockTime = 60;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 1.5;
    gasMax = 5000000;
    feedAutoTimeHour = 2;

    feedMin = 5000;
    feedMax = 7000;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 3;

    bot = {
        active:false,
        crazy:false,
        address : "******************************************", //1115
    }
    pump = {active:true, gasMax:5000000, countMin:2, countMax:2, rewardMin: 0.002}
    trash = {active:true, bid:true, delay:5000, rewardMin:0.002}

    tokens = {
        "******************************************" : {name:"wmxc", ignore:true, keep:1, isEth:true, price:0.003},
    };

    routers = {
        "******************************************" : { name: "mxc" },
        
    };

    gasWatch = [
        "******************************************"
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}