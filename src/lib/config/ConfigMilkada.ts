import { macro } from "../macro";
import Config from "./Config";

export default class ConfigMilkada extends Config {

    mainnet = {
        //wss://rpc-devnet-cardano-evm.c1.milkomeda.com //(oh:19ms) (vg:15-80ms) (io:30~70ms)
        //wss : ["wss://rpc-mainnet-cardano-evm.c1.milkomeda.com"]
        data : "https://rpc-mainnet-cardano-evm.c1.milkomeda.com", 
        //wss : ["https://rpc-mainnet-cardano-evm.c1.milkomeda.com"],
        
        wss : [
            "wss://rpc-mainnet-cardano-evm.c1.milkomeda.com",
            //"wss://rpc-mainnet-cardano-evm.c1.milkomeda.com",
            "https://rpc-mainnet-cardano-evm.c1.milkomeda.com",
        ],
        
    };
    eth = "******************************************";
    //stableTokens = ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************"];
    stableTokens = ["******************************************",];

    blockTime = 4;
    gasMin = 41;
    gasDelta = 0.000123;
    gasMax = 800;

    feedMin = 2;
    feedMax = 3;
    feedPumpMulti = 1;
    feedAutoTimeHour = 12;
    maxOperator = 9;

    bot = {
        active : false,
        crazy : false,
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************", //new pair checker
        //address : "******************************************", //pump fee3
        //address : "******************************************", //swap with fee, support v1, testpair, pump2
        //address : "0xFd6CA7cE5443fe2073dB09454199DC916B32cF32", //0412 + 0715
        address : "0x070B7A332087888aa896da6F4Ea65589C615a8D3", //1115+0123 修复v1
        sharingan : true,
    };
    pump = {
        active: true,
        gasMax :300.1,
        countMin:3,
        countMax:7,
    }

    trash = { active:true, rewardMin:0.002, bid:true, delay:1000 }

    tokens = {
        "******************************************" : { name: "wada", ignore:true, cex:true, keep: 200, price: 0.4 },

        "******************************************" : { name: "usdc.multi", ignore:true, cex:true, keep: 2,}, //d6
        "******************************************" : { name: "usdc.ce", ignore:true, cex:true, keep: 2,}, //d6

        "******************************************" : { name: "usdt.multi", ignore:true, cex:true, keep:2,}, //d6
        "******************************************" : { name: "usdt.ce", ignore:true, cex:true, keep: 2,}, //d6

        "0x6De33698e9e9b787e09d3Bd7771ef63557E148bb" : { name: "dai", ignore:true, cex:true, keep: 2}, //d18

        //"******************************************" : { name: "usdt.mad", ignore:true, cex:true},
        //"******************************************" : { name: "usdc.mad", ignore:true, cex:true, keep: 20},
        "******************************************" : { name: "sUsdc",  ignore:true, cex:true},

        "******************************************" : { name: "busd", ignore:true, cex:true},
        "******************************************" : { name: "weth", cex:true },
        "******************************************" : { name: "wbtc", cex:true },
        "******************************************" : { name: "avax", cex:true },
        "******************************************" : { name: "bnb", cex:true },

        "******************************************" : { name: "ftm", cex:true },
        "******************************************" : {name:"ftm2", cex:true},

        "******************************************" : { name: "myield", cex:true },
        "******************************************" : { name: "wbtc", cex:true},
        "******************************************" : { name: "weth", cex:true},
        "******************************************" : { name: "mid" },
    }
    routers = {
        "******************************************" : {name: "milky",},
        "******************************************" : {name: "muesli",},
        "******************************************" : {name: "occamx", },
        "******************************************" : {name: "milky2", fee:99800,},
        "******************************************" : {name: "u2",},
        "******************************************" : {name: "u3", fee:99750,},

        "******************************************" : {name: "ada",},
        "******************************************" : {name: "u6", eth:"******************************************",},
        "******************************************" : {name: "u7",},
    };

    gasWatch = [
        "******************************************",
        "******************************************", //残渣
    ];

    blackList = [
        "******************************************", //
        "******************************************",  //
        "******************************************",
    ];

    blackListPump = [
        "******************************************",
        "******************************************",
        "******************************************",
    ]
}