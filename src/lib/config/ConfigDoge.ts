import Config from "./Config";

export default class ConfigDoge extends Config {
    mainnet = {
        data : "https://dogechain.ankr.com",
        /*
        https://rpc01.dogechain.dog    //or
        https://rpc-sg.dogechain.dog   //sg
        https://rpc-us.dogechain.dog   //vg
        https://rpc.dogechain.dog      //vg
        https://rpc01-sg.dogechain.dog //sg
        https://rpc02-sg.dogechain.dog //sg
        https://rpc03-sg.dogechain.dog //sg
        https://dogechain.ankr.com      //hk
        https://dogechain-sj.ankr.com   //or
        */
        //wss: ["ws://127.0.0.1:8545/ws"],
        //wss: ["http://127.0.0.1:8545/"],
        //wss : ["doge|ws://127.0.0.1:8545/ws", "https://rpc-sg.dogechain.dog"]
        wss : ["doge|ws://127.0.0.1:8545/ws", "http://127.0.0.1:8545/", "https://rpc-sg.dogechain.dog"],
        //wss : ["https://rpc02-sg.dogechain.dog"],
        //wss : ["https://rpc-sg.dogechain.dog", "https://rpc-us.dogechain.dog", "https://rpc.dogechain.dog"],
    };

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************", "******************************************"];
    stablePumpToken = "******************************************";
    onlyActivePair = true;

    gasMin = 50;
    gasDelta = 101.01;
    gasMax = 10000;

    feedMin = 27;
    feedMax = 30;
    feedPumpMulti = 1;
    feedAutoTimeHour = 6; //-1不自动加gas, 单位小时

    maxLp = 10;

    blockTimne = 2;

    bot = {
        active : true,
        //address : "******************************************",
        //address : "******************************************" //new token checker
        //address : "******************************************", //pump fee
        address : "******************************************", //pump fee3
    };

    pump = {
        active: true,
        gasMax :1000,
        countMin:5,
        countMax:10,
    }
    
    trash = { active:true, rewardMin:0.001, delay:300 }

    tokens = {
        "******************************************" : {name:"wwdoge", ignore:true, price:0.15, limit:7000, keep:5000 },
        "******************************************" : {name:"dc", cex:true },
        "******************************************" : {name:"eth", ignore:true, cex:true},

        "******************************************" : { name:"usdt.m", ignore:true, cex:true },
        "******************************************" : { name:"usdc.m", ignore:true, cex:true },
        "******************************************" : { name:"dai.m",ignore:true, cex:true},

        "******************************************" : { name:"usdc.s", ignore:true, cex:true },


        "******************************************" : { name: "OMNOM" },
        "******************************************" : { name: "KIB" },
        "******************************************" : {name:"wojk"},
        "******************************************" : {name:"DogeShrek"},
        "******************************************" : {name:"yuzu"},

        //"******************************************" : {name:"Celestial Doge", ignore:true,},
        //"******************************************" : {name:"SATSUMA", ignore:true}, //小数点太小
        //"0xB43bD5a6384974CAF1b2A313bbcd14ac8ab4054B" : {name:"CADINU", ignore:true},
    };
    
    tokenBlackList = {
        "0x93C8a00416dD8AB9701fa15CA120160172039851" : "DOGMONEY",
        //"******************************************" : "Celestial Doge",
        "0xf108924c3a2B8200aD7bB6958f016d718ff406AD" : "DCMINER",
        "0x6707984c61531536d4457E131811C8014b6B30D7" : "kds", //fee
        "0x7cae5B287Cc8B3d2dfd00Fb5B938331A5318248b" : "sbi", //fee
        "0x45ae2F1e300859ede62C3CC0FC2248a67cA3661A" : "shark", //fee
        "0x3FFA83f295FdC28224bc224BeE48AbdF95aD94b6" : "doge eat bone", //maybe fee
        "0x50f985A0Bb5fb1c5Aa7F0ee2d4335dBb1B8ae093" : "micro doge", //maybe fee
        "0xa375d351fB2A62E954D769973471e5EFf432AC88" : "Bull Terrier",
        "0x0f21BbB9941C3314aF80CdA68c8619cc00120256" : "dswap", //fee
        "0xA41884A9628C71f6822251Fd52c3B58Cf8F925df" : "doga", //fee
        "0x1dE7453B86Dfb2b713861e9Da211074423b00984" : "dayc", //fee
        "0x9aD220F6C7fA17aC7796bf15725f3F6e529f03aF" : "exdoge", //fee
        "0x025863C8Ea5cE219472881102eA48a2a63d03a76" : "Baby Doge Eat Bone", //fee
        "0x59B4AC2B92018948B561E1507260f2Aa9Fe7A55F" : "Doge Eat Moon", //fee
        "0xAEe671F222231dc384946450DB1da9c78F49Ccf1" : "wsOHMD", //fee
        "******************************************" : "satsuma", //
    }

    routers = {
        "0xa4ee06ce40cb7e8c04e127c1f7d3dfb7f7039c81" : {name: "doge", fee:99800},
        "0x6258c967337D3faF0C2ba3ADAe5656bA95419d5f" : {name: "u1"},
        "0x72d85Ab47fBfc5E7E04a8bcfCa1601D8f8cE1a50" : {name: "yoda", fee:99500},
        "0xaBC4325bAD182076EAa5877c68437833d596D3Ee" : {name: "u3"},
        "0xd1529eF462316b2f31336352dEf66Ae21EB69241" : {name: "u4"},
        "0x9BBF70e64fbe8Fc7afE8a5Ae90F2DB1165013F93" : {name: "u6"},
        "0x1fA896D1fcdE7e5E92D0DbebA3caE2753c86F3cA" : {name: "u7", fee:99750},
        "0xe0839da3DD2DAaD6690F987B879fDEc0b47F2324" : {name: "u5", fee:99800},
        "0x0236F4029690de5438FB9aDE6aDB7271109295aE" : {name: "u6", fee:99750},
        "0xF03B6DAD0e06e6c542CF88596355f91922f69bA7" : {name: "u7"},
        "0x9695906B4502D5397E6D21ff222e2C1a9e5654a9" : {name: "wojak", fee:99750},
        "0xe0d1ba683de619f5d9cf7e2a65400cb13f9e10c9" : {name: "noni"},
        "0xD8eD9772Db0775b7173e6801d5A547cb6A62b2ea" : {name: "dark"},
    };

    gasWatch = [
        "0xAa133761b93366dB60aABB4c9D539e056df2BaCA", //wwdoge
        "0x25dDcD92AF41C27fBd90EEFF3cDC258e4167524b",
    ]

    blackList = [
        "0x7265FDA6a457AD2d79534AB5845Fb786Cf0A7c9e",
        "0x069B08fa772Fd0BD99c4214c64921f7Ef000A39a",
        "0x7F488284111BE0586a8bf7fA6E9eCd36809Ae6bf",
        "0x5AD2517fe5Df61A33f298f29aC06C8ee8739BeCB",
        "0xF251b0Ae1a6bBfd7eab08f72D48D4bb885f2EeD2",
        "0x5670Db764c82DB51b840d95D7666E26B5f9C818D",
        "0x4953Ce2bE101c24e6cBdE18480CC8dd55296461D",
        "0xa9B81f02c83C017f29f8d9ad9056d6B5ddB0D3E9",
        "0x1394801f24807CE1Ae91771913C959da4664E619",
        "0x2A55352f2f37DB425B27c7C22a0d23CEDe7E7831",
        "0x7374FB06644eE85be7de011C567B9deBD9FfFC99",
        "0x948a8F44F9C92146Fa6995f07C886B32b481f31d",

        "0xF787Cd3D2f61Cd4014E6867883DcEd2F62d68D6F",
        "0x22cbd396Ca25fD41F4959112861F7c11f691Ad43",
        "0xBB61E50A12Ba25EE919c2F13de904C494608CcEF",
        "0x95a4817B5EE1f0df7ADA34f973D068E104ab8920",
        "0xb96dD39A05cA15021cFA14942a61dD1D8EA99B03",
        "0x8530Ddd0508148C12595f3bCE669918ebe3B1432",
        "0xc9393b38f0d8eB9e1e3010eb7F0BefF4b97EB780",
        "0xF014a8BA0C1D96c7c5F8fF026CfE4A2a77D5166c",
        "0xf245c57FF7e05D7f9862a3e5B71474605ABF5AbA",
        "0x1D9161942bFfC71dBAA2a96582A241698E0530c8",
        "0x5E981336C62DA9B9d11afC6E8Cd24590EAf6726e",
        "0xda861241FA0184AEd23E90190866052402c02f95",
        "0x350E648C855EedE667b15a16e77425af1ED27C89",
        "0xF787Cd3D2f61Cd4014E6867883DcEd2F62d68D6F",
        "0x753bE1E4b5cC650237b70D0fABE534d77F138268",
        "0x22cbd396Ca25fD41F4959112861F7c11f691Ad43",
        "0xD1F5fB35fC9B951498a36796Fe5211946dffd55E"
        
    ]

    blackListPump = [
        "0x7265FDA6a457AD2d79534AB5845Fb786Cf0A7c9e",
        "0x069B08fa772Fd0BD99c4214c64921f7Ef000A39a",
        "0x7F488284111BE0586a8bf7fA6E9eCd36809Ae6bf",
        "0x5AD2517fe5Df61A33f298f29aC06C8ee8739BeCB",
        "0xF251b0Ae1a6bBfd7eab08f72D48D4bb885f2EeD2",
        "0x5670Db764c82DB51b840d95D7666E26B5f9C818D",
        "0x4953Ce2bE101c24e6cBdE18480CC8dd55296461D",
        "0xa9B81f02c83C017f29f8d9ad9056d6B5ddB0D3E9",
        "0x1394801f24807CE1Ae91771913C959da4664E619",
        "0x2A55352f2f37DB425B27c7C22a0d23CEDe7E7831",
        "0x7374FB06644eE85be7de011C567B9deBD9FfFC99",
        "0x948a8F44F9C92146Fa6995f07C886B32b481f31d",
        "0x95a4817B5EE1f0df7ADA34f973D068E104ab8920",
        "0xb96dD39A05cA15021cFA14942a61dD1D8EA99B03",
        "0x8530Ddd0508148C12595f3bCE669918ebe3B1432",
        "0xc9393b38f0d8eB9e1e3010eb7F0BefF4b97EB780",
        "0x7B724Af32E93d1D0525C1f3FB4972d7777777771",
        "0x778aAaa75236a03ff34b3E10b3C2B0CE6A16Fc18",
        "0x966E5A58BBdD32503F85Ce3Ce473904C65B6a7cd",
        "0xF014a8BA0C1D96c7c5F8fF026CfE4A2a77D5166c",
        "0xf245c57FF7e05D7f9862a3e5B71474605ABF5AbA",
        "0x1D9161942bFfC71dBAA2a96582A241698E0530c8",
        "0x5E981336C62DA9B9d11afC6E8Cd24590EAf6726e",
        "0xda861241FA0184AEd23E90190866052402c02f95",
        "0x350E648C855EedE667b15a16e77425af1ED27C89",
        "0xF787Cd3D2f61Cd4014E6867883DcEd2F62d68D6F",
        "0x753bE1E4b5cC650237b70D0fABE534d77F138268",
        "0x22cbd396Ca25fD41F4959112861F7c11f691Ad43",
        "0x8986bEEc516812A6Cc91a121F6C2F025F256Dc07",
        "0x6D4139681475ba68bF8caF2e3719Ca257BEF266a"
    ]
}