import Config from "./Config";

export default class ConfigRose extends Config {
    mainnet = {
        data : "https://emerald.oasis.dev",
        wss : ["wss://emerald.oasis.dev/ws", "https://emerald.oasis.dev"],
    };
    eth = "******************************************";
    stableTokens = ["******************************************","******************************************","******************************************"];
    token = {
        wrose : {address:"******************************************", decimals:18},
        usdt : {address:"******************************************", decimals:6},
        usdc:  {address:"******************************************", decimals:6},
    };
    routers = {
        "******************************************" : {name: "swap1"},
        "******************************************" : {name: "swap2"},
    }
}