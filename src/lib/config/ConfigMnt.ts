import { type } from "os";
import { macro } from "../macro";
import Config from "./Config";

export default class ConfigMnt extends Config {
    mainnet = {
        data : "https://evm.kava.io",
        //ws://127.0.0.1:8546
        //aws-vg 7ms
        wss : ["wss://wss.mantle.xyz"] //aws-vg
    };
    blockTime = 6;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 1;
    gasDelta = 0.0001;
    gasMax = 1000;
    feedAutoTimeHour = 0.1; //单位小时
    onlyActivePair = false;

    feedMin = 0.15;
    feedMax = 0.2;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 10;
    securityLevel = 10;

    bot = {
        active:true,
        crazy:false,
        address : "******************************************", //0412 + 0715
        gasMax : 1000,
    }
    pump = {active:true, gasMax:1000, countMin:1, countMax:3, rewardMin: 0.002}
    trash = {active:true, bid:true, delay:1, rewardMin: 0.1}


    tokens = {
        "******************************************" : {name:"weth", ignore:true, keep:0.01, price:1900},
    };
    tokenBlackList = {}

    routers = {
        "******************************************" : { name: "fusion", type: macro.ROUTER_TYPE.V3 },
        "******************************************" : { name: "u1", type: macro.ROUTER_TYPE.V3 },
    };

    gasWatch = [
    ]
    whiteListPair = {
        //"******************************************" : {fee0:1000, fee1:1000}, //test
    }
    blackListPair = [];

    blackList = [
    ]

    blackListPump = [
    ]
}