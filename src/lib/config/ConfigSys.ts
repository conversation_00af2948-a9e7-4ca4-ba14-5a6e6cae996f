import Config from "./Config";

export default class ConfigSys extends Config {
    mainnet = {
        data : "https://rpc.syscoin.org",
        //https://rpc.ankr.com/syscoin/c959fef5bb6d732dc90f685ebea9bf31b73f21ba843c15ccf222df5704d112c3
        wss : ["wss://rpc.syscoin.org/wss", "https://rpc.syscoin.org", "https://rpc.ankr.com/syscoin"]
    };
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];
    blockTime = 10 * 60;

    tokens = {
        "******************************************" : { name: "wsys" },
        "******************************************" : { name: "usdc"}
    };
    routers = {
        "******************************************" : {name: "pega"}
    }
}