import { macro } from "../macro";
import Config from "./Config";

export default class ConfigOpbnb extends Config {
    mainnet = {
        data : "https://opbnb-mainnet-rpc.bnbchain.org",
        //wss : ["wss://opbnb-mainnet-rpc.bnbchain.org"] //vg
        //wss://opbnb.publicnode.com
        wss : ["https://opbnb-mainnet-rpc.bnbchain.org"],
        //wss : ["wss://opbnb.publicnode.com"],
        //wss : [ "b|wss://opbnb-mainnet.nodereal.io/ws/v1/9c31fd66d47a4014b4015d068966836d", "txpoolOnly|https://opbnb-mainnet-rpc.bnbchain.org"]
        //wss : [ "b|wss://opbnb-mainnet.nodereal.io/ws/v1/9c31fd66d47a4014b4015d068966836d"], //ceil
        //wss : ["b|wss://opbnb-mainnet.nodereal.io/ws/v1/bfb02494386b422ba8781eaed5b0ef34", "txpoolOnly|https://opbnb-mainnet-rpc.bnbchain.org"], //icefang
        //wss : ["b|wss://opbnb-mainnet.nodereal.io/ws/v1/4ca416e7034f4127a309cd491c2c428c", "txpoolOnly|https://opbnb-mainnet-rpc.bnbchain.org"], //winterwoo
        
        //wss : [ "wss://opbnb-mainnet.nodereal.io/ws/v1/64a9df0874fb4a93b9d0a3849de012d3"], //public1
        //wss : [ "wss://opbnb-mainnet.nodereal.io/ws/v1/e9a36765eb8a40b9bd12e680a1fd2bc5"], //public2
        //wss : ["https://opbnb-mainnet-rpc.bnbchain.org"]
    };

    eth = "******************************************";
    stableTokens = ["******************************************"];

    blockTime = 2; //每个区块的时间

    gasMin   = 0.00001;
    gasDelta = 0.00001;
    gasMax   = 0.011;

    feedMin = 0.0013;
    feedMax = 0.002;
    feedPumpMulti = 1; //pump的填充gas百分比
    feedAutoTimeHour = 0.1;
    maxOperator = 10;
    updateAllPairsDelay = 200;
    //disableSubscribeLog = true;

    bot = {
        active : false,
        //crazy: false,
        //address : "******************************************", //0412+0715
        //address : "******************************************", //1115
        address : "******************************************", //1115+0123
    };

    pump = {
        active:false,
        gasMax :0.000001,
        countMin:1,
        countMax:1,
        rewardMin:0.005,
    }

    trash = {active:true, rewardMin:0.01, bid:true, delay:10}

    tokens = {
        "******************************************" : {name:"wbnb", ignore:true, price:300, keep:0.01, limit:0.001, },
        //"******************************************" : {name:"wbnb2", ignore:true, price:300, keep:0.01, limit:0.001,},
        //"******************************************" : {name:"wbnb3", ignore:true, price:300, keep:0.01, limit:0.001},
    };
    routers = {
        "0x3958795ca5c4d9f7eb55656ba664efa032e1357b" : {name: "binary"},
        "0x4346aac201e734853d2625c8a892f3b9f2fe8bad" : {name: "fdex", fee: 99800},
        "0x6494bdb4f1a678669815226ecae77748a8749dd5" : {name: "opanke", },
        "0xe4f7776c753af46d2aa23e3348d17548c86dc47d" : {name: "byte" },
        "0xda9ef9a892e34b7146ffd069092cc632ab28b0a1" : {name: "opb", fee: 99750 },
        "******************************************" : {name: "cz", fee: 99000 },
        "******************************************" : {name:"knight"},
        "******************************************" : {name:"luigi", fee: 99750},

        "******************************************" : {name:"u1"},
        "******************************************" : {name:"u2"},
        "******************************************" : {name:"u3", eth:"******************************************"},
        "******************************************" : {name:"u4"},
        "******************************************" : {name:"u6", fee: 99750},
        //"******************************************" : {name:"zena"},
        "******************************************" : {name:"u9", },
        "******************************************" : {name:"u10", fee:99750 },
        "******************************************" : {name:"cubi" },
        "******************************************" : {name:"u12", eth:"******************************************" },
        "******************************************" : {name:"u13", fee:99000, eth:"******************************************" },
        "******************************************" : {name:"u14", fee:99750 },
        "******************************************" : {name:"u15", fee:99800 },
        "******************************************" : {name:"u16" },
        "******************************************" : {name:"u17" },
        "******************************************" : {name:"u18", fee:99750, eth:"******************************************"},
        "******************************************" : {name:"u20", stableFee:99970, volatileFee:99700, type: macro.ROUTER_TYPE.V2_STABLE },

        "******************************************" : {name:"u21", },
        "******************************************" : {name:"u22", },
        "******************************************" : {name:"u23", stableFee:99970, volatileFee:99700, type: macro.ROUTER_TYPE.V2_STABLE },
        "******************************************" : {name:"u24", fee:99750},
        "******************************************" : {name:"u25", stableFee:99970, volatileFee:99700, type: macro.ROUTER_TYPE.V2_STABLE },
        "******************************************" : {name:"u26", },
        "******************************************" : {name:"u27", },
        "******************************************" : {name:"moby", },
        "******************************************" : {name:"u29", },
        "******************************************" : {name:"u30", },
        "******************************************" : {name:"u31", fee:99750},

        "******************************************" : {name:"u32", eth:"******************************************"},
        "******************************************" : {name:"cuan", fee:99750},
        "******************************************" : {name:"u34"},
        "******************************************" : {name:"u35"},
        "******************************************" : {name:"u36", fee:99800},
        "******************************************" : {name:"u37", fee:99800},
        "******************************************" : {name:"u38"},
        "******************************************" : {name:"fed"},
        "******************************************" : {name:"u40"},
        "******************************************" : {name:"u41", stableFee:99960, volatileFee:99820, type: macro.ROUTER_TYPE.V2_STABLE},
        "******************************************" : {name:"u42"},
        "******************************************" : {name:"u43"},
        "******************************************" : {name:"u44", fee:95000},
        "******************************************" : {name:"u45", fee:99800, eth:"******************************************"},
        "******************************************" : {name:"u46", fee:99750},
        "******************************************" : {name:"xos", },
        //"******************************************" : {name:"u46", },
        //"******************************************" : {name:"u47", },
        //"******************************************" : {name:"u48", },
        "******************************************" : { name: "punk", fee:99750 },
        "******************************************" : {name:"u49"},
        //"******************************************" : { name:"u50"}, //v3
        "******************************************" : {name:"x", fee:99750},
        "******************************************" : {name:"u47", stableFee:99960, volatileFee:99820, type: macro.ROUTER_TYPE.V2_STABLE},
        "******************************************" : {name:"thena"},
        "******************************************" : {name:"grok", fee:99750},

    };
    gasWatch = [
        "0x85a6dbef8532d86cece30c2a0bc551474c8f0aab",
        "0x0a53971157efe09ef6f64d75f913da9b8b8cf44a",
        "0x9ac6e4314a89076774ed58e02adee75752a22d68",
        "0x81d70ef88511d5c6552249f9bde184a8cf4ff1ce"
    ]
}