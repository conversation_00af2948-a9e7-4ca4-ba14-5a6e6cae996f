import Config from "./Config";

export default class ConfigCro extends Config {
    mainnet = {
        data : "https://evm.cronos.org",
        wss : ["wss://cro.getblock.io/6dda325e-7c3d-4982-abec-b3c1a92a555a/mainnet/"]
        //wss : ["ws://127.0.0.1:8546", "https://evm.cronos.org"],
    };

    gasMin = 4870;
    gasDelta = 100.1;
    gasMax = 20000.1;
    onlyActivePair = true;

    feedMin = 18;
    feedMax = 20;
    feedAutoTimeHour = 12;
    feedPumpMulti = 4;

    blockTime = 6; //每个区块的时间

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];
    tokens = {
        "******************************************" : { name: "wcro", price:0.1, ignore:true, keep:1000 },
        "******************************************" : { name: "usdc", ignore:true , keep:10},
    };

    bot = {
        active: true,
        //address : "******************************************",
        address : "******************************************", //pump fee3
    }
    pump = {
        active:false,
        gasMax :10000,
        countMin:1,
        countMax:7,
    }

    trash = { active:true, rewardMin:0.05, delay:0 }

    routers = {
        "******************************************" : {name: "mmf"},
        "******************************************" : {name: "vvs"},
        //"******************************************" : "cro", //factoy不对
        "0xcd7d16fb918511bf7269ec4f48d61d79fb26f918" : {name: "crona", fee:99750},
        "0xb99978440f310658c5e69d5042724327ef6d3ce7" : {name: "candy", fee:99850},
        "0xec0a7a0c2439e8cb67b992b12ecd020ea943c7be" : {name: "crk"},
        //"0x3048be6787ee956cc898d30f44f65ea643ecf755" : "cgs",
        "0xdb02a597b283eacb9436cd2a2d15039a11a3299d" : {name: "elk"},
        //"0x54b525f1839ae10085de6affd0c549df1f1deef1" : "cbo", //没有factory
        //"0xd1216282b156a152fec1ac216804074860fe1508" : "ann", //没有factory
        "0x52c520ddc9d88a9a3e554a574b31caa9c0932c57" : {name: "hop", fee:98000},
        "0x68797130d8e63745761c524c33121fdd7290cb72" : {name: "u1", fee:99900},
        "0xd3b68a35002b1d328a5414cb60ad95f458a8cf92" : {name: "u2", fee:99800},
        "0x69004509291F4a4021fA169FafdCFc2d92aD02Aa" : {name: "u3"},
        "0xFc0D2D06Efe8d44F3EeCc8e1Df7c1509F7bA8e31" : {name: "u4", fee:99800},
        "0x77befde82eba4bdc4d3e4a853bf3ea4ffb49dd58" : {name: "u5", fee:99800},
        "0x3c1997d8738dcab7ed099105fcd61a9fe5f351dd" : {name: "u6"},
        "0x304EE5a9e1Fa788999fE9b7c852595Bf62C7E8E9" : {name: "u7"},
        "0xd30d3ac04e2325e19a2227cfe6bc860376ba20b1" : {name:"u8", fee:99800},
        "0xC6f863bCbecFD76cf17449573096F7aFb2d8bd07" : {name:"u9"},
        "0x2117e09f5a8035332146718a899ffcdb3393fd3a" : {name:"u10"},

        "0xb2c8d1e0626245a06bd5901063ab52f86f772bc4" : {name:"u11"},
        "0xe59597880dee2098b5cef70a7c807b4cc31df4eb" : {name:"u12"},
        "0x28a10fe91d4a8d0637999a903eef9ad5b1d9947c" : {name:"u13", fee:99750},
        "0x800e9a94491ee26c0d38ea24e18b506e9890f5c3" : {name:"u14", fee:99830},
        "0x9aadb4a3bfacff8aa60a2c63735cb8b94de7c57d" : {name:"u15", fee:99800},
        "0x1aec9e41230782b3b9dfd0b643bc8d202c32768a" : {name:"u16"},
        "0xdadaae6cdfe4fa3c35d54811087b3bc3cd60f348" : {name:"u17"},
        "0x8118dd9fed86523bf724e2ec5f56055da0668af4" : {name:"u18"},
        "0x77979e0e68242b768df2de711b339d61cd034a05" : {name:"u19"},
    }

    gasWatch = [
        "0xff0923db7d9f89cec1b7b0338d82e49023eba690",
        "0x1adda7e1f9b7b2a9ea7a5e1e9b93fadaec06ad0a",
        "0xf467513648c854e1f48573f99f0a4094128a245f",
        "0x1d93102c2c3584825b50198d14181bbf6bdf8046",
        "0x0302dd0aaa53123cc67b5b056616f0017c1e8311",
        "0x5ff1958268d15872659d3d58b692ad68468b5fc3", //残渣
        "0xc275cd1619b077abcac0ad023a52dace2f148131"
    ]


}