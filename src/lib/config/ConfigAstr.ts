import Config from "./Config";

export default class ConfigAstr extends Config {
    mainnet = {
        //wss://evm.astar.network hk:300ms, de:140ms jp:340ms vg:193ms io:150
        data : "https://evm.astar.network",
        //wss://astar.api.onfinality.io/public-ws
        //wss://astar-rpc.dwellir.com //de
        //wss://astar.public.blastapi.io

        //https://astar.public.blastapi.io
        //https://astar-rpc.dwellir.com //de
        //https://astar.api.onfinality.io/public
        //https://evm.astar.network

        //wss://astar.public.blastapi.io //没有订阅
        //wss://astar-rpc.dwellir.com (aws-de 38ms)(vg 120ms)
        //wss://astar.api.onfinality.io/public-ws (jp 11ms)(aws-de 35ms)(vg 148ms)
        //wss://1rpc.io/astr (vg 181ms)(jp 200ms)(aws-de 280ms)

        //wss : ["https://evm.astar.network"]
        //wss : ["wss://rpc.astar.network", "wss://astar.api.onfinality.io/public-ws", "wss://astar.blastapi.io/fccb123f-dbda-4b13-8687-842aee20b52c"],
        //wss : ["wss://astar.blastapi.io/fccb123f-dbda-4b13-8687-842aee20b52c"],
        wss : ["wss://rpc.astar.network", "wss://astar-rpc.dwellir.com"]
    };
    blockTime = 12;
    eth = "******************************************";
    stableTokens = ["******************************************",
                    //"******************************************",
                    "******************************************",
                    //"******************************************",
                    //"******************************************"
                ];
    //stableTokens = ["******************************************"];
    gasMin = 2;
    gasMax = 2002;
    feedAutoTimeHour = 6;
    
    feedMin = 35;
    feedMax = 40;
    feedPumpMulti = 1; //pump的填充gas百分比

    bot = {
        active:false,
        crazy:false,
        //address : "******************************************",
        //address : "******************************************", //pump fee3
        //address : "******************************************", //testPairPatch
        //address: "******************************************"
        //address: "******************************************", //0412 + 0715
        address : "******************************************", //1115
        sharingan: true,
    }
    pump = {active:true, gasMax:2001, countMin:1,  rewardMin:0.1 }
    trash = {active:true, bid:false, delay:500, rewardMin:0.1 }

    tokens = {
        "******************************************" : {name:"wastr",   isEth:true, ignore:true, keep:5000, price:0.03, eq:["******************************************"]},
        "******************************************" : {name:"wastr.a", isEth:true, ignore:true, keep:5000, price:0.03, eq:["******************************************"]},

        "******************************************" : {name:"usdt",    ignore:true, keep:5, eq:["******************************************", "******************************************"]}, //d6
        "******************************************" : {name:"usdc.ce", ignore:true, keep:5, eq:["******************************************", "******************************************"]}, //d6
        "******************************************" : {name:"usdt.ce", ignore:true, keep:5, eq:["******************************************", "******************************************"]}, //d6

        "******************************************" : {name:"dai", ignore:true, keep:500},
        "******************************************" : {name:"bai", ignore:true, keep:500},
        "******************************************" : {name:"busd", ignore:true },
        //"0xDe2578Edec4669BA7F41c5d5D2386300bcEA4678" : {name:"arsw"},
        //"0xffffffff00000000000000010000000000000005" : {name: "intr"},
        //"0xffffffff00000000000000010000000000000003" : {name: "glmr"},
    };
    tokenBlackList = {
        "0xe1af4d744e2a66cd07c474bed167960be872fcd9" : "val"
    }

    routers = {
        "0xe915d2393a08a00c5a463053edd31bae2199b9e7" : {name: "arth"},
        "0x4e231728d42565830157fffabbb9c78ad5152e94" : {name: "u1"},
        "0xF4e6251852144B9b3e268a175FcE8C0C47e6419E" : {name: "star"},
        "0xd38f0bc4a0728d2a1fcaafa364f7c8422ba1cd14" : {name: "solar", fee:99750},
        "0xdac282ca5534dc5936165197263e5f08b2c4577c" : {name: "versa", fee:99800},
        "0xe7009A8E41E8d2101AFB6c9e40D123621c4DAfCe" : {name: "polka"},
        "0x06c04b0ad236e7ca3b3189b1d049fe80109c7977" : {name: "beast", fee:99800},
        "0x0fd60f0B13F7d816aE2DF1B9a4B62a9d94FbCac5" : {name: "pandora"},
        "0xa4183Dca1d46580970d19CB25f4735065018e4E9" : {name: "yumi", fee:99800},
        "0xf5016C2DF297457a1f9b036990cc704306264B40" : {name: "u2"},
        //"0xd9fb513766E60a569697d1f17aE466b0Cb887454" : {name: "u3"}, //no factory
        "0xa5e48a6e56e164907263e901b98d9b11ccb46c47" : { name: "u4", fee:99750},
        //"0xd7f74d2bb63d9c45e7ea8e755dec2fb935cb7ada" : {name:"arth_v3"}
        //"0x1f0Edf66cd7185E3CDB25bEf05726288d7E2dd77" : {name:"arth_multi"},
    };

    gasWatch = [
        "0x3196dBb1f93eb5a66Ba3b7431f4858cC76978317"
    ]
    /*
    blackListPair = [
        "0x56ce6643edd621ecd904d9b6c9e88745a125af6d", //val-usdc
        "0x50bc0bf7f5a9fb597efc4c247a3870ed48715a5c", //val-wastr
    ];
    */

    blackList = [
        "0x2EC14FBF2977d8cd2A2134436eA4201019cb0d34",
        "0x57d060b3650f238BD791F12bc438200Ded3BBf3A",
        "0x430b6e393f2b2af7c045f00b2c0133c0f7bfed3a",
        "0xf9704647EdF2e9071e2783371a35249f6e79a1FC"
    ]

    blackListPump = [
        "0xd1f4047d537d7e53416a84b872d90ed5751480b1", //fake
        "0x601c79fbe8a8d5d40c8453d9d8ff3ecf9ae3a53a",
        "0x8eb8d0290ce4a4680f388f4ec8777991f21a9b06",
        "0xa83a223821184580839814275b080c50131aa920"
    ]
}