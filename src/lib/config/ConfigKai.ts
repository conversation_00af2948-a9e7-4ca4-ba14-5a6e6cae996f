import Config from "./Config";

export default class ConfigKai extends Config {
    mainnet = {
        data : "https://rpc.kardiachain.io",
        wss : ["wss://ws.kardiachain.io"],
    };

    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 1.01;
    gasDelta = 0.12;
    gasMax = 300000;
    blockTime = 5;

    feedAutoTimeHour = 12;

    feedMin = 80;
    feedMax = 90;

    bot = {
        active : false,
        crazy : false,
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************", //new pair checker
        //address : "******************************************", //pump fee
        //address : "******************************************", //pump fee2
        //address : "******************************************", //pump fee3
        //address : "******************************************", //testPairPatch
        //address : "0xC49510087191fA2fd34afd303d1a13Be1b4562BF", //0412 + 0715
        address : "0x509fcc26cDf21ed6f260793F3bb113dac67481c2", //1115
        sharingan : true,
    }
    pump = {
        active: true,
        rewardMin : 0.0001,
        gasMax :10000,
        countMin:3,
        countMax:7,
    }

    trash = {active:true, rewardMin:0.0001, bid:true, delay:1000}
    
    tokens = {
        "******************************************" : { name:"wkai", ignore:true, cex:true, keep:10000, price:0.001},
        "******************************************" : { name: "kusd-t", ignore:true, cex:true, keep:20 },
        "******************************************" : { name:"usdt", ignore:true, cex:true, keep:20 },
        "******************************************" : { name:"usdc", ignore:true, cex:true, keep:20}, //multichain
        /*
        "******************************************" : { name:"eth", cex:true, ignore:false},
        "******************************************" : { name:"busd", cex:true, ignore:false},
        "******************************************" : { name:"bnb", cex:true, ignore:false},
        "******************************************" : { name:"beco" },
        "******************************************" : { name: "kdx" },
        */

        //"******************************************" : { name:"hng" },
        //"******************************************" : { name:"dfl" },
    };
    routers = {
        "******************************************" : {name: "kaidex", fee: 99750},
        //"******************************************" : "u0"
        //"******************************************" : "polka",
        "******************************************" : {name: "beco"},
        "******************************************" : {name: "u2", fee: 99750},
        "******************************************" : {name: "u3"},
        "0xbAFcdabe65A03825a131298bE7670c0aEC77B37f" : {name: "kaidex"},
        "0x3de8dca6f14635a6fdf3e77c40e4f90602b72764" : {name: "u5"},
        "0xcaa3af1b19166277dac631948b5fe94f6a4ed4e8" : {name: "u6"},
        "0x636fab0c916c294f1987fee14dc88c427af09f94" : {name: "u7"},
    };
    gasWatch = [
        "0x53B89904C6df25c487FfA79661a4f2F3765115e1",
        "0xefb8f5a7479a6532a71f70919c00a18ae8a164d6", //残渣
        "0x8f93f3277A0790bC63b79D039af948ddB38465B2",
        "0x52b57e57d62aba7c13db4d3c4eefb980bb36a358",
        "0x9140adceabb0f98a8d3639e42b56bf23d4cae6d0",
        "0x52b57e57d62aba7c13db4d3c4eefb980bb36a358",
        "0xC0a43FCBE2FD2D9a9f0689118635A08A8FB8a401",
        "0x59dEAa44Aa89d7DBaC1Ae1EA5ed2500a78965601"
    ]
    blackList = [
        "0x01a9C8246e16873eA7468FEb9A469eEB0Da59A23", //ARKN hack
    ]
}