import Config from "./Config";

export default class ConfigArb extends Config {
    mainnet = {
        data : "https://arb1.arbitrum.io/rpc",
        wss : ["ws://127.0.0.1:8548", "https://arb1.arbitrum.io/rpc"]
        //wss : ["https://arb1.arbitrum.io/rpc"]
    };
    blockTime = 12;
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************"];
    gasMin = 0.1001;
    gasMax = 0.3;
    feedAutoTimeHour = 6;
    onlyActivePair = true;

    feedMin = 0.0026;
    feedMax = 0.003;
    feedPumpMulti = 1; //pump的填充gas百分比

    bot = {
        active:false,
        crazy:false,
        address : "******************************************", //swap with fee, support v1, testPair pump2
    }
    pump = {active:false, gasMax:501, countMin:1, countMax:7}
    trash = {active:true, bid:false, delay:0, rewardMin:10}

    tokens = {
        "******************************************" : {name:"weth",   ignore:true, keep:5000, price:1700},

        "******************************************" : {name:"usdc", ignore:true, keep:500}, // eq:["******************************************"]},
        "******************************************" : {name:"usdt", ignore:true, keep:500}, //eq:["******************************************"]},
    };
    tokenBlackList = {}

    routers = {
        //"******************************************" : {name: "joe"}, //not v2
        "******************************************" : {name:"joeV1"},
        "******************************************" : {name: "sushi"},
        "******************************************" : {name: "cam"}, //fee for pair
        "******************************************" : {name: "sharky", fee:99750},
        "******************************************" : {name: "sliz", fee:99750}, //stable version:3
        "******************************************" : {name: "alien", fee:99750},
        "0xc640fd6f9baa1fce48ed1abe823355e0356e60a9" : {name: "arbi",  fee:99750},
        "0x38eed6a71a4dda9d7f776946e3cfa4ec43781ae6" : {name: "oreo",  fee:99750},
        "0xcdaec65495fa5c0545c5a405224214e3594f30d8" : {name: "fish"},
        "0x8e72bf5a45f800e182362bdf906dfb13d5d5cb5d" : { name: "xcal"},

        
    };

    gasWatch = [
        "0x7bC3AF5BfC6c6649Fa3e08F1c4B45DCBb327D9d7",
        "0x0000900e00070d8090169000D2B090B67f0c1050",
        "0x50c269937F80C630D15F92F84DE17d02206038CD",
        "0x5D6543ec4591e1d898025bb8BFf4f952aA574B6A"
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}