import { macro } from "../macro";
import Config from "./Config";

export default class ConfigKcc extends Config {

    mainnet = {
        data : "https://rpc-mainnet.kcc.network",
        //wss://rpc-ws-mainnet.kcc.network (ali-jp 11ms) (aws-jp 14ms)
        //ws://127.0.0.1:18546
        //wss : ["wss://rpc-ws-mainnet.kcc.network", "ws://127.0.0.1:18546"],
        wss : [ "wss://rpc-ws-mainnet.kcc.network"],
    };
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************"];
    gasMin = 1.01;
    gasDelta = 0.1;
    gasMax = 2000;

    onlyActivePair = true;

    feedMax = 0.6;
    feedMin = 0.4;
    feedPumpMulti = 1; //pump的填充gas百分比
    feedAutoTimeHour = 12;

    bot = {
        active : false,
        crazy : false,
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************", //new pair checker
        //address : "******************************************", //pump fee3
        //address : "0x55374f0d5e69a08c3360da1b19b8d04683b4c57f", //swap with fee, support v1, testPait, pump2
        //address : "0x5c2350D2c808c93251D43C68443317680fcafED7", //0412 + 0715
        address : "0xf4C0C06C0c897F41DaCb1d22C6e1dE3EcC4Ee0cc", //1115
        sharingan : true,
    };
    pump = {
        active: true,
        greed: true,
        gasMax :501.2,
        countMin:2,
        countMax:7,
    }
    trash = { active:true, bid:true, delay:500 }

    tokens = {
        "******************************************" : { name: "wkcs", ignore: true, keep: 10, price:8, max:150 },
        "******************************************" : { name: "usdt", ignore: true, keep: 70, eq: ["******************************************"] },
        "******************************************" : { name: "usdc", ignore: true, keep: 70, eq: ["******************************************"] },
        "******************************************" : { name: "eth", price : 2000, cex:true },
        //"******************************************" : { name: "ape", cex:true },
        "******************************************" : { name: "mjt"},
        "******************************************" : { name: "kus"},
        //"******************************************" : { name: "btck", cex:true},
        "******************************************" : { name: "cooha"},
        //"******************************************" : { name: "busd", cex:true},
        //"******************************************" : { name: "bnb", cex:true},
        "******************************************" : { name: "cooga"},
        "******************************************" : { name: "mana", cex:true},
        "******************************************" : { name: "sand", cex:true},
        //"******************************************" : { name: "elk", cex:true},
        //"******************************************" : { name: "babyDoge", cex:true},
        //"******************************************" : { name: "crv", cex:true},
        //"******************************************" : { name: "uni", cex:true},
        //"******************************************" : { name: "cfx", cex: true},
        //"******************************************" : { name: "link", cex: true},

        //"******************************************" : { name: "ogx"},
        //"******************************************" : { name: "wbtc", cex:true},
        //"******************************************" : { name: "boring", cex:true},
        "******************************************" : { name:"sax" },
    };
    tokenBlackList = {
        "******************************************" : "koge", //fee
        "******************************************" : "kloki", //fee
        "******************************************" : "babyKloki", //fee
        "******************************************" : "KukafeToken",//fee
        "******************************************" : "kshib",//fee
        //"******************************************" : "mls", //波段
        "******************************************" : "kccpad", //fee
    };
    blackList: string[] = [
        "******************************************",
        "******************************************",
    ];
    routers = {
        "******************************************" : {name: "mjt"},
        "******************************************" : {name: "koffee"},
        "0xa58350d6dee8441aa42754346860e3545cc83cda" : {name: "kuswap"},
        "0x5DDac4C73b15353dc4a00DaeAfb38631a7cBd389" : {name: "elk"},
        "0x98bdEA86e7D6a7117Ffdd8AB4b14c7eE0332cDC0" : {name: "u1", fee:99750},
        "0x6074e20633D2D8FbdF82119867a81581cabe06dD" : {name: "kafe", fee:99800},
        "0x8DEdbD1ad1aCf3Ddf5918bEceDE6EA0Ab7c002B5" : {name: "bol", fee:99800},
        "0xB2A0e133F39761D6B7488171a3b263DF2203ad4A" : {name: "gem", fee:99750},
        "0x2DF7AEb4C3F15D9c5Db595e8118ff6dB4A154d6D" : {name: "papr"},
        "0x81148af4A2c4A4857Cc56faBEC83B672fc073888" : {name: "ksf", fee:99750},
        "0x8BC3731BAf0992B7765D04FEe4953e4F53f69909" : {name: "kape", fee:99800},
        "0x4844E701F85e2a5A4270Ee8afd3F00dcFD4B8F5a" : {name: "kwoof"},
        "0x748e7a1D2d2566120787f7134588180bc591C22f" : {name: "kuku",fee:99750},
        "0x213c25900f365F1BE338Df478CD82beF7Fd43F85" : {name: "kandy",fee:99800},
        "0x8390CE2dc3D454fC8da387efb514eb281321eaBf" : {name: "kex"},
        "0xf742609f4cafeef624816e309d770222aa8a55cc" : {name: "u3", fee:99800},
        //"0xB3184913A87EEfe616788FBA3A02128A01188Ef2" : {name:"emi"},
        "0x6074761ae58b88ac6b12396a290164e0e20b46fe" : {name: "u4" },
        "0x9dDB61E7aD149b14Fb33D6e5Afbdf79b5a3D7CC7" : {name: "shiba", fee:99750},
        "0xa6c415a3B4E51F4fF0090A785BD6f4Ba7B6253aa" : {name: "ork", fee:99750},
        "0xcb4a650f7081ae4ffce93f473dc80fdd0065d309" : {name:"u5", type : macro.ROUTER_TYPE.V2_STABLE },
    };
    gasWatch = [
        "0x5fF73Cc0E517863358E4BAD9A07B6543CF819137",
        "0x4a1e62709153a05b8cde8670dee4861e7514ac84",
        "0xA67689c8390842e0Bee3da75218eD107d86ef3e1",
        "0xe2c6d3c2522ac9af55e3c139c9ba3e484d722bb5",
        "0x89e1920b861130e05487dec4d1a0e341debc4c63",
        "0xcdec5759135d47e1627cae8a9fca091033dd8db0",
        "0xce47a617227dc7a9ab332e9887152fe6eb857df6"
    ]

}