import { macro } from "../macro";
import Config from "./Config";

export default class ConfigNova extends Config {
    mainnet = {
        data : "https://nova.arbitrum.io/rpc",
        wss : ["ws://127.0.0.1:9548", "https://nova.arbitrum.io/rpc"]
    };
    blockTime = 1;
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************"];
    gasMin = 0.1001;
    gasMax = 0.3;
    feedAutoTimeHour = 6;
    onlyActivePair = true;

    feedMin = 0.0045;
    feedMax = 0.005;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 2;

    bot = {
        active:false,
        crazy:false,
        //address : "******************************************", //swap with fee, support v1, testPair pump2
        address : "******************************************", //0412 + 0715
    }
    pump = {active:false, gasMax:501, countMin:1, countMax:7}
    trash = {active:true, bid:false, delay:0, rewardMin:0.01}

    tokens = {
        "******************************************" : {name:"weth",   ignore:true, keep:1, price:2000},
        "******************************************" : {name:"usdc", ignore:true, keep:500}, //d6
        "******************************************" : {name:"dai", ignore:true, keep:500}, //d18

        "******************************************" : {name:"usdt", ignore:true, keep:500},
    };
    tokenBlackList = {}

    routers = {
        "******************************************" : { name: "sushi" },
        "******************************************" : { name: "rcp" },
        "******************************************" : { name: "arb" },
        "******************************************" : { name: "arch" , type:macro.ROUTER_TYPE.V2_STABLE }, //fee:99950
        
    };

    gasWatch = [
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}