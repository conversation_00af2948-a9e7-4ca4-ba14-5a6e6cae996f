import Config from "./Config";

export default class ConfigEch extends Config {
    //https://docs.ech.network/quick-start#connect-to-echelon-with-metamask
    mainnet = {
        data : "",
        wss : ["wss://ws.ech.network", "b|https://rpc.ech.network"],
    };

    eth = "******************************************";
    stableTokens = ["******************************************"];
    stablePumpToken = "******************************************";
    onlyActivePair = true;

    gasMin = 1;
    gasDelta = 0.00001;
    gasMax = 30000;

    bot = {
        active : false,
        address : "******************************************",
    };

    pump = {
        active: true,
        greed: true,
        gasMax :30000,
        countMin:1,
        countMax:1,
    }

    tokens = {
        "******************************************" : {name:"wech", ignore:true, price:0.1},
        "******************************************" : {name:"bUsdc", ignore:true, price:0.1},

    };
    routers = {
        "******************************************" : {name: "ech"},
        "******************************************" : {name: "dfy"},
    };
}