import { macro } from "../macro";
import Config from "./Config";

export default class ConfigOp extends Config {
    mainnet = {
        data : "https://mainnet.optimism.io",
        //wss : ["https://mainnet.optimism.io"],
        wss : ["https://mainnet.optimism.io", "wss://optimism.llamarpc.com/sk_llama_73b12dc543e0c7777691286f4897112b"]
    };
    blockTime = 5;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 0.015;
    gasMax = 1.01;
    feedAutoTimeHour = 2;
    disableSubscribeLog = true;

    feedMin = 0.0004;
    feedMax = 0.0005;
    feedPumpMulti = 1; //pump的填充gas百分比
    maxOperator = 1;

    bot = {
        active:true,
        crazy:false,
        address : "******************************************", //1115+0123
    }
    pump = {active:true, gasMax:1.01, countMin:1, countMax:1, rewardMin: 0.01}
    trash = {active:true, bid:false, delay:100, rewardMin:0.01}

    tokens = {
        "******************************************" : {name:"weth", ignore:true, keep:0.001, isEth:true, price:4000},
    };

    routers = {
        "******************************************" : { name: "elk" },
        "******************************************" : { name: "u1"},
        "******************************************" : { name: "u2",  skipUpdate:true}, //v3
    };

    gasWatch = [
    ]
    blackListPair = [];

    blackList = [
        "******************************************"
    ]

    blackListPump = []
}