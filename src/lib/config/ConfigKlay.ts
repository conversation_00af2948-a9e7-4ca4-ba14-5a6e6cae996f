import Config from "./Config";

export default class ConfigKlay extends Config {
    mainnet = {
        //data : "https://klaytn01.fandom.finance",
        data : "https://public-en.node.kaia.io",
        //"wss://public-node-api.klaytnapi.com/v1/cypress/ws" getlog最快
        //"wss://klaytn02.fautor.app/ws/" tx最快
        //https://docs.klaytn.foundation/dapp/json-rpc/public-en
        //wss : ["wss://public-en-cypress.klaytn.net/ws"],
        wss: ["https://rpc.ankr.com/kaia", "wss://public-en.node.kaia.io/ws"],
        //wss: ["https://public-en.node.kaia.io"],
        //日本30ms
        //wss : ["wss://public-node-api.klaytnapi.com/v1/cypress/ws", "wss://public-en-cypress.klaytn.net/ws"]
    };

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************",  "******************************************"];
    
    onlyActivePair = false;
    disableSubscribeLog = true;
    blockTime = 3; //每个区块的时间

    feedMin = 3.8;
    feedMax = 4;
    feedPumpMulti = 1; //pump的填充gas百分比
    feedAutoTimeHour = 4;

    //updateAllPairsDelay = 5000;


    bot = {
        active : false,
        crazy: false,
        //address : "******************************************", //pump fee3
        //address : "******************************************", //testPair
        address : "******************************************", //1115 ******************************************
    };

    pump = {
        active:true,
        gasMax :1000.2,
        countMin:1,
        countMax:1,
        rewardMin:0.001,
    }

    trash = {active:true, rewardMin:0.001, delay: 100}

    tokens = {
        "******************************************" : {name:"wklay.d", ignore:true, isEth:true, price:0.1, keep:100, eq : ["******************************************"]}, //defi
        "******************************************" : {name:"wklay", ignore:true, isEth:true, price:0.1, keep:100, eq: ["******************************************"]}, //claim
        "******************************************" : {name:"oUsdt", ignore:true, price:0.2}, //hacked
        "******************************************" : {name:"jewel", ignore:true, price:0.1},
    };
    routers = {
        //"******************************************" : {name: "klay"},
        //"******************************************" : {name:"koko"},
        "******************************************" : {name: "cla"},
        "******************************************" : {name: "dfk"},
        "******************************************" : {name: "u1", eth:"******************************************"},
        "******************************************" : {name: "para", fee:99800},
        "******************************************" : {name: "u2", fee:99750},
        "******************************************" : {name: "u3" },
        "******************************************":{name: "u4" },
        "******************************************":{name: "u5" },
        "******************************************":{name: "u6", fee:99800 },
        "******************************************":{name: "u7" },
        "******************************************":{name: "u8" },
        "******************************************":{name: "u9", fee:99750 },
        "******************************************":{name: "u10" },
        "******************************************":{name: "u11" },
        "******************************************":{name: "u12", fee:99750 },
        "******************************************":{name: "u13" },
        "0x41b5e9bcde96d5fec4a6bba79e1a6cb824483670":{name: "u14", fee:99500 },
        "0x1ae44dbbf06025e4f72c632aced1a5195233fa71":{name: "u15" },
        "0xb837807be2df4ed47bed00735c4761839f3fe761":{name: "u16", fee:99800 },
        //"0x5ea3e22c41b08dd7dc7217549939d987ed410354":{name:"u17",},
    };
    gasWatch = [
        "0x760a44ec5be3132660b222e4d422243dd2f0fa4d",
        "0xf4a3b75379e7a018cf409f683ade9dd2752e66db"
    ]
}