import Config from "./Config";
export default class ConfigXdai extends Config {

    mainnet = {
        //"wss://rpc.gnosischain.com/wss" gc华沙 20ms
        data : "https://rpc.gnosischain.com",
        //https://rpc.gnosischain.com/
        //https://dai.poa.network/
        //https://xdai-rpc.gateway.pokt.network
        //"https://rpc-df.xdaichain.com"
        //ws://xdai.poanetwork.dev:8546
        //wss://xdai.poanetwork.dev/wss
        //wss://rpc.gnosischain.com/wss //vg 20ms
        //wss : ["https://dai.poa.network/"],
        //wss : ["wss://rpc.ankr.com/gnosis/ws/c959fef5bb6d732dc90f685ebea9bf31b73f21ba843c15ccf222df5704d112c3", "wss://rpc.gnosischain.com/wss"], //jp
        //wss://gno.getblock.io/mainnet/?api_key=6dda325e-7c3d-4982-abec-b3c1a92a555a //de
        //wss : ["https://rpc.ankr.com/gnosis"]
        wss : ["wss://rpc.gnosischain.com/wss"],
        //wss : ["https://rpc.gnosischain.com"],
        //wss : ["wss://rpc.gnosischain.com/wss", "wss://rpc.gnosischain.com/wss", "b|wss://gnosis-mainnet.blastapi.io/79d51d53-2a7f-435e-9e2d-570c0cb7207e", "b|https://rpc.ankr.com/gnosis", "b|https://gnosis-mainnet.public.blastapi.io"],
        //wss : ["wss://rpc.gnosischain.com/wss"],
    };
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];
    gasMin = 1;
    gasDelta = 5.233;
    gasMax = 2000;

    feedMin = 3.5;
    feedMax = 4;
    feedAutoTimeHour = 6;
    //updateAllPairsDelay = 500;

    bot = {
        active : false,
        crazy : false,
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address: "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************", //new pair checker
        //address : "******************************************", //pump fee3
        //address : "******************************************", //test pair patch
        //address : "******************************************", //0412 + 0715
        address : "******************************************", //1115
        sharingan : true,
    };
    pump = {
        active:true,
        gasMax :200.1,
        countMin:3,
    }
    trash = {active:true, rewardMin:0.001, bid:true, delay:200}

    tokens = {
        "******************************************" : { name: "wxdai", ignore: true, isEth:true, keep:100, max:1000 },
        "******************************************" : { name: "weth",  ignore: true, price: 2200 },

        "******************************************" : { name: "usdt", ignore: true, },
        "******************************************" : { name: "usdc", ignore: true, },
        "******************************************" : { name: "usdc", ignore:true, cex:true},
        "******************************************" : { name: "busd", ignore: true, },
        "******************************************" : { name: "mai",  ignore: true, },
        
        "******************************************" : { name: "husd", cex:true, ignore:true},
        "******************************************" : { name: "tusd", cex:true, ignore:true},
        
        /*
        "******************************************" : { name: "gno", ignore:true,  cex:true },
        "******************************************" : { name: "bnb"},
        "******************************************" : { name: "data"},
        "******************************************" : { name: "hopr"},
        "0x3a97704a1b25F08aa230ae53B352e2e72ef52843" : { name: "agve"},
        "0xD3D47d5578e55C880505dC40648F7F9307C3e7A8" : { name: "dpi"},
        "0x21a42669643f45Bc0e086b8Fc2ed70c23D67509d" : { name: "fox"},
        "0x82dFe19164729949fD66Da1a37BC70dD6c4746ce" : { name: "bao"},
        "0x71850b7E9Ee3f13Ab46d67167341E4bDc905Eef9" : { name: "hny"},
        "0xeEeEEb57642040bE42185f49C52F7E9B38f8eeeE" : { name: "elk", cex:true},
        "0xc9B6218AffE8Aba68a13899Cbf7cF7f14DDd304C" : { name: "clny" },
        "0x71850b7E9Ee3f13Ab46d67167341E4bDc905Eef9" : { name: "hny" },
        "0xb7D311E2Eb55F2f68a9440da38e7989210b9A05e" : { name: "stake" },
        "0xE1C110E1B1b4A1deD0cAf3E42BfBdbB7b5d7cE1C" : { name: "elk", cex:true },
        "0x1e16aa4Df73d29C029d94CeDa3e3114EC191E25A" : { name: "xmoon" },
    
        "0x3a97704a1b25F08aa230ae53B352e2e72ef52843" : { name: "agve", cex:true },
        "0x7eF541E2a22058048904fE5744f9c7E4C57AF717" : { name: "bal", cex:true},
        "0x4c68041898bfEfbfCc4253fbE8eD30830E6eb767" : { name: "rune", cex:true},
        "0xCa8d20f3e0144a72C6B5d576e9Bd3Fd8557E2B04" : { name: "wbnb", cex: true},
        "0x4f4F9b8D5B4d0Dc10506e5551B0513B61fD59e75" : { name: "giv" },
        "0x21a42669643f45Bc0e086b8Fc2ed70c23D67509d" : { name: "fox" },
        "0x18E9262e68Cc6c6004dB93105cc7c001BB103e49" : { name: "raid", ignore: true },
        "0xb0C5f3100A4d9d9532a4CfD68c55F1AE8da987Eb" : { name: "haus", ignore:true },
        "0xc60e38C6352875c051B481Cbe79Dd0383AdB7817" : { name: "node" },
        "0x532801ED6f82FFfD2DAB70A19fC2d7B2772C4f4b" : { name: "swapr" },
        "******************************************" : { name: "hopr" },
        "0x7CC4d60a3C83e91d8c2ec2127A10Bab5c6Ab209d" : { name: "sxp", },
        "0x7f7440C5098462f833E123B44B8A03E1d9785BAb" : { name: "inch1", cex:true},
        "0x524B969793a64a602342d89BC2789D43a016B13A" : { name: "donut",},
        "0x38Fb649Ad3d6BA1113Be5F57B927053E97fC5bF7" : { name: "xcomb", },
        "0x703120F2f2011a0D03A03a531Ac0e84e81F15989" : { name: "uncl", },
        "0x0116e28B43A358162B96f70B4De14C98A4465f25" : { name: "uncx", },
        "0xE2e73A1c69ecF83F464EFCE6A5be353a37cA09b2" : { name: "link", cex:true},
        "0x48b1B0d077b4919b65b4E4114806dD803901E1D9" : { name: "dip", },
        "0x7122d7661c4564b7C6Cd4878B06766489a6028A2" : { name: "matic", cex:true },
        "0x911F196Ed489e41C8B45B5C56FEce021C27a6159" : { name: "cmp" },
        "0xfa57AA7beED63D03Aaf85fFd1753f5f6242588fb" : { name: "mps", ignore: true },
        "0x0da1a02CDF84C44021671d183d616925164E08Aa" : { name: "ren" },
        "0x7838796B6802B18D7Ef58fc8B757705D6c9d12b3" : { name: "mana", cex:true},
        "0x226bCf0e417428a25012d0fA2183d37f92bCeDF6" : { name: "zrx"},
        "0x99C9dF4BAE3aE5630A146CaE3FdeC791aB0440c6" : { name: "agf"},
        "0x12dd44fC0dC52cBa611e6d9C27D34Cd6811c8531" : { name: "ada", cex:true},
        "0x2bF2ba13735160624a0fEaE98f6aC8F70885eA61" : { name: "FRACTION" },
        "0x8A95ea379E1Fa4C749dd0A7A21377162028C479e" : { name: "audio"},
        "0x51732a6fC4673d1aCca4c047F5465922716508Ad" : { name: "ocean", cex:true},
        "0xd9Fa47e33d4Ff7a1ACA489DE1865ac36c042B07a" : { name: "hex" },
        "0x479e32cDFF5F216f93060700C711D1cC8E811a6B" : { name: "trips" },
        "0x0aCD91f92Fe07606ab51EA97d8521E29D110fD09" : { name: "cel", cex:true},
        "0x4e1a2bFfe81000F7be4807FAF0315173c817d6F4" : { name: "mask", cex:true},
        "0x0b7A1c1A3D314DCC271EA576dA400B24e9ad3094" : { name: "nmr" },
        "0x75886F00c1a20Ec1511111Fb4Ec3C51de65B1fe7" : { name: "fix"},
        "0x7B6F95D7575Ad408f264520447b8c39e11620f23" : { name: "print", maxLp:1},
        */
    };
    tokenBlackList = {
        "0x57e93BB58268dE818B42E3795c97BAD58aFCD3Fe" : "rare", //可能有fee
        //"0xfa57AA7beED63D03Aaf85fFd1753f5f6242588fb" : "mps", //有波段，容易亏钱
        "0xC32a3c867aBAd28d977e1724f92D9684fF3d2976" : "airs", //fee
    };
    routers = {
        "0x1b02dA8Cb0d097eB8D57A175b88c7D8b47997506" : {name: "sushi"},
        "0x1c232f01118cb8b424793ae03f870aa7d0ac7f77" : {name: "honey"},
        "0x6093AeBAC87d62b1A5a4cEec91204e35020E38bE" : {name: "bao"},
        "0xE43e60736b1cb4a75ad25240E2f9a62Bff65c0C0" : {name: "swapr"},
        "0xb18d4f69627F8320619A696202Ad2C430CeF7C53" : {name: "levin"},
        "0xe5759714998e8B50A33c7333C04C2d02e5dcE77f" : {name: "elk"},
        "0x5a673720fBdDB078Bc26958e64fE03A3Ac8836d7" : {name: "xion"},
        "0xdADaae6cDFE4FA3c35d54811087b3bC3Cd60F348" : {name: "xdex"},
        "0xf65c984f166553942C66BCc1FE74cFD3f26523e6" : {name: "u1"},
        "0xDdaEB30f3dA1612900e1E26386a009F4576A175C" : {name: "u2"},
        "0x548Bd7B53bB2b24Be65Bc14A9aaa43131Bf644C6" : {name: "u3", fee:99750},        
        "0xb91e4a4f33d176e2d8fcbe2a34d5948602633730" : {name: "u4"},
        "0xC9483Ea9f0abDacc7D313A0148767Dc8d259A621" : {name: "u5"},
        "0xf0347ad4fcb6620cb6c0bf3f33c7c4d857055207" : {name: "u6", fee:99800},
        "0x0689be64c84299622b3744c32af5Bc9b3E45e2D5" : {name: "o3"},

    };
    gasWatch = [
        "0xcbDaf1B4bA7FbC093B5b6858BFAE436471762C64",
        "0x5de684af977388e54906C0068948537580A22F0A", //搬砖
        //"0x5fF1958268D15872659d3D58B692AD68468B5fc3", //残渣竞价搬砖
        "0x9E310E650F602B6D8606d6De7B55EC37C6ed32F8", //搬砖
        "0xB108Ac5f615b75ab0252d551fD15918C41E1A755",
        "0x9CEeFC835f540894c4E134BE1409e300269E0299", //搬砖
        "******************************************", //残渣竞价
        "0x8FA7B3A28494D52C53d4346B10cE0782CB0d37Bb", //残渣竞价
    ]

    blackList = [
        "0x89c59fD0CC329a427Df6C01848A2ffc82cbfce0A", //买卖交易多次
        "0x4b4a7708F6B64cBBf592d0abFAAE07180239Ae97",
        "0x240396CE189bA01eF2bE2cfd1816989962D2aF6E",
    ]

    blackListPump = [
        "0x4b4a7708F6B64cBBf592d0abFAAE07180239Ae97", //买卖多次bless
        //"0x253ba6c8b005ae3989b5D02331518223222bDC96", //机器人
        //"0xd676432A77cfe7bbF5a048E375557cC18e295aE7",
        //"0x253ba6c8b005ae3989b5D02331518223222bDC96"
    ]
}
