import Config from "./Config";

export default class ConfigSama extends Config {
    mainnet = {
        //在gc-nl
        data : "https://rpc.exosama.com",
        wss : ["wss://rpc.exosama.com", "wss://rpc.exosama.com"]
    };

    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];

    blockTime = 5; //每个区块的时间
    gasMin = 1.5;
    gasMax = 200000.1;
    gasDelta = 0.2500001;


    feedMin = 18;
    feedMax = 20;
    feedPumpMulti = 1; //pump的填充gas百分比
    feedAutoTimeHour = 4;


    bot = {
        active : true,
        crazy: false,
        //address : "******************************************", //pump fee3
        //address : "******************************************", //pump fee3 with junk //有残留
        address : "******************************************", //swap with fee, support v1, pump2
    };

    pump = {
        active:true,
        gasMax :1000.2,
        countMin:2,
        countMax:2,
        rewardMin:0.001,
    }

    trash = {active:true, rewardMin:0.001, delay:500, bid:true}

    tokens = {
        "******************************************" : {name:"wsama", ignore:true, price:0.045, keep:3000},
        "******************************************" : {name:"usdc", ignore:true, keep:60},
        "0xE3F5a90F9cb311505cd691a46596599aA1A0AD7D" : {name:"usdt", ignore:true, keep:60},
        /*
        "0xb296335B6d4AB9F5e5Dc4bf115621c14ab70136C" : {name:"PUMPK"},
        "0xc9e0DBcF775519D49cd46afa6CfA60AAcAe9F539" : {name:"DON", maxLp:1},
        "0x76ba6821A834E2d30A699312829f8204830CCF5B" : {name:"BLOOD"},
        "0x829191FEC0f25a27c5bDCdBbD0e37E6342b39945" : {name:"IRON"},
        "0xff6ca2a78E4Dd4871256cc5B63dC8dbf02a36F3D" : {name:"DNA"},
        "0x549ab43056E3489335b47927FFcd4825ce484bA3" : {name:"GOLD"},
        "0x122EBE2B679cF54Bc8a6e89c1009714b354e2d10" : {name:"STONE"},
        */

        "0xD295D1BCcE9654191291367117a16DeC5723Dbd5" : {name:"exo", ignore:true}, //fee
        "0x96335a11FDDB0BFfFC0866A95F5611Ed3Ba7828b" : {name:"BSAMA", ignore:true}, //fee
        "0x351293b38CbF0ddc7590a2F59646224ec026e0F9" : {name:"GOTCHI", ignore:true}, //fee
    };
    routers = {
        "0xbe4e0322a2145169edE5c1bcF8b90A11f8346ec4" : {name: "khaos", fee:99750},
        "0xb8be7Fe5dB23382798A3c00e08927A3826D1Ed76" : {name:"exo"},
        "0x7ffce8523272ba19ba7b97b8f94832adaf9f29ce" : {name:"u1"},
        "0xf6c7c542ad2fd139be7926bcf4d7ce91b756fcad" : {name:"u2", fee: 99750},
    };

    whiteListPair = {
        "0x1a639841f26157282a2555e456e9bc41417ad17f" : {fee0:0, fee1:5000}, //USDC-EXO
        "0x72797db0E6556082DbB08926e67AA2ADD6Bf5E62" : {fee0:0, fee1:5000}, //WSAMA-EXO
        "0x833A211AC2F7Ec8380dc2e3deBc061dE737Df0F7" : {fee0:6000, fee1:5000}, //BSAMA-EXO
        "0x6966B85d09409bfbd45B8D787e15bC8a181DDC84" : {fee0:6000, fee1:0}, //WSAMA-BSAMA //反过来会失败
        "0x95D1B60D3b9A5F131b6672dBDAeF2D1f50E962d2" : {fee0:21000, fee1:0}, //GOTCHI-WSAMA
        "0x0435Ec55e2CC7977B6FECC4F37686B47c2aa1B1e" : {fee0:21000, fee1:0}, //GOTCHI-WSAMA

        "0x1E935217C056331309E185de1c14AE4689B5963B" : {fee0:2000, fee1:0}, //EXOSHIB-WSAMA
        "0xc140eD240324330F4b64420b5fF6231De780acc4" : {fee0:2000, fee1:6000}, //EXOSHIB-BSAMA
        "0xF503b92dB479637c167387BA589e828fB46D0d15" : {fee0:2000, fee1:0}, //EXOSHIB-DON
    }

    blackList = [
        "0x7FE332a833B1765759C2bb36D573546ba599f517"
    ];

    gasWatch = [
        "0x86aAAa7fAeD7C1999BD1BF6C601C0E03F439bD07",
        "0xc3DA629c518404860c8893a66cE3Bb2e16bea6eC",
        "0x533FaBD39F810481d25e0aAd836318867E0C6469",
        "0x8f93f3277A0790bC63b79D039af948ddB38465B2",
        "0x888597B97C426Eb121d222E08b5bf474DCe18E43", //套娃
    ]
}