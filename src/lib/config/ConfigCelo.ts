import Config from "./Config";

export default class ConfigCelo extends Config {
    mainnet = {
        data : "https://rpc.ankr.com/celo",
        
        //wss : ["wss://rpc.ankr.com/celo/ws/c959fef5bb6d732dc90f685ebea9bf31b73f21ba843c15ccf222df5704d112c3", "wss://forno.celo.org/ws"],
        wss : ["wss://forno.celo.org/ws"]
        //wss : ["https://rpc.ankr.com/celo", "wss://rpc.ankr.com/celo/ws/c959fef5bb6d732dc90f685ebea9bf31b73f21ba843c15ccf222df5704d112c3"]
        //wss : ["https://rpc.ankr.com/celo"]
    };
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************", "******************************************"];
    gasMin = 0.5;
    gasDelta = 0.01;
    gasMax = 1001;

    feedMax = 1;
    feedMin = 0.8;
    //feedAutoTimeHour = 12; //-1不自动加gas, 单位小时, celo需要转账充值

    bot = {
        active : true,
        //address: "******************************************",
        //address : "******************************************",
        //address : "******************************************",
        //address : "******************************************"
        //address : "******************************************", //new pair checker
        //address : "******************************************",
        address : "******************************************", //swap with fee, support v1, testpair, pump2
    };

    pump = {
        active:true,
        greed:true,
        allPath:false,
        gasMax :51.2,
        countMin:2,
        countMax:6,
    }

    trash = { active:true, delay:1000 }

    tokens = {
        "******************************************" : { name: "celo", ignore: true, keep:40, cex:true, price:0.8},
        "******************************************" : { name: "cusd", ignore: true, keep:10, cex:true},
        "******************************************" : { name: "mCusd", ignore: true, keep:10, cex:true},

        "0x918146359264C492BD6934071c6Bd31C854EDBc3" : { name: "mCusd", ignore: true, cex:true},
        "0x2A3684e9Dc20B857375EA04235F2F7edBe818FA7" : { name: "usdc", ignore: true, cex:true},
        "0xE273Ad7ee11dCfAA87383aD5977EE1504aC07568" : { name: "mCeur", ignore: true, cex:true},
        "******************************************" : { name: 'dai', ignore: true, cex:true},
        "******************************************" : { name: "cReal", ignore: true, cex:true},
        "******************************************" : { name: "mCreal", ignore: true, cex:true},

        "******************************************" : { name: "pact"},
        "******************************************" : { name: "sol", cex:true},

        /*
        "******************************************" : { name: "weth", cex:true},
        "******************************************" : { name: "btc", cex:true},
        "******************************************" : { name: "wbtc", cex:true},
        "******************************************" : { name: "avax", cex:true},
        "******************************************" : { name: "bnb", cex:true},

        "******************************************" : { name: "ube" },
        "******************************************" : { name: "mobi" },
        "******************************************" : { name: "sushi", cex:true},
        "******************************************" : { name: "immo"},
        "******************************************" : { name: "ari"},
        "******************************************" : { name: "moo"},
        "******************************************" : { name: "cmco2"},
        "******************************************" : { name: "knx"},
        "******************************************" : { name: "rCelo", cex:true, ignore:true},
        "******************************************" : { name: "knox"},
        "******************************************" : { name: "tfbx"},
        
        "0x0a60c25Ef6021fC3B479914E6bcA7C03c18A97f1" : { name: "stabilUSD", cex:true},
        "0xef4229c8c3250C675F21BCefa42f58EfbfF6002a" : { name: "usdc", cex:true},
        "0x639A647fbe20b6c8ac19E48E2de44ea792c62c5C" : { name: "bifi", cex:true},
        */
    }

    tokenBlackList = {
        "0x5c68fB7B31D4B0d0208D75057fbdcCc949531f59" : "sda",
        "0xD90BBdf5904cb7d275c41f897495109B9A5adA58" : "cdoge",
    }

    routers = {
        "0xE3D8bd6Aed4F159bc8000a9cD47CffDb95F96121" : {name: "ube"},
        //"0xa730b463395f5ca07ece5cefeccf7f45e1e2c9bf" : {name: "ube2"},
        "0x1421bDe4B10e8dd459b3BCb598810B1337D56842" : {name: "sushi"},
        //"0xFc9e2C63370D8deb3521922a7B2b60f4Cff7e75a" : {name: "mobius"},
        "0x1C2DdB78F1fBDE389D15acd275ce6b51EAFbBA14" : {name: "u1", fee:99800},
        //"0x7D28570135A2B1930F331c507F65039D4937f66c" : {name: "u2"},
        //"0x56d0Ae52f33f7C2e38E92F6D20b8ccfD7Dc318Ce" : {name: "u3"},
        "0x59414459C6c9883480983eF9888334d9376431F3" : {name: "u3"},
        "0xbaE8D41d1C0a997080420C4BAE89475fBC6A5d33" : {name: "u4"},
        //"0x39a5a4D1373da8cCA9492881d20e7607845A7d86" : {name: "minima"}, //SwapExactInputForOutput
    };
}