import { ethers , BigNumber, utils} from "ethers";
import bot from "../bot";
import tools from './tools';
import BotBase from "./botBase";
import ExTimer from "./comp/ExTimer";
import { BaseSwapExData, MixGas, SumToken, SwapAmoutsOut, SwapResult } from "./type/DataType";
import { TokenBalanceItem } from "./comp/tokenBalance";
import { macro } from "./macro";
import Lazy from "./lazy/LazyController";

export default class BotBull extends BotBase {

    enable = bot.config.bot.active; //用于调试
    lock = false;

    constructor(wallets:{[k:string]:string}, addr:string){
        super(wallets, addr);
        if(bot.mode == macro.BOT_MODE.UPDATE) return;
        if(this.enable && !this.lock && bot.config.bot.active){
            this.tokenBalance = this.loadCache(macro.FILE.BALANCE_BULL);
            ExTimer.setIntervalOld(() => { this.saveCache(macro.FILE.BALANCE_BULL, this.tokenBalance) }, 1000 * 20 * bot.config.blockTime); //每20个块保存一次
        }
    }

    //大宗买入
    async  onPut (
        trans:ethers.providers.TransactionResponse,
        dataDecode: SwapResult,
        multi:number,
        slippage:number,
        token : string,
        isEth : boolean,
        totalImpact : number,
        pairIn:number,
        pairOut:number){
            if(!this.enable || this.lock) return;
            this.addTokenListCache(token);
            const bToken = bot.token(token);
            const bStable = bot.token(bToken.mainStableToken);
            //不超过池子的滑点的购买价格
            //let bestDeal = bot.oracleV2.getBestDeal(router, token, slippage, multi).usdt;
            let bestDeal = bToken.bestDeal(slippage, multi).stable;
            const maxDeal = pairIn * 1.2;
            Lazy.ins().logTs1(`${macro.COLOR.Green}bestDeal(${bestDeal})  maxDeal(${maxDeal}) ${macro.COLOR.Off}`);
            //if(bestDeal > maxDeal) bestDeal = maxDeal;
            
            const stableBalance = this.tokenBalance.info(bToken.mainStableToken).usdt.num - bStable.keep;
            bestDeal = Math.min(bestDeal, maxDeal, stableBalance);

            if(slippage > macro.priceImpact.big) this.greedMode = true;
            //bestDeal = bestDeal * multi;

            if(bStable.limit > 0) bestDeal = tools.min(bStable.limit, bestDeal); //config设置的最大交易量

            //已经有持仓，跟着whale卖出，如果有更大的whale就补仓
            if(this.tokenBalance.has(token)) {
                const holding = this.tokenBalance.info(token);
                if(bStable.limit > holding.usdt.num
                    && holding.usdt.num > 0.001
                    && ((holding.usdt.num * 1.5) < bestDeal)
                    && stableBalance > 0.00001){
                    //继续购买
                    bestDeal = bestDeal - holding.usdt.num;
                } else {
                    Lazy.ins().logTs1(`@@ ... reach max holding`);
                    //如果whale成功交易会卖出更高价格，这里需要调高getBestAmountOut的输出
                    const sell = await this.getBestAmountOut(token, holding.token.bn, false, 1 + totalImpact * 0.9);
                    const data = new BaseSwapExData({
                        data:sell,
                        mixGas : new MixGas(trans),
                        whale: bot.swapDataToWhale(trans.from, dataDecode, false, isEth),
                        whaleTrans: trans
                    });
                    this.sellToken(data);
                    return;
                }
            }

            let buyAmount = bot.token(bToken.mainStableToken).toBigNumber(bestDeal);

            //没有钱, 不操作
            if(stableBalance < bStable.keep * 0.2) {
                Lazy.ins().log1("  @ .... not enough stable token");
                return;
            }

            //本地查询价格，加快速度
            const buy = await this.getBestAmountOut(token, buyAmount, true, bot.chain == macro.CHAIN.DOGE ? macro.slippage.bigLow : macro.slippage.normal, true);
            this.logTrade(macro.FILE.LOG_BULL_WHALE, trans.hash, token, buy.out.num, bToken.mainStableToken, buy.in.num, true);
            let data = new BaseSwapExData({
                data :buy,
                mixGas : new MixGas(trans).bid(bestDeal),
                frontRun:true,
                whale: bot.swapDataToWhale(trans.from, dataDecode, true, isEth, trans.to), bid: true }
            );

            //await this.delay(); //等待其他机器人入场
            this.buyToken(data, (receipt)=>{
                    trans.wait().then((r)=>{
                        //等鲸鱼完成交易后立刻开始卖出
                        //bot.receiptBlockNum(r, " @@ whale buy success!!");
                        const {bull} = bot.client;
                        const balance = bull.tokenBalance.info(token);
                        balance.dealer = trans.from;
                        balance.dealerSuccess = true;
                        bull.updateBalance([bToken.mainStableToken, token], true);
                    }).catch((e)=>{
                        //鲸鱼交易失败
                        //console.log(" @@ oops...whale trans fail");
                        const {bull} = bot.client;
                        const balance = bull.tokenBalance.info(token);
                        balance.dealer = trans.from;
                        balance.dealerSuccess = false;
                        bull.updateBalance([bToken.mainStableToken, token], true);
                    });
            });

            //TODO: 针对滑点大的交易立刻卖出
            //console.log(`(${bToken.symbol}) price: `, (await bot.oracleV2.getPrice(router, token)).num); //放在最后避免阻塞

    }
    //大宗卖出
    async onPull(
        trans:ethers.providers.TransactionResponse,
        dataDecode:SwapResult,
        multi:number,
        slippage:number,
        token : string,
        isEth : boolean,
        totalImpact : number,
        pairIn:number,
        pairOut:number){
            if(!this.enable || this.lock) return;

            const gasPrice = trans.gasPrice || bot.handle.minGas();
            const {amountIn, amountOut: amountOutMin, path, to, deadline} = dataDecode;
            this.addTokenListCache(token);
            const bToken = bot.token(token);

            //大单卖出时优先清空持仓
            if(this.tokenBalance.has(token)){
                const balance = this.tokenBalance.info(token);
                let forceSell = false;
                
                //计算是否盈利，如果不亏损则立刻卖出
                const sell = await this.getBestAmountOut(token, balance.token.bn, false, macro.slippage.normal, true);
                if(sell.out.num >= balance.usdt.num){
                    Lazy.ins().log1("=@ good pre selling...");
                    forceSell = true;
                }

                if(!forceSell){
                    //使用bear护航
                    const {bear} = bot.client;
                    if(bear.tokenBalance.info(token).token.num > 0 || bear.tokenBuyBack.info(token).token.num > 0){
                        Lazy.ins().log1("@ bear has token, skip pre selling");
                        return;
                    }

                    //如果购入跟卖出是同一个whale，并且whale上次没有购买成功，可能是波段用户，不操作
                    /*if(balance.dealer == to && balance.dealerSuccess == false) {
                        console.log(" @ pre whale buy fail. the same whale sell token. ");
                        return;
                    }*/
                }

                //抢先出售
                Lazy.ins().log1(`@ pre selling ...., preHoldingValue: ${balance.usdt.num}`);
                //await this.delay(); //等待其他机器人入场
                const data = new BaseSwapExData({
                    data:sell,
                    mixGas : (new MixGas(trans)).more().more(),
                    frontRun: true,
                    whale: bot.swapDataToWhale(trans.from, dataDecode, true, isEth),
                    whaleTrans: trans
                });
                this.sellToken(data, (receipt)=>{
                    //如果赚钱了就不回购
                    const parser = bot.receiptParser(receipt, token, bToken.mainStableToken);
                    if(parser.usdt.num >= balance.usdt.num) return;
                    //console.log(sellSuccess);
                    //记录出售信息
                    Lazy.ins().log1("@ adding buyback...")
                    this.tokenBuyBack.addReceipt(receipt, token, bToken.mainStableToken);
                    //记录上次购买的价格
                    this.tokenBuyBack.info(token).preUsdt = balance.usdt.num;

                    trans.wait().then((r)=>{
                        //等鲸鱼完成交易后看是否需要buyback
                        //bot.receiptBlockNum(r, " @@ whale sell success..");
                        this.autoBuyBack();
                    }).catch(e=>{ });
                });
            } else {
                //没有持仓时在whale卖出后抄底(白名单) //SUPER HUGE DEAL
                const stableBalance = this.tokenBalance.info(bToken.mainStableToken).usdt.num;
                if( !this.tokenBuyBack.has(token)
                    && multi > 3 && bToken.whiteList
                    && bot.client.bear.tokenBuyBack.has(token) //bear已经建仓好
                    && stableBalance > 0){

                    Lazy.ins().log1(" @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                    Lazy.ins().log1(" @@@  buy after great sell  @@@@@@@");
                    //let bestBuy = bot.oracleV2.getBestDeal(router, token).usdt * multi / 2;
                    let bestBuy = bToken.bestDeal().stable / 2;
                    bestBuy = Math.min(stableBalance, bestBuy * 1.2); //抄底倍数
                    const bestBuyBn = bot.token(bToken.mainStableToken).toBigNumber(bestBuy);
                    const buy = await this.getBestAmountOut(token, bestBuyBn, true, 1 + totalImpact * 0.7);
                    const data = new BaseSwapExData({
                        data:buy,
                        mixGas : new MixGas(trans),
                        whale:bot.swapDataToWhale(trans.from, dataDecode, false, isEth)
                    });
                    this.buyToken(data);
                }
            }
    }

    buyToken(dataEx : BaseSwapExData, onFinish? : (receipt:ethers.providers.TransactionReceipt)=>void){
        const tokenAddr = dataEx.data.paths[0][dataEx.data.paths[0].length-1];
        if(!this.trans.isEnable(tokenAddr)) {
            Lazy.ins().log1("@@ buyToken: transPool length > 0");
            return;
        }
        const toToken = bot.token(tokenAddr);
    
        Lazy.ins().logTs1(`@@ (${toToken.symbol}) buy $${dataEx.data.in.num} -> ${dataEx.data.out.num}, path:${dataEx.data.pairs.length}`);
        dataEx.onFinish = (receipt:ethers.providers.TransactionReceipt)=>{
            const r = bot.client.bull.tokenBalance.addReceipt(receipt, tokenAddr, toToken.mainStableToken, true);
            if(r) this.logTrade(macro.FILE.LOG_BULL, receipt.transactionHash, toToken.address, r.token.num, r.usdtAddr, r.usdt.num, true);
            bot.client.bull.tokenBuyBack.reduceReceipt(receipt, tokenAddr, toToken.mainStableToken, true);

            bot.receiptBlockNum(receipt, `${macro.COLOR.White}@@ (${toToken.symbol}) buy `);
            onFinish && onFinish(receipt);
            setTimeout(()=>{ this.greedMode = false}, 1000 * 20);
            this.updateBalance([toToken.mainStableToken]); //更新稳定币信息
        }

        dataEx.onFail = (e)=>{
            Lazy.ins().log1(`@@ (${toToken.symbol}) buy fail`);
        }
        this.swap(dataEx);
    }

    sellToken(dataEx : BaseSwapExData, onFinish? : (receipt:ethers.providers.TransactionReceipt)=>void, force=false)  {
        const tokenAddr = dataEx.data.paths[0][0];
        const token = bot.token(tokenAddr);

        if(!force && !this.trans.isEnable(tokenAddr)) {
            Lazy.ins().log1(`@ (${token.symbol}) sellToken err:  transPool > 0 `);
            return;
        }

        Lazy.ins().logTs1(`@ (${token.symbol}) sell ${dataEx.data.in.num} -> $${dataEx.data.out.num}, minOut:${dataEx.data.outSlippage.num.toFixed(4)} path:${dataEx.data.pairs.length}`);
        dataEx.onFinish = async (receipt:ethers.providers.TransactionReceipt)=>{
            //计算获利
            bot.receiptBlockNum(receipt, `${macro.COLOR.White}@ (${token.symbol}) sell `);
            const r = bot.receiptParser(receipt, token.address, token.mainStableToken); //price result
            const buyCost = this.tokenBalance.info(token.address).usdt.num;

            this.tokenBalance.remove(token.address);
            onFinish && onFinish(receipt);
            this.updateBalance([token.mainStableToken]); //更新稳定币信息
            const {transactionHash, blockNumber, transactionIndex} = receipt;

            let blockElapsed = 0;
            let whaleIndex = "-";
            if(dataEx.whaleTrans){
                try {
                    let whaleRes = await dataEx.whaleTrans.wait();
                    blockElapsed =  blockNumber- whaleRes.blockNumber;
                    whaleIndex = whaleRes.transactionIndex.toString();
                } catch(e) {
                    whaleIndex = "-1";
                }
            }

            let profitStr = "";
            if(buyCost > 0){
                const profit = r.usdt.num - buyCost;
                profitStr = (blockElapsed == 0 ? "" : `(${+blockElapsed})`)+ `(${transactionIndex}/${whaleIndex})` + ` reward: ${profit.toFixed(4)}`;
                Lazy.ins().log1(`bull ${macro.COLOR.BYellow}${profitStr}${macro.COLOR.Off}`);
            }
            this.logTrade(macro.FILE.LOG_BULL, transactionHash, token.address, r.token.num, r.usdtAddr, r.usdt.num, false, profitStr);
        };

        dataEx.onFail = (e)=>{
            //console.log(e);
            Lazy.ins().log1(`@@ (${token.symbol}) sell fail`);
        }
        this.swap(dataEx);
    }

    //updateBalance用到的token
    //初始化时读取所有token，持有的token缓存起来, 有大宗交易时增加到tokenListCache
    //tokenListCache会越来越大，可能需要定时清空
    tokenListCache :string[] = [];

    /**
     * 
     * @param selectedTokens 只更新指定的token
     * @returns 
     */
    async updateBalance(selectedTokens:string[] = [], fromServer=true){
        if(bot.mode == macro.BOT_MODE.LOCAL || !bot.config.bot.active) return false;
        //if(!this.enable) return false;
        //console.log(" ... update balance");
        const bonus = this.greedMode ? 1.0001 : 1.003;
        let sum = 0; //持仓总价值
        let stableSum = new SumToken();

        const { stableTokens } = bot.config;
        let allTokens : string[] = [];
        if(this.tokenListCache.length > 0){
            allTokens = tools.arrConcat(this.tokenListCache, bot.config.stableTokens);
        } else {
            allTokens = bot.config.bot.crazy ? tools.arrConcat(Array.from(bot.oracleV2.data.pm.tokens.data.values()).filter(x => x.active == true).map(x=>x.address), bot.config.stableTokens) : Object.keys(bot.config.tokens);
        }
    
        for(const tokenAddr of allTokens){
            if(bot.config.tokenBlackList[tokenAddr]) continue;
            if(bot.config.stableTokens.includes(tokenAddr)) continue;
            if(selectedTokens.length > 0 && !selectedTokens.includes(tokenAddr)) continue;
            const token = bot.token(tokenAddr);
            const tokenStable = bot.token(token.mainStableToken);
            let balance : {bn:BigNumber, num:number};
            try {
                balance = await this.getBalance(tokenAddr);
            } catch(e){
                Lazy.ins().log1(`${macro.COLOR.Red}error1 loading balance of ${token.symbol}: ${tokenAddr} ${macro.COLOR.Off}`);
                continue;
            }
            //稳定币不计算卖出价格
            if(stableTokens.includes(tokenAddr)){
                this.addTokenListCache(tokenAddr);
                this.tokenBalance.setData(tokenAddr, new TokenBalanceItem({ usdt:balance, token:balance }))
                sum += balance.num;
                stableSum.add(tokenAddr, balance.num);
                continue;
            }
            const holding = this.tokenBalance.info(token.address);
            if(balance.num > 0){
                let sell : SwapAmoutsOut;
                try {
                    //const slippage = force ? macro.slippage.bigLow : macro.slippage.normal;
                    const slippage = macro.slippage.bigLow;
                    sell = await this.getBestAmountOut(token.address, balance.bn, false, slippage, fromServer);
                } catch(e){
                    //console.log(e);
                    Lazy.ins().log1(`${macro.COLOR.Red}fail token: ${token.symbol} ${macro.COLOR.Off}`)
                    continue;
                }
                
                this.addTokenListCache(tokenAddr);
                //补上未记录的持仓
                if(!this.tokenBalance.has(token.address)){
                    Lazy.ins().log1(`~~~~~ not log token ${token.symbol}`);
                    this.tokenBalance.setData(token.address, new TokenBalanceItem({token:balance, usdt:{bn:BigNumber.from('0'), num:0}}))
                }
                holding.token.bn = balance.bn;
                holding.token.num = balance.num;
                const buy = holding.usdt;
                const avgSell = sell.out.num/balance.num;
                const avgBuy = buy.num/balance.num;
                
                sum += sell.out.num; //累加总价值
                stableSum.add(token.mainStableToken, sell.out.num);
                
                Lazy.ins().log1(`${macro.COLOR.On_Blue}[balance]${macro.COLOR.Blue} ${token.symbol}/${tokenStable.symbol}: ${balance.num.toFixed(2)}, `
                    +`price: ${sell.out.num.toFixed(2)}/${buy.num.toFixed(2)} `
                    + `avg:${avgSell.toFixed(4)}/${avgBuy.toFixed(4)} `
                    + ` ${((holding.token.num/token.sum.token) * 100).toFixed(2)}%${macro.COLOR.Off}`);

                if(sell.out.num > buy.num * bonus && holding.usdt.num > 0){
                //if(sell.out.num > buy.num * bonus){
                    this.sellToken(new BaseSwapExData({data:sell}));
                }
                
            } else {
                //只在定时检查里去除
                if(holding.token.num > 0 && selectedTokens.length == 0){
                    Lazy.ins().log1(`~~~~ not holding token ${token.symbol}`)
                    this.tokenBalance.remove(token.address);
                }
            }
        }
        if(selectedTokens.length > 0) return true;

        const st =  bot.client.stableBalance;

        st.show(`[bull](${this.tokenListCache.length})`, stableSum);
        
        if(st.totalVal.now < st.totalVal.init * 0.97){ //亏了3%停止操作
            Lazy.ins().log1("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
            Lazy.ins().log1(" WARNING!!! sum drop off 97% ");
            Lazy.ins().log1(JSON.stringify(st));
            Lazy.ins().log1("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
            this.lock = true;
        } else {
            this.lock = false;
        }
        //setTimeout(()=>{ this.updateBalance() }, 20000);
        return true;
    }

    async autoBuyBack(){
        if(!this.enable || this.lock) return false;
        const bonus = 1.002;
        const data = this.tokenBuyBack.data();
        for(const tokenAddr of Object.keys(data)){
            //上次卖出时候获得的token
            if(!data[tokenAddr]) continue; //有报错，不清楚什么情况
            const {usdt, token} = data[tokenAddr];
            const bToken = bot.token(tokenAddr);
            const bStableToken = bot.token(bToken.mainStableToken);
            const now = await this.getBestAmountOut(bToken.address, usdt.bn, true, macro.slippage.low, true);
            const infoBuyBack = this.tokenBuyBack.info(tokenAddr);

            Lazy.ins().log1(`    [buyBack] ${bToken.symbol}/${bStableToken.symbol}: `
                +`${usdt.num.toFixed(2)} -> `
                +`${now.out.num.toFixed(1)}/${token.num.toFixed(1)} `
                +`price:${(usdt.num/now.out.num).toFixed(3)}/${(usdt.num/token.num).toFixed(3)}`);

            if(now.out.num > token.num * bonus){
                this.buyToken(new BaseSwapExData({
                    data: now
                }), (receipt)=>{
                    const infoNow = this.tokenBalance.info(tokenAddr);
                    infoNow.usdt.num = infoBuyBack.preUsdt; //把上次购买的价格记录到这次购买记录上
                    infoNow.usdt.bn = utils.parseUnits(infoBuyBack.preUsdt.toString());
                    this.tokenBuyBack.remove(tokenAddr);
                });
            }
        }
        return true;
    }

    addTokenListCache(token:string){
        /*
        if(bot.chain == macro.CHAIN.ONE || bot.chain == macro.CHAIN.KLAY){
            const symbol = bot.token(token).symbol;
            if(symbol.includes('DFK') && symbol !== "DFKGOLD") return;
        }
        */
        if(!this.tokenListCache.includes(token)) this.tokenListCache.push(token);
    }
    
}