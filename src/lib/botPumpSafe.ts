import { BigNumber, ethers, utils } from "ethers";
import bot from "../bot";
import BotPump from "./botPump";
import DataType, { FindPathResult, MevPath, MixGas, ServerPumpData } from "./type/DataType";
import Lazy from "./lazy/LazyController";
import { macro } from "./macro";

class PumpSafeItem {
    tokenIn = "";
    tokenOut = "";
    amountIn = 0;
    amountOutMin = 0;
    pairs : string[] = [];
    needEstimateGas = false;
}

export default class BotPumpSafe extends BotPump {
    //path: success
    successPath : {[path:string]:number} = {};
    failPath : {[path:string]:number} = {};

    jobs : PumpSafeItem[] = [];

    constructor(wallets:{[k:string]:string}, addr:string){
        super(wallets, addr);
    }

    tryPump(tokenIn:string, tokenOut:string, amountIn:number, amountOutMin:number, pairs:string[]){
        const key = pairs.join(',');
        let failCache = this.failPath[key];
        let successCache = this.successPath[key];
        if(failCache && failCache > 3){
            //EstimateGas失败超过两次，标记为失败的pair
            return;
        }
        this.jobs.push({
            tokenIn : tokenIn,
            tokenOut : tokenOut,
            amountIn : amountIn,
            amountOutMin : amountOutMin,
            pairs : pairs,
            needEstimateGas : (successCache && successCache > 2) ? false : true
        });
    }

    async beginPumps(mixGas:MixGas){    
        const _jobs = [...this.jobs];
        this.jobs = [];

        let opIndex = bot.getOperatorIndex(macro.zeroAddress, bot.handle.blockNum +4); //与trash隔开；

        for(let p of _jobs){
            const key = p.pairs.join(',');
            const encodeData = DataType._old_pumpSafeReqEncode(p.tokenIn, p.tokenOut, p.amountIn, p.amountOutMin, p.pairs);
            if(p.needEstimateGas){
                //检查是否成功
                try {
                    let success = await bot.provider().estimateGas({
                        from : this.operators[0].ins().address,
                        to : bot.config.bot.address,
                        data : encodeData,
                    });
                    this.successPath[key] ??= 0;
                    this.successPath[key] += 1;
                    Lazy.ins().log1(`${macro.COLOR.Green}[pumpSafe] estimateGas success${macro.COLOR.Off}`);
                } catch(e) {
                    this.failPath[key] ??= 0;
                    this.failPath[key] += 1;
                    Lazy.ins().log1(`${macro.COLOR.BBlack}[pumpSafe] estimateGas fail${macro.COLOR.Off}`);
                    return;
                }
            }
            const signData = this.signTxSync(opIndex, encodeData, mixGas, 2000000);
            bot.handle.sendTransactionAll(signData, undefined, (receipt)=>{

                //this.logSafe(receipt, encodeData, opIndex, p.tokenOut, p.amountOutMin - p.amountIn);
            },
            (fail)=>{
                Lazy.ins().log1(`[pumpSafe] pending fail`);
            },
            (fail2)=>{
                Lazy.ins().log1(`[pumpSafe] tx fail`);
            });
            opIndex = bot.getNextOperator(opIndex);
        }
    }


    async sharingan(path:string[], amountOuts:number[], trans : ethers.providers.TransactionResponse){
        if(!bot.config.bot.sharingan) return;
        const tokenIn = path[0];
        const amountIn = amountOuts[0];
        const from = trans.from || macro.zeroAddress;
        const to = trans.to || macro.zeroAddress;
        //检查是否成功
        try {
            //console.log(`${macro.COLOR.Yellow}[sharingan] value: ${utils.formatEther(trans.value)}${macro.COLOR.Off}`)
            let success = await bot.provider().estimateGas({
                from : from,
                to : to,
                data : trans.data,
                value: trans.value,
            });
            const sharinganReward = amountOuts[amountOuts.length-1] == 0 ? 0 : amountOuts[amountOuts.length-1] - amountIn;;
            Lazy.ins().log1(`${macro.COLOR.Yellow}[sharingan] try on !!!!!!${macro.COLOR.Off}`);
            const pairs = [];
            const mixGas = new MixGas(trans).litterMore();
            //组合成mevPath

            for(let i = 0 ; i < path.length-1; i++){
                const addr = bot.oracleV2.getPairAddr(to, path[i], path[i+1]);
                pairs.push(addr);
                //let r = this.findPath(addr, path[i], mixGas, cache);
            }
            let result = await this.pairsToMevPath(path[0], pairs);
            result.amount = bot.token(path[0]).toBigNumber(amountOuts[0]);
            result.stable = tokenIn;
            //console.log(result);

            let encodeData = DataType.findPathResultsToServerPumpData([result]);

            //check mev
            try {
                let res = await bot.provider().call({
                    from: await bot.getbotOwner(),
                    to : bot.config.bot.address,
                    data : encodeData.encodePackedData,
                });
                let decode = bot.contractIns.iBot.decodeFunctionResult("pumpSmart", res);
                let status  = decode[0] as number[];
                let rewards = decode[1] as BigNumber[];
                let calcReward = bot.token(tokenIn).toNum(rewards[0]);
                if(status[0] == macro.PUMP_RESULTS.Success || status[0] == macro.PUMP_RESULTS.SuccessOverlow){
                    if(calcReward > 0 && calcReward > sharinganReward * 1.15){
                        result.amount = macro.bn.zero;
                        result.mevType = macro.MEV_TYPE.DXDY;
                        encodeData = DataType.findPathResultsToServerPumpData([result]);
                        console.log(`dydx reward: ${calcReward}, sharingan reward: ${sharinganReward}`);
                    }
                }
            } catch(e){
            }

            let opIndex = bot.getOperatorIndex(macro.zeroAddress, bot.handle.blockNum + 3); //与trash隔开；
            const signData = this.signTxSync(opIndex, encodeData.encodePackedData, mixGas, 2000000);
            
            /*
            bot.provider().estimateGas({
                from : this.operators[opIndex].ins().address,
                to : bot.config.bot.address,
                data : encodeData.encodePackedData,
            }).then(success=>{
                console.log(`${macro.COLOR.BRed} pump safe estimateGas success!!! ${macro.COLOR.Off}`);
                console.log(success);
            }).catch(err=>{
                console.log(`${macro.COLOR.BRed} pump safe estimateGas fail!!! ${macro.COLOR.Off}`);
                console.log(err);
            });
            */

            bot.handle.sendTransaction(signData, undefined, (receipt)=>{
                this.logSafe(receipt, encodeData.data, opIndex, tokenIn, sharinganReward, true);
            },
            (fail)=>{
                console.log(fail);
                Lazy.ins().log1(`${macro.COLOR.Red}[sharingan] pending fail${macro.COLOR.Off}`);
            },
            (fail2)=>{
                Lazy.ins().log1(`${macro.COLOR.Red}[sharingan] tx fail${macro.COLOR.Off}`);
            });
        } catch(e) {
            console.log(e);
            Lazy.ins().log1(`${macro.COLOR.Red}[pumpSafe] sharingan fail.${macro.COLOR.Off}`);
        }
    }

    async pairsToMevPath(tokenIn:string, pairs:string[]){
        const mev = new MevPath();
        mev.convertEth = 0;
        mev.pairs = pairs;
        mev.s0 = tokenIn;
        mev.s1 = tokenIn;
        mev.status0 = macro.MEV_STATUS.ACTIVE;
        mev.status1 = macro.MEV_STATUS.ACTIVE;

        mev.tokenIn0or1 = [tokenIn == bot.oracleV2.data.pm.get(pairs[0]).token0 ? 0 : 1, tokenIn == bot.oracleV2.data.pm.get(pairs[pairs.length-1]).token0 ? 0 : 1];
        mev.type = macro.MEV_TYPE.BASE;
        mev.weight = 1;

        let res = await bot.contractIns.batchCheckMev([mev]);
        mev.fees0 = res[0].fee0;
        mev.fees1 = res[0].fee1;
        mev.gas = res[0].gas + 200000;

        const find = new FindPathResult;
        find.convertEth = 0;
        find.tokenIn0or1 = mev.tokenIn0or1[0];
        find.mevType = mev.type;
        //find.gas = mev.gas;
        find.pairs = mev.pairs;
        find.fees = mev.fees0;
        return find;
    }

    async logSafe(receipt:ethers.providers.TransactionReceipt, serverData:ServerPumpData[], seed:number, stable:string, expectRewards:number, isSharingan = false){
        if(!this.hashPool.enable(receipt.transactionHash)) return;

        const { effectiveGasPrice, cumulativeGasUsed, gasUsed } = receipt;
        const bEth = bot.token(bot.config.eth);
        const costVal = bEth.toNum((effectiveGasPrice || cumulativeGasUsed).mul(gasUsed)) * bEth.price;

        const sum = {
            hash : receipt.transactionHash,
            block : receipt.blockNumber,
            index : receipt.transactionIndex,
            total : 0,
            success : 0,
            reward : 0
        };
        let results : number[] = [];
        let rewards : BigNumber[];

        const logs = (receipt as any)["logs"] as Array<any>;
        if(logs && logs.length > 0){
            let logData = logs[logs.length-1].data;
            [ results, rewards ]= bot.abiCoder.decode(["uint8[]","uint112[]"], logData);
            sum.total = results.length;
            for(let i = 0; i < serverData.length; i++){
                if(results[i] == macro.PUMP_RESULTS.Success || results[i] == macro.PUMP_RESULTS.SuccessOverlow){
                    sum.success++;
                    const pIn = bot.oracleV2.data.pm.get(serverData[i].pairs[0].addr);
                    const tokenIn = bot.token(serverData[i].tokenIn0or1.isZero() ? pIn.token0 : pIn.token1);
                    sum.reward += tokenIn.toNum(rewards[i]) * tokenIn.price;
                }
            }
        }

    
        let rewardStr = "";
        let str : string[] = [];
        if(sum.reward > 0){
            rewardStr = `$ ${sum.reward.toFixed(3)} - ${costVal.toFixed(3)} = ${(sum.reward-costVal).toFixed(3)} (${expectRewards.toFixed(3)}) ${results.join(",")} `;
            str.push(`$ ${sum.reward.toFixed(3)} - ${costVal.toFixed(3)} = ${(sum.reward-costVal).toFixed(3)} (${expectRewards.toFixed(3)})`);
            str.push(sum.index.toString());
            if(isSharingan) str.push("*");
        } else {
            rewardStr = `$  - ${costVal.toFixed(4)} (${expectRewards.toFixed(3)}) `;
            str.push(`$       - ${costVal.toFixed(4)} (${expectRewards.toFixed(3)})`);
            str.push(sum.index.toString());
        }

        bot.localLog.append(macro.FILE.LOG_PUMP_SAFE, sum.hash, str);
        Lazy.ins().logTs1(
            `${macro.COLOR.Yellow}[pumpSafe] op:(${seed}) tx:${sum.hash} ${sum.block} (${sum.index}) `
            +`${sum.reward > 0 ? macro.COLOR.BGreen : ""}${rewardStr} ${results.join(",")} ${macro.COLOR.Off}`);

        if(isSharingan) Lazy.ins().logTs1(`${macro.COLOR.BYellow}sharingan success!!!!${macro.COLOR.Off}`);
    }
}