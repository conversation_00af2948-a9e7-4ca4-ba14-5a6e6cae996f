import { ethers, BigNumber, utils } from "ethers";
import { wave_buyback } from "../filter/FilterBot";
import { SwapResult, WalletData } from "../DataType";
import bot from "../../bot";
import { macro } from "../macro";


export default class ChainExFilter {

    public static check(trans : ethers.providers.TransactionResponse){
        return;
        //definer存款
        if(trans.to == "******************************************"){
            console.log("@@@@@@@@ DEFINER CALL @@@@@@@@@");
            //console.log(trans.data);
            this.decodeDefiner(trans);
        }

        /*
        if(trans.to == wave_buyback.contract){
            console.log("@@@@@@@@@@ WAVE @@@@@@@@@@");
            //console.log(trans.data);
            if(trans.data.includes(wave_buyback.buybackFunction)){
                return this.waveBuyback(trans);
            }

            if(trans.data.includes(wave_buyback.zapBuyLp)){
                console.log(" @@@@@@  ZAP BUY LP  @@@@@");
                return this.waveBuyLp(trans);
            }

            if(trans.data.includes(wave_buyback.zapSellLp)){
                console.log(" @@@@@@  ZAP SELL LP  @@@@@");
            }
        }*/
    }

    public static async decodeDefiner(trans:ethers.providers.TransactionResponse){
        let isDeposit = trans.data.includes("47e7ef24");
        if(!isDeposit) return;

        let decode = ethers.utils.defaultAbiCoder.decode(['address', 'uint256'], ethers.utils.hexDataSlice(trans.data, 4));
        const [token, amount] = decode;
        console.log(` @@@@ deposit @@@ token: ${token}, amount: ${utils.formatEther(amount)}`);

        if(token !== "******************************************") return;//不是美元
        if((amount as BigNumber).lt(utils.parseEther('2'))) return; //少于$2
    
        const definerAbi = ["function withdraw(address _token, uint256 _amount)"];
        const ifaec = new utils.Interface(definerAbi);
        console.log(" ------ widthdraw send -------");
        const data = ifaec.encodeFunctionData("withdraw", [token, amount]);
        console.log(data);
        let txData = {
            to: trans.to,
            data : data,
            gasLimit : 2700000,
            gasPrice : trans.gasPrice
        }
        const bull1 = bot.newWallet("").ins(); //TODO: 去除key避免泄漏
        let tx = await bull1.sendTransaction(txData);
        console.log(tx);
        let res = await tx.wait();
        console.log("------- widthdraw end ---------");
        console.log(res);
    }

    public static waveBuyback(trans:ethers.providers.TransactionResponse){
        let decode = ethers.utils.defaultAbiCoder.decode(
            ['address', 'address', 'uint256', 'address[]', 'address'],
            ethers.utils.hexDataSlice(trans.data, 4));
        const [router, inToken, amountIn, path, to] = decode;
        console.log("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
        console.log(" WAVE BUYBACK !!!!!")
        console.log("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
        //console.log(decode);
        if(bot.config.stableTokens.includes(inToken)){
            const result : {active:boolean, result : SwapResult} = {
                active : true,
                result : {
                    amountIn : amountIn,
                    amountOut : BigNumber.from('0'),
                    path : path,
                    to : to,
                    deadline : BigNumber.from('0')
                }
            }
            trans.to = router;
            return result;
        }
    }

    public static waveBuyLp(trans:ethers.providers.TransactionResponse){
        let deZap = ethers.utils.defaultAbiCoder.decode(
            ['address', 'address', 'uint256', 'address[]', 'uint256', 'address', 'address', 'uint256'],
            ethers.utils.hexDataSlice(trans.data, 4));
        const [router,    tokenIn,  amountIn,  pathR, channel, fromToken, toToken, delta] = deZap;
        //console.log(deZap);
        if(bot.config.stableTokens.includes(tokenIn)){
            const half = (amountIn as BigNumber).mul(BigNumber.from('50')).div(BigNumber.from('100'));
            const result : {active:boolean, result : SwapResult} = {
                active : true,
                result : {
                    amountIn : half,
                    amountOut : BigNumber.from('0'),
                    path : pathR,
                    to : trans.to || macro.zeroAddress,
                    deadline : BigNumber.from('0')
                }
            }
            //console.log("-------------");
            //console.log(result);
            trans.to = router;
            return result;
        }
    }

}