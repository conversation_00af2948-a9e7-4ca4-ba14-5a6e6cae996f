import { execSync } from "child_process";
import { macro } from "../macro";

export class Host {
    name = "";
    ip = "";
    port = 22;
    pem = "";
    user = "";

    //proxy = " -o ProxyCommand='ssh -W %h:%p bwg'";
    proxy = "";

    _rsyncCmdPreStr = "";

    _configFile = ["router_core_checked.json", "pump_bad_pair.txt", "unknow_router.txt"];
    _localPem = ""; 

    constructor(name="", ip="", port=22, pem="", user="", proxy=false){
        this.name = name;
        this.ip = ip;
        this.port = port;
        this.pem = pem;
        this.user = user;
        //if(!proxy) this.proxy = "";
        this._localPem = pem !== "" ? ` -i '~/Script/remote/aws/${this.pem}'` : "";
    }

    rsync(prameters : string[], color = macro.COLOR.White){
        const cmd = `rsync -i -avr -e "ssh -p ${this.port}${this._localPem}${this.proxy}" ${prameters.join(" ")}`;
        //const cmd = `rsync --dry-run -i -avr -e "ssh -p ${this.port}${this._localPem}" ${prameters.join(" ")}`; //for test
        console.log(`${color}[${this.name}]${macro.COLOR.Off} ${cmd}`);
        execSync(`${cmd}`, {stdio: 'inherit'});
        return cmd
    }

    ssh(cmds : string[], op = ";"){
        const loginCmd = `ssh -p ${this.port}${this._localPem}${this.proxy} ${this.user}@${this.ip} "${cmds.join(` ${op} `)}"`;
        console.log(`${macro.COLOR.BPurple}[${this.name}]${macro.COLOR.Off} ${loginCmd}`);
        execSync(loginCmd, {stdio: 'inherit'});
    }

    //下载数据
    d(chain:macro.CHAIN){
        this.rsync([
            `--exclude="router_${chain}_checked.json"`,
            //`--exclude="router_${chain}_checked_mev.json"`,
            `${this.user}@${this.ip}:~/temp/bot_${chain}/src/data/${chain}/ ./src/data/${chain}`
        ], macro.COLOR.Green);

        console.log("<<<<<< download data done");
    }

    d_router(chain:macro.CHAIN){
        this.rsync([
            `-e "ssh -p ${this.port}${this._localPem}"`,
            `${this.user}@${this.ip}:~/temp/bot_${chain}/src/data/${chain}/router_${chain}_checked.json ./src/data/${chain}/router_${chain}_checked.json`
        ], macro.COLOR.Green);
        console.log("<<<<<< download router done");
    }

    u_bot(chain:macro.CHAIN){
        const prameters = [
            "--exclude='node_modules'",
            "--exclude='src/master'",
            "--exclude='src/data'",
            "--exclude='src/backup'",
            "--exclude='src/json'",
            "--exclude='.vscode'",
            "--exclude='src/sol'",
            "--exclude='src/test'",
            "--exclude='.*'",
            `./ ${this.user}@${this.ip}:~/temp/bot_${chain}`
        ];
        this.rsync(prameters, macro.COLOR.BRed);
        console.log("upload bot done >>>>>>");
    }

    u_data(chain:macro.CHAIN){
        const prameters = [
            `--exclude="router_${chain}_checked.json"`,
            "--include='*.json'",
            "--include='*.txt'",
            "--exclude='*'",
            `./src/data/${chain}/ ${this.user}@${this.ip}:~/temp/bot_${chain}/src/data/${chain}/`
        ];
        this.rsync(prameters, macro.COLOR.BRed);
        console.log("upload data done >>>>>>");
    }

    u_router(chain:macro.CHAIN){
//"rsync -i -avr -e 'ssh -p #{@server[:port]}#{@ssh_pramets}' --include='router_#{@chain}_checked.json' --exclude='*' ./src/data/#{@chain}/ #{@user}@#{@server[:ip]}:~/temp/bot_#{@chain}/src/data/#{@chain}/"
        const prameters = [
            `--include="router_${chain}_checked.json"`,
            `--include="router_${chain}_checked_mev.json"`,
            //"--include='pump_bad_pair.txt'",
            "--exclude='*'",
            `./src/data/${chain}/ ${this.user}@${this.ip}:~/temp/bot_${chain}/src/data/${chain}/`
        ];
        this.rsync(prameters, macro.COLOR.BRed);
        console.log("upload router done >>>>>>");
    }

    connect(){
        
    }

    pm2_restart(chain:macro.CHAIN, isSlave:boolean){
        //const pm2Export = `export PATH="/home/<USER>/.nvm/versions/node/v16.14.2/bin/pm2"`;
        const nvm = "source ~/.nvm/nvm.sh";
        if(chain == macro.CHAIN.NOVA || chain == macro.CHAIN.METIS){
            this.ssh([nvm, `pm2 restart ${chain}`]);
        } else if(isSlave){
            this.ssh([`${nvm} && pm2 restart ${chain}1 || pm2 restart ${chain}3 || pm2 restart ${chain}`]);
        } else {
            this.ssh([`${nvm} && pm2 restart ${chain} || pm2 restart ${chain}1 || pm2 restart ${chain}2 || pm2 restart ${chain}3`]);
        }
    }

    pm2_stopall(chain:macro.CHAIN, isSlave:boolean){
        const nvm = "source ~/.nvm/nvm.sh";
        if(isSlave){
            this.ssh([nvm, `pm2 delete ${chain}1`]);
        } else {
            this.ssh([nvm, `pm2 delete ${chain}`, `pm2 delete ${chain}1`, `$pm2 delete ${chain}2`]);
        }
    }

    pm2_ls(){
        const nvm = "source ~/.nvm/nvm.sh";
        this.ssh([nvm, `pm2 ls`]);

    }



}