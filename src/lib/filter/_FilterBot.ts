export const blackList : {[k:string]: { desc : string}} = {
    //"0x576DaE38769d5fa2A8eaA709c35D0f24BC8bc523" : { desc: "1000每笔分散买入okb"},
    //"0x0f4C6578991B88Fe43125C36C54D729aeDd58473" : { desc: "每笔卖出35okb" },
    //"0x30E0B82C2CFed975D068f08e6E004f188E05b407" : { desc: "交易ltck bot" },
    //"0x86f062EB7c1b364d4573f25eA3FBAff70f9a610C" : { desc: "交易okb" },
    //"0x02189FD168b423C093D62770eaDcC744E39c7324" : { desc: "blade搬砖1" },
    //"0x088302305324d242D16F26Bb0bFB0fda7632F46b" : { desc: "blade搬砖2" },
    //"0x06B1767503aEC0d3E827CD27BB016F00f1Cec862" : { desc: "celt搬砖"},
    //"0xd676432A77cfe7bbF5a048E375557cC18e295aE7" : {desc:"中心化搬砖，总是在che交易sushi"},
    "0xd9A2e75b371f01d629B619DbA9D4C890a7963A4a" : { desc: "che搬砖" },
    //"0x12b7F0F4f5eEcEDcB778bc4fC9eA81dF4af5a914" : { desc: "多币微利搬砖机器人" },
    "0x0EC594099e41377775A7970f64C3d3465eD5d586" : { desc: "blade手动套利" },
    "0xfb5033e7131484b9dceF2497De2f31b68732a640" : { desc: "jf虚假交易" },
    "0x9110665e7D430413e1e8b4284AaDD2f9A3c0AAeF" : { desc: "hdt fake"},
    "0x85CCE1fB13b817C687D329854924A6D07fBb2Cf0" : { desc: "hdt虚假交易"},
    "0xBd9323ACeC2ca28E1Bca144ceA827d3B9C8F400f" : { desc: "hdt虚假交易"},
    "0xBb9Ec9F75EC8107CaD9728FABf37577eC6Eac485" : { desc : "虚假ufo交易"},
    "0x043517A2B6Da181a8c6e99736f5183bA992485f8" : {desc:"hep hdt波段, 持仓大户"},
    "0x50495C6E53d15e644862b3402A8b086e9c49afD9" : {desc:"合约攻击"},
    "0x9A8464B2F2d95B728A85FdC99b38465fFB66c394" : { desc:"合约攻击"},
    
    "0xB9b9161c6440353d30E06f6A3F93CD43Bd1e3907" : { desc:"mdex交易所搬砖机器人，虚假orare交易"},
    "0xad54f1193ED4031518922C2F100E9f812a151d54" : { desc:"ftm虚假交易spirit"},

    //"0xD95a366A2C887033ba71743c6342e2Df470E9dB9" : {desc:"polygon波段ads交易"},
    //"******************************************" : {desc:"polygon垃圾高gas"},
    "******************************************" : {desc:"polygon假交易"},

    //bot逻辑有bug
    "******************************************" : { desc:"heco大宗swapTokensForExactTokens"},
    "******************************************" : { desc:"face"},
    //"******************************************" : { desc:"face1"},

    "******************************************" : {desc:"ethkd, btckd波段"},
    //xdai
    "******************************************" : {desc:"ocean波段"},
    //moonriver
    "******************************************" : {desc:"差价搬砖，gas太高"},

    "******************************************" : {desc:"kcc:"},
    "******************************************" : { desc:"kcc"},
    "******************************************" : { desc:""},

    "******************************************" : {desc:"attacker"},
    "******************************************" : {desc:"attacker1"},
    "******************************************" : {desc:"attacker2"},
    "******************************************" : {desc:"attacker3"},
    //harmory
    "******************************************" : {desc:"jewel-usdc搬砖"},
    "******************************************" : {desc:"dfk假交易"},
    "******************************************" : {desc : "one搬砖" },
    "******************************************" : { desc : "one假交易" },
    "0x469FC745d0B0bBb6d00534790c1B108890e38496" : { desc : "one假交易" },
    "0x97cD44825204684aE3E302C2A95299Ef8688f35B" : { desc: "sushi假交易"},
    "0x4c182c83C3EA3219bf30369e63c81574B9aDDcDc" : {desc: "sushi假交易"},
    "0x5fE8919DB4c363c577063Ca23D4Be624310f0db5" : { desc: ""},
    "0x5F1dA84bD5906baC815263BD3c9c3a0EC975dd22" : { desc:""},
    "0xdCAf7305d0b165b3C401563D068b4461E774eAC0" : {desc:""},
    "0x06DE8e93E4E7DEc8152a55c0219D68c1A9F4E8e8" : { desc:""},
    "0x418ecB9E8797a106C91cc0E9Bbe3eE12B71441f4" : {desc:""},
    "0xD8f16305eB01AD6D1f35a7c8e35872E644fF5045" : {desc:""},
    "0x851a0EF093f36B18De43522877C673849Fe92Fb4" : {desc:""},
    "0x761C715F1C1aA5c551E55D867b183a5D6a982574" : {desc:""},
    "0x0C8c6fac0bFfe999E8cCC1abb76C1B66369C3F38" : {desc:""},
    "0xC783fD41FA9dC4FCf311e0bE4fd55fbf69a571d7" : {desc:""},
    "0x10B431cfF1eC424BE97A11e03bB414C12aE47826" : {desc:""},
    "0xA6782BcAC93c3e8EfcFaF96204209834395623Fd" : {desc:""},
    "0xD7CC08dc017faE6800955741E6886A6aA9988d79" : {desc:""},
    "0x07D400B2cA02987192981e9CBAFaeDa4dAe4f1bc" : {desc:""},
    "0xF8Ea822b547f3A9d5957D0dC308D6D2eB03c05b5" : {desc:""},
    "0xa475F62f0797Fc8Eccb5e609767EEb14A0024F44" : {desc:""},
    "0x2493fb6A3691A0c20A4b1cE45DCb1B50E5403F98" : {desc:""},
    "0x622849bEF7e9F66D95490480b7227A0b3dAba646" : {desc:""},
    "0x90345e9eCDA1B890178c18dD59E951528e644603" : {desc:""},
    "0x71b9D599eEf8Cc9E428BAA50B47dfa2229ed4296" : {desc:""},
    "0x001BD4e6Ef4F26f4825fbEFBA7d80bb572F3e72D" : {desc:""},
    "0x747Da2506702177bB07184C080038773ad928ef7" : {desc:""},
    "0x2B5b2138867af0cc6f24f6E2F8139efAef929B89" : {desc:""},
    "0xC459e812Bd5f43E0dA861bc4Fa80ffbCE26DC71F" : {desc:""},
    "0xb917AA3AF0e15fd8588486EB7D0a2ca81245E687" : {desc:""},
    "0x0a0C38F154406AbfA9C8c1BfF829586BC39A732B" : {desc:""},
    "0x93Fa6B6A15C319a1F36971F211c8a06535D9A5a9" : {desc:""},
    "0x12149220813C3d5F1267260E4aDe2A650f220455" : {desc:""},
    "0xDc2FF7cd117fA9fb121712657Dda7cF941203BEB" : {desc:""},
    "0x9c1B386637BEe6cCE8EbE3D1E3F2b883F73329cb" : {desc:""},
    "0x17C83c454609906FDDe906f320E7b3F90a0E2B2D" : {desc:""},
    "0x8D03020cE848a1E1f17e8f81a66C52866fD0130e" : {desc:""},
    "0xD6aA8E4eE9EB10A6F99b3913ee13038ae741ee14" : {desc:""},
    "0x358686DEF92fDD48152060df36B8307445352Ea6" : {desc:""},
    "0x1E526aB4c87ab26e47e95741489B2cB96f893E79" : {desc:""},
    "0x5301CaAbA90BdB9Ae3B70F1916f45C90E028a80a" : {desc:""},
    "0x4748C6F18c65D1F4cbB0290Cdd6aF8DF129dB1Bb" : {desc:""},
    "0x4252Ed989B6eF477Eaafdda5b07993B6aEDd722B" : {desc:""},
    "0x0Eac43945D419B61A991bEabe2c87c14Ce1501A5" : {desc:""},
    "0xCe7A4eFbEc87897A85f2c3EB1DFFD4C2f802fbBA" : {desc:""},
    "0x5565B5d668f82cD887d0A21eA512A3Def0b0b0b1" : {desc:""},
    "0x7C7B884dc480c4B0e1314a33130541F8FD039660" : {desc:""},
    "0x86FC1E1faCbB481B373FB2c564CD85d849EC131A" : {desc:""},
    "0x7f9d5F11080d031024FBCdCe323E3D7e3f6E2D61" : {desc:""},
    "0xbDa1362767cB6Cdb6F5c52Da13193139864604E1" : {desc:""},
    "0xf052C45508D0ec8CbC1b5DeC9423Bf3aA4FF7378" : {desc:""},
    "0x3Fd88ed885cB39080d7e8614f7AD4BE8994F2465" : {desc:""},
    "0xc9F35fCB4220D0AA171933D31FF0d48Aa1a34F16" : {desc:""},
    "0x2A9C628F9B1b06a881407AE457Ffb0E51FB7E752" : {desc:""},
    "0xB69E8624E6ed3766E5B12689533E1F89Aa21ed32" : {desc:""},
    "0xeF9BEc8646d73055f7241f41371d3FFEC59E4f75" : {desc:""},
    "0xFDc276505e80A23Fa469b1C72A6201569598b2d0" : {desc:""},
    "0x3E92aCcd6E766ee9F09D44b70deE02ccc34Fc265" : {desc:""},
    "0x39716C895981DF68bfaC088D23a877C424F2c94E" : {desc:""},
    "0xCCe4A5B3268EC5b280457E188d5D0C32a2ecb103" : {desc:""},
    "0x74B61DD9030398fF7E7764C6e50F9f974dc7d740" : {desc:""},
    "0xA928Fe9460F5fe02dF8a121290eA362aEEfCdbcA" : {desc:""},
    "0xB29201410352243Acb425A36b1Ceb058745bf9cd" : {desc:""},
    "0x715b7E106E429719Bb2Aa5ab897DF961CcF53C19" : {desc:""},
    "0x2607C38acB946fD446345A8990d1012eac3B812d" : {desc:""},
    "0xFB8A00995bd4cF3e0E582d732770E84da749FEE3" : {desc:""},
    "0x10379a88110EE864BD0716B5B53D8405056f9A30" : {desc:""},
    "0xCa021e95A73244fC878257a60e6b6445F146D835" : {desc:""},
    "0x23b9e507201ffABDB24382caeda35571583a886D" : {desc:""},
    "0x7bBa4DebE95068Ea57e707D3F68e312228b2CB39" : {desc:""},
    "0x9abF4AFb62f0719092e84df61b1b824Af94FB96d" : {desc:""},
    "0xF8Fb18C673B981024ad08fE1B874999bd55d79A3" : {desc:""},
    "0x9cE42bdb71fB64aD38Fd8002A13274EF04D0A37e" : {desc:""},
    "0xDB7F2201f37f186f97498086391D4fa6C628323d" : {desc:""},
    "0xFb09bE138BE0bA9800B68e55929983DbCF67c7f1" : {desc:""},
}


export const koa_buyback = {
    withdraw : "0x7156C4FBc9F533DF88F4Bd4ccddcb215c607Ca4E",
    company : "0x42D7c02176B95BfbDAE4D672F3e40f9aAC30e273",
    dead : "0x000000000000000000000000000000000000dead"
};

export const che_hdt_buyback = {
    buy : "0x6A657c0c931ca196c58B91DD805bB2C5662356FC", //回购hdt
    buy2: "0x0066242c89fF689507EE4cDcad1348492a338E6C"
}

export const crystal_buyback = {
    buyer1 : "0xEF12E7b6278f601402A3152a539A8C2300b34673", //一直少量购入
    buyer2 : "0x1a864595309018958FEf92fa6e7454ED26E3a521", ///少量买入
}

export const js_buyback = {
    buyer1 : "0x9F662aD945E9Baa602A80548C922591Aeb7b86dc", //回购jf
    buyer2 : "0x757d4c404517e5cf6BeB8c7b70DCF38666F8C7ff", //操作jf bac
}

export const wave_buyback = {
    contract : "0x802A7a27ab04dBab982D907710325e7Da292F452",
    buybackFunction : "0x6192c2a6",
    zapBuyLp     : "0x6c5c91a8",
    zapSellLp : "0xb95de2e3"
}

export const buy_back = {
    "0x9F662aD945E9Baa602A80548C922591Aeb7b86dc" : "jf",
    "0x6A657c0c931ca196c58B91DD805bB2C5662356FC" : "hdt",
    "0x0066242c89fF689507EE4cDcad1348492a338E6C" : "hdt2",
    "0x7156C4FBc9F533DF88F4Bd4ccddcb215c607Ca4E" : "koa",
    "0x5145D67645e94eA914D5bFe37D5E86d1C71a885a" : "blade",
    "0x7F61FC0d3d5Dc16120F9C1EfA7f0bB90dfc48632" : "hep",
}