import { BigNumber, ethers, providers, utils } from "ethers";
import bot from "../../bot";
import { UniswapV2Func, macro } from "../macro";
import { Pair } from "../type/Pair";
import { GetPairInfoResultDesc, SwapResult } from "../type/DataType";
import DecodeUniswapV2 from "../decoder/DecodeUniswapV2";
import Lazy from "../lazy/LazyController";
import tools from "../tools";


export default class RouterUni {
    routerName = "";
    routerAddr = "";
    factoryAddr = "";

    pairVersion = macro.PAIR_VERSION.V2;
    private univ2 = new DecodeUniswapV2();

    constructor(routerAddr:string){
        this.routerAddr = routerAddr;
        this.routerName = bot.config.routers[routerAddr].name;
    }

    async _view(addr:string, func:string){
        return bot.provider().call({to : addr, data: utils.id(func)});
    }

    async getFactory(){
        if(this.factoryAddr == ""){
            // 首先检查配置中是否有自定义factory地址
            const routerConfig = bot.config.routers[this.routerAddr];
            if(routerConfig && routerConfig.factory) {
                this.factoryAddr = routerConfig.factory;
                console.log(`${macro.COLOR.BBlack}[routeruni] router:${this.routerName.padEnd(12, " ")} factory(config): ${this.factoryAddr}${macro.COLOR.Off}`);
            } else {
                // 如果没有自定义factory地址，则通过合约调用获取
                // const lengthBn = await Promise.any([f.allPairsLength(), f.totalPairs()])
                // const f = await this._view(this.routerAddr, "factory()");

                let f;
                try {
                    //f = await this._view(this.routerAddr, "factoryV2()");
                    f = await this._view(this.routerAddr, "factory()");
                } catch (e) {
                    //console.log("factoryV2() call failed, falling back to factory()");
                    try {
                        f = await this._view(this.routerAddr, "factory()");
                    } catch(e) {
                        f = await this._view(this.routerAddr, "getV1Factory()");
                    }
                }
                const addr = bot.abiCoder.decode(["address"], f)[0];
                console.log(`${macro.COLOR.BBlack}[routeruni] router:${this.routerName.padEnd(12, " ")} factory: ${addr}${macro.COLOR.Off}`);
                this.factoryAddr = addr;
            }
        }
        return this.factoryAddr;
    }

    async getPair(tokenA:string, tokenB:string){
        if(this.factoryAddr == "") await this.getFactory();
        const f = new ethers.Contract(this.factoryAddr, macro.abi.factory, bot.provider());
        const addr = await f.getPair(tokenA, tokenB);
        return addr;
    }

    async allPairs(index:number){
        const f = new ethers.Contract(this.factoryAddr, macro.abi.factory, bot.provider());
        const addr = await f.allPairs(index);
        return addr;
    }

    async weth(){
        const res = await Promise.any([
            this._view(this.routerAddr, "WETH()"),
            this._view(this.routerAddr, "weth()")
        ]);
        const addr = bot.abiCoder.decode(["address"], res)[0];
        return addr;
    }

    _totalPairs = -1;
    async totalPairs(){
        if(this._totalPairs != -1) return this._totalPairs;
        if(this.factoryAddr == "") await this.getFactory();

        const f = new ethers.Contract(this.factoryAddr, macro.abi.factory, bot.provider());
        try {
            const lengthBn = await Promise.any([f.allPairsLength(), f.totalPairs()]) as BigNumber; //makiswap
            this._totalPairs = lengthBn.toNumber();
            return this._totalPairs;
        } catch(e) {
            console.log(e);
            console.log(`router: ${this.routerAddr}, factory: ${this.factoryAddr}`);
            throw("error router totalPairs: " + this.routerAddr);
        }

        /*
        //adaswap报错。未知原因
        let lengthBn : BigNumber;
        //有的router获取pair长度名字不一致
        try {
            const res = await Promise.any([
                bot.provider().call({to:this.factoryAddr, data: utils.id("totalPairs()")}), 
                bot.provider().call({to:this.factoryAddr, data: utils.id("allPairsLength()")}), //makiswap
            ]);
            lengthBn = (bot.abiCoder.decode(["uint"], res) as [BigNumber])[0];
        } catch(e) {
            console.log(e);
            throw("error router totalPairs: " + this.routerAddr);
        }
        return lengthBn.toNumber();
        */
    }


    async baseFee(addr="", stable = false){
        const r = bot.config.routers[this.routerAddr];
        return  r ? (r.fee || 99700) : 99700;
    }

    async decodeData(
        trans:ethers.providers.TransactionResponse,
        selector : UniswapV2Func,
        to :string,
        websocketId:number,
        onSwaps : (router : string, trans : ethers.providers.TransactionResponse, dataDecode : SwapResult[], websocketId:number)=> Promise<void>,
        onAddLiquidity : (trans : ethers.providers.TransactionResponse, tokenA:string, tokenB:string, amountADesired:BigNumber, amountBDesired:BigNumber) => void,
        onRemoveLiquidity : (trans : ethers.providers.TransactionResponse, tokenA:string, tokenB:string, liquidity:BigNumber) => Promise<void>
    ){
        const { data } = trans;
        switch(selector.set){
            case 1: //流动性逻辑
                if(selector.type == 0){
                    const result = this.univ2.decodeAddLiq(selector, data, trans);
                    onAddLiquidity(trans, result.tokenA, result.tokenB, result.amountADesired, result.amountBDesired);
                } else if(selector.type == 1){
                    const result = this.univ2.decodeRemoveLiq(selector, data, trans);
                    await onRemoveLiquidity(trans, result.tokenA, result.tokenB, result.liquidity);
                }
                break;
            case 2: //swap逻辑
                const result = this.univ2.decodeSwap(selector, data, trans);
                //Lazy.ins().logTs("[pending time] decode");
                if(result) await onSwaps(to, trans, [result], websocketId);
                break;
            case 3: //zksync mute addliq
                if(selector.type == 0){
                    const result = this.univ2.decodeAddLiqMute(selector, data, trans);
                    onAddLiquidity(trans, result.tokenA, result.tokenB, result.amountADesired, result.amountBDesired);
                } else if(selector.type == 1){
                    const result = this.univ2.decodeRemoveLiqMute(selector, data, trans);
                    await onRemoveLiquidity(trans, result.tokenA, result.tokenB, result.liquidity);
                }
                break;
            case 4: //zksync mute swap
                const result2 = this.univ2.decodeSwapMute(selector, data, trans);
                if(result2) await onSwaps(to, trans, [result2], websocketId);
                break;
            case 100: //kub dk swap
                const decode = this.univ2.decodeKubDK(data, trans);
                if(!decode) return;
                switch(decode.action){
                    case 0:
                        const r0 = decode.result as {tokenA: string; tokenB: string; amountADesired: BigNumber; amountBDesired: BigNumber};
                        onAddLiquidity(trans, r0.tokenA, r0.tokenB, r0.amountADesired, r0.amountBDesired);
                        break;
                    case 1:
                        const r1 = decode.result as {tokenA: string; tokenB: string; liquidity: BigNumber;}
                        await onRemoveLiquidity(trans, r1.tokenA, r1.tokenB, r1.liquidity);
                        break;
                    case 2:
                        const r2 = decode.result as SwapResult;
                        onSwaps(decode.router, trans, [r2], websocketId);
                        break;
                }
                break;
            case 101: //pls multicall
                const decode2 = this.univ2.decodePls(data);
                Lazy.ins().log1(`${macro.COLOR.BBlack}[multicall]: ${trans.hash}${macro.COLOR.Off}`);
                if(decode2.length > 0){
                    onSwaps(decode2[0].to, trans, decode2, websocketId);
                }
                break;
            case 103: //pls multi2
                const decode4 = this.univ2.decodePls2(data);
                Lazy.ins().log1(`${macro.COLOR.BBlack}[multicall2]: ${trans.hash}${macro.COLOR.Off}`);
                if(decode4.length > 0){
                    onSwaps(decode4[0].to, trans, decode4, websocketId);
                }
                break;
            case 104: //okx 聚合
                Lazy.ins().log1(`${macro.COLOR.BBlack}[okx single]: ${trans.hash}${macro.COLOR.Off}`);
                const decode3 = this.univ2.decodeOkxSingle(data);
                if(!decode3) return;
                //console.log(decode3);
                //聚合router: 0xf6aab105cb9e66e03cad2c2f3f8558242593385c
                //onSwaps("0xf6aab105cb9e66e03cad2c2f3f8558242593385c", trans, [decode3], websocketId);
                const bIn = bot.token(decode3.tokenIn as string);
                const nums = bot.oracleV2.getAmountsOutByPairLocal(bIn.address, bIn.toNum(decode3.amountIn), decode3.path);
                const tokenOuts = bot.oracleV2.data.getTokenOuts(bIn.address, decode3.path);
                decode3.path.forEach((_p, i)=>{
                    if(i >= decode3.path.length) return;
                    const p = bot.oracleV2.data.pm.get(_p);
                    if(p){
                        try {
                            let swap : SwapResult = {
                                amountIn : bot.token(tokenOuts[i]).toBigNumber(nums[i]),
                                amountOut : macro.bn.zero,
                                path : [tokenOuts[i], tokenOuts[i+1]],
                                tokenIn : tokenOuts[i],
                                deadline : BigNumber.from(0),
                                exactIn : true,
                                isEth : false,
                                to : trans.from
                            };
                            //swapDatas.push(swap);
                            console.log("--------- swap ----------" , swap);
                            console.log("router: ", p.routers[0]);
                            onSwaps(p.routers[0], trans, [swap], websocketId);
                        } catch(e) {
                            console.log("error decode okx single");
                        }

                    }
                });

        }
    }

    async getAmountsOut(amountIn:BigNumber, path:string[]){
        const c = new ethers.Contract(this.routerAddr, macro.abi.router, bot.provider());
        const res : BigNumber[] = await c.getAmountsOut(amountIn, path);
        return res;
    }

    async getAmountOut(amountIn:BigNumber, reserveIn:BigNumber, reserveOut:BigNumber){
        const c = new ethers.Contract(this.routerAddr, macro.abi.router, bot.provider());
        return await c.getAmountOut(amountIn, reserveIn, reserveOut) as BigNumber;
    }

    async isStable(addr:string){ return false }

    async getPairReserves(pair:string){
        let r0 : BigNumber;
        let r1 : BigNumber;
        let blockTimestampLast = 0;
        //"function getReserves()"
        const data = await bot.provider().call({to:pair, data: "0x0902f1ac"});
        const [_reserve0, _reserve1, _blockTimestampLast] = bot.abiCoder.decode(["uint", "uint", "uint"], data);
        r0 = _reserve0;
        r1 = _reserve1;
        blockTimestampLast = (_blockTimestampLast as BigNumber).toNumber();
        return {
            r0: r0,
            r1 : r1,
            blockTimestampLast : blockTimestampLast
        };
    }

    async _getPair_old(addr:string){
        const pair = new ethers.Contract(addr, macro.abi.pair, bot.provider());
        const r = await this.getPairReserves(addr);
        const _isStable = await this.isStable(addr);

        const token0 = await pair.token0();
        const token1 = await pair.token1();

        let token0Info = await bot.oracleV2.data.pm.tokens.getAndUpdate(token0);
        let token1Info = await bot.oracleV2.data.pm.tokens.getAndUpdate(token1);

        let pairInfo = new Pair();
        pairInfo.address = addr;
        pairInfo.token0 = token0;
        pairInfo.token1 = token1;
        pairInfo.blockTimestampLast = r.blockTimestampLast;
        pairInfo.token0Symbol = token0Info.symbol;
        pairInfo.reserve0 = Number(utils.formatUnits(r.r0, token0Info.decimals));
        if(r.r0.lt(BigNumber.from(10))) pairInfo.reserve0 = 0; //忽略不能分割bignumber的数量，基本是假币

        pairInfo.token1Symbol = token1Info.symbol;
        pairInfo.reserve1 = Number(utils.formatUnits(r.r1, token1Info.decimals));
        if(r.r1.lt(BigNumber.from(10))) pairInfo.reserve1 = 0;

        pairInfo.version = this.pairVersion;
        pairInfo.fp = await this.baseFee(addr, _isStable); //router里pair的fee
        //console.log("_getPair: ", pairInfo.fp);
        return pairInfo;
    }

    async getPairInfo(addr:string){
        //return await this._getPair(addr);
        let res = await bot.contractIns.batchGetPairInfo([addr]);
        const p = res[0];
        p.version = this.pairVersion;
        p.fp = await this.baseFee(addr, p.stable); //router里pair的fee
        return p;
    }

    async batchGetRouterPairs(from:number, to:number){
        //console.log("[batchGetPairInfo] -------------- ", from, to);
        const iBot = new ethers.utils.Interface(macro.abi.bot);
        let data = iBot.encodeFunctionData("batchGetRouterPairs", [this.routerAddr, BigNumber.from(from), BigNumber.from(to)]);
        let raw = "";
        let resServer : GetPairInfoResultDesc[] = [];
        try {
            raw = await bot.provider().call({
                data : data,
                to : bot.config.bot.address,
                from : await bot.getbotOwner()
            });
            resServer = iBot.decodeFunctionResult("batchGetRouterPairs", raw)[0];
        } catch(e){
            for(let i = from; i <= to; i++){
                data = iBot.encodeFunctionData("batchGetRouterPairs", [this.routerAddr, BigNumber.from(i), BigNumber.from(i+1)]);
                try {
                    raw = await bot.provider().call({
                        data : data,
                        to : bot.config.bot.address,
                        from : await bot.getbotOwner()
                    });
                    const resultOne = iBot.decodeFunctionResult("batchGetRouterPairs", raw)[0][0];
                    if(resultOne){
                        resServer.push(resultOne);
                    } else {
                        console.log(`from: ${i}, to: ${i+1}`);
                        console.log(iBot.decodeFunctionResult("batchGetRouterPairs", raw));
                        throw("error batchGetPairInfo");
                    }
                } catch(e){
                    //出错了，改成单个更新
                    //console.log(e);
                    console.log(`#### ERROR ERROR ERROR router:${this.routerAddr} from:${i} to:${i+1}  ######`);
                    await tools.delay(2000);
                }

            }
        }
        const res : Pair[] = [];
        let j = 0;
        //console.log("[batchGetRouterPairs] resServer: ", resServer);
        for (const x of resServer){
            if(!x) console.log(x, j, resServer.length);
            const d = new Pair();
            d.id = x.id.toNumber();
            d.address = x.addr;
            d.token0 = x.token0;
            d.token1 = x.token1;
            d.d0 = x.d0.toNumber();
            d.d1 = x.d1.toNumber();

            //let token0Info = await bot.oracleV2.data.tokens.getAndUpdate(x.token0);
            //let token1Info = await bot.oracleV2.data.tokens.getAndUpdate(x.token1);

            d.token0Symbol = x.s0;
            d.token1Symbol = x.s1;

            d.reserve0 = Number(utils.formatUnits(x.r0, x.d0.toNumber()));
            d.reserve1 = Number(utils.formatUnits(x.r1, x.d1.toNumber()));
            d.blockTimestampLast = x.lastUpdate.toNumber();
            d.version = this.pairVersion;
            //d.stable = x.stable;
            d.fp = await this.baseFee(x.addr, x.stable);
            res.push(d);
            j++;
        }
        //console.log("[batchGetRouterPairs] res: ", res);
        return res;
    }
}