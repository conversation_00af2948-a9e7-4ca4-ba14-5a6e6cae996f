import { BigNumber, ethers, utils } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";
import RouterUni from "./RouterUni";

export default class RouterUniV2Stable extends RouterUni {
    _stableFee   = 100000 - 300;
    _volatileFee = 100000 - 30;


    ////https://optimistic.etherscan.io/address/******************************************#code // 固定是5/10000
    async isStable(addr:string){
        const f = await bot.provider().call({to:addr, data: utils.id("stable()")});
        const r = bot.abiCoder.decode(["bool"], f)[0];
        console.log("routeruni isStable: ", r);
        return r as boolean; 
    }

    //TODO: 需要改成动态获取
    async baseFee(addr="", stable = false) : Promise<number>{
        //tableFee = 4; // 0.04%        volatileFee = 30;
        const config = bot.config.routers[this.routerAddr];
        if(stable){
            return config.stableFee || this._stableFee;
        } else {
            return config.volatileFee || this._volatileFee;
        }
    }
    /*
    async getPairInfo(addr:string){
        let p = await this._getPair(addr);
        const stable = await this.isStable(addr);
        p.version = stable ? macro.PAIR_VERSION.V2_STABLE : macro.PAIR_VERSION.V2;
        return p;
    }
    */

}