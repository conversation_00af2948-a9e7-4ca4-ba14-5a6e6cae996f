import { BigNumber, ethers, utils } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";
import RouterUni from "./RouterUni";

export default class RouterUniV1 extends RouterUni {
    pairVersion = macro.PAIR_VERSION.V1;

    async getPairReserves(pair:string){
        let r0 : BigNumber;
        let r1 : BigNumber;

        //"function getReserves()"
        const data = await bot.provider().call({to:pair, data: "0x0902f1ac"});
        const [_reserve0, _reserve1] = bot.abiCoder.decode(["uint", "uint"], data);
        r0 = _reserve0;
        r1 = _reserve1;
        return {
            r0: r0,
            r1 : r1,
            blockTimestampLast : 0
        };
    }

}