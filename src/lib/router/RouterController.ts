import bot from "../../bot";
import { macro } from "../macro";
import RouterUni from "./RouterUni";
import RouterUniV1 from "./RouterUniV1";
import RouterUniV2Eoa from "./RouterUniV2Eoa";
import RouterUniV2KavaEqu from "./RouterUniV2KavaEqu";
import RouterUniV2OnlyRouter from "./RouterUniV2OnlyRouter";
import RouterUniV2Stable from "./RouterUniV2Stable";
import RouterUniV2StableFee from "./RouterUniV2StableFee";
import RouterUniV3 from "./RouterUniV3";

export default class RouterController {

    adapter : Map<string, RouterUni> = new Map();

    constructor(){
        for(const [k,v] of Object.entries(bot.config.routers)){
            let addr = k.toLocaleLowerCase();
            const type = v.type || macro.ROUTER_TYPE.V2;
            switch(type){
                case macro.ROUTER_TYPE.V1:
                    this.adapter.set(addr, new RouterUniV1(addr));
                    break;
                case macro.ROUTER_TYPE.V2:
                    this.adapter.set(addr, new RouterUni(addr));
                    break;
                case macro.ROUTER_TYPE.V3:
                    this.adapter.set(addr, new RouterUniV3(addr));
                    break;
                case macro.ROUTER_TYPE.V2_ONLY_ROUTER:
                    this.adapter.set(addr, new RouterUniV2OnlyRouter(addr));
                    break;
                case macro.ROUTER_TYPE.V2_STABLE: //有stable和其他两种fee
                    this.adapter.set(addr, new RouterUniV2Stable(addr));
                    break;
                case macro.ROUTER_TYPE.V2_STABLE_FEE: //每个pair都有单独的fee
                    this.adapter.set(addr, new RouterUniV2StableFee(addr));
                    break;
                case macro.ROUTER_TYPE.V2_EOA:
                    this.adapter.set(addr, new RouterUniV2Eoa(addr));
                    break;
                case macro.ROUTER_TYPE.V2_KAVA_EQU:
                    this.adapter.set(addr, new RouterUniV2KavaEqu(addr));
                    break;
            }
        }
    }

    get(addr:string){
        return this.adapter.get(addr) as RouterUni;
    }

}