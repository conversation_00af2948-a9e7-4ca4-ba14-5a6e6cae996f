import { BigNumber, ethers, utils } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";
import RouterUniV2Stable from "./RouterUniV2Stable";

export default class RouterUniV2StableFee extends RouterUniV2Stable {
    //https://optimistic.etherscan.io/address/******************************************#code // 固定是5/10000
    //stableFee = -1;
    //volatileFee = -1;


    //需要动态获取fee
    //https://explorer.zksync.io/address/******************************************#contract  ////pairFee 1 = 0.01% - 1000 = 10%
    async baseFee(addr:string,  stable = false){
        const pair = new ethers.Contract(addr, macro.abi.pair, bot.provider());
        const fee : BigNumber = await pair.pairFee();
        return ~~(100000 - fee.toNumber() * 10);
    }


}