import bot from "../../bot";
import { macro } from "../macro";
import { MevPath } from "../type/DataType";

export default class Helper {
    public static displayMevs(ms : MevPath[]){
        ms.forEach(m=>{
            const stable = bot.token(m.s0);
            console.log(`(${bot.config.tokens[stable.address].name}) ${bot.oracleV2.data.getTokenOuts(stable.address, m.pairs, true).join(' -> ')} ${macro.COLOR.BBlack} weight:${m.weight}${macro.COLOR.Off}`);
            console.log(`${macro.COLOR.BBlack}${m.pairs.join(",")}${macro.COLOR.Off}`);
        });
        console.log(`length: ${ms.length}`);
    }
}