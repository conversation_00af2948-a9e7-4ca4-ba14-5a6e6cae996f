use std::{collections::HashSet, sync::Arc};

use alloy::{eips::{BlockId, BlockNumberOrTag}, network::{Ethereum, Network}, primitives::Address, providers::{Provider, RootProvider}, rpc::types::{BlockTransactionsKind, Filter, Log}, transports::BoxTransport};
use colored::Colorize;
use dashmap::DashMap;
use futures::StreamExt;
use tokio::{sync::mpsc::Receiver, task::JoinHandle};

use crate::{connector::{error::{ConnectorError, HeaderSendErrorWrapper, LogsSendErrorWrapper, UpdatedPoolsSendErrorWrapper}, Connector}, vira::{pool::{DexPool, Status, POOL}, status::pools::Pools}};

use super::{dex::{factory::{DexFactory, FACTORY}, uni_v2::factory::UniV2Factory}, errors::{DEXError, EventLogError}};

///----------------- Sync -----------------
/// 订阅区块, 并根据区块更新stage_manager的data, 单独一个task
/// 返回:
/// - block_rx: 更新block, 获取block num, min gas price 
///     [USAGE]: excutor
/// - log_rx: 根据log嗅探新的pool
///     [USAGE]: log strategy
/// - updated_pools_rx: 最新block里有变动的pool地址 
///     [USAGE]: trash strategy
/// - stream_handle: 订阅区块的task

pub async fn subscribe_blocks_buffered(
    sm_data : Arc<Pools>,
    connector : Arc<Connector>
) -> (
    Receiver<<Ethereum as Network>::HeaderResponse>,
    Receiver<Vec<Log>>,
    Receiver<HashSet<Address>>,
    JoinHandle<Result<(), ConnectorError>>,
) {
    println!("subscribe_blocks_buffered {} begin....", connector.url);
    let (header_tx, header_rx) = tokio::sync::mpsc::channel(8);
    let (log_tx, log_rx) = tokio::sync::mpsc::channel(8);
    let (updated_pools_tx, updated_pools_rx) = tokio::sync::mpsc::channel(8);

    let provider = connector.provider().clone();
    let is_http = connector.url.contains("http://");

    let stream_handle = tokio::spawn(async move {
        loop {
            let result = if is_http {
                // HTTP 轮询模式
                handle_http_polling(&provider, &header_tx, &log_tx, &updated_pools_tx, sm_data.clone()).await
            } else {
                // WebSocket 订阅模式
                handle_websocket_subscription(&provider, &header_tx, &log_tx, &updated_pools_tx, sm_data.clone()).await
            };

            // 处理错误，使用红色输出
            if let Err(e) = result {
                println!("{}", format!("Provider error: {:?}, retrying in 2 seconds...", e).red());
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
                continue;
            }
        }
    });

    (header_rx, log_rx, updated_pools_rx, stream_handle)
}

// 提取获取 logs 的公共逻辑
async fn fetch_and_process_logs(
    provider: &RootProvider<Ethereum>,
    last_block: u64,
    current_block: u64,
    log_tx: &tokio::sync::mpsc::Sender<Vec<Log>>,
    updated_pools_tx: &tokio::sync::mpsc::Sender<HashSet<Address>>,
    sm_data: Arc<Pools>,
) -> Result<(), ConnectorError> {
    // 获取logs
    let logs = provider.get_logs(&Filter::new().from_block(last_block+1).to_block(current_block)).await?;
    
    let updated_pools = handle_state_changes_from_logs(sm_data.clone(), &logs).await?;
    updated_pools_tx.send(updated_pools).await.map_err(UpdatedPoolsSendErrorWrapper)?;
    log_tx.send(logs).await.map_err(LogsSendErrorWrapper)?;
    
    Ok(())
}

// HTTP 轮询处理函数
async fn handle_http_polling(
    provider: &RootProvider<Ethereum>,
    header_tx: &tokio::sync::mpsc::Sender<<Ethereum as Network>::HeaderResponse>,
    log_tx: &tokio::sync::mpsc::Sender<Vec<Log>>,
    updated_pools_tx: &tokio::sync::mpsc::Sender<HashSet<Address>>,
    sm_data: Arc<Pools>,
) -> Result<(), ConnectorError> {
    let mut last_block = provider.get_block_number().await?;
    
    loop {
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        let current_block = provider.get_block_number().await?;

        if current_block > last_block {
            // 获取新区块
            if let Some(block) = provider.get_block(
                BlockId::Number(BlockNumberOrTag::Number(current_block))
            ).await? {
                header_tx.send(block.header).await.map_err(HeaderSendErrorWrapper)?;
                fetch_and_process_logs(provider, last_block, current_block, log_tx, updated_pools_tx, sm_data.clone()).await?;
                last_block = current_block;
            }
        }
    }
}

// WebSocket 订阅处理函数
async fn handle_websocket_subscription(
    provider: &RootProvider<Ethereum>,
    header_tx: &tokio::sync::mpsc::Sender<<Ethereum as Network>::HeaderResponse>,
    log_tx: &tokio::sync::mpsc::Sender<Vec<Log>>,
    updated_pools_tx: &tokio::sync::mpsc::Sender<HashSet<Address>>,
    sm_data: Arc<Pools>,
) -> Result<(), ConnectorError> {
    let mut last_block = provider.get_block_number().await?;
    
    let subscription = provider.subscribe_blocks().await?;
    let mut block_stream = subscription.into_stream();
    
    while let Some(block) = block_stream.next().await {
        let current_block = block.number;
        header_tx.send(block).await.map_err(HeaderSendErrorWrapper)?;
        fetch_and_process_logs(provider, last_block, current_block, log_tx, updated_pools_tx, sm_data.clone()).await?;
        last_block = current_block;
    }
    
    Ok(())
}

//更新data
async fn handle_state_changes_from_logs(sm_data : Arc<Pools>, logs:&Vec<Log>) -> Result<(HashSet<Address>), EventLogError> {
    // For each log, check if the log is from an amm in the state space and sync the updates
    let mut updated_pools = HashSet::new();
    for log in logs.into_iter() {
        //let log_block_number = get_block_number_from_log(&log)?;
        let log_address = log.address();
        if let Some(mut pool) = sm_data.data.get_mut(&log_address) {
            pool.sync(log)?;
            updated_pools.insert(log_address);
        }
    }
    Ok(updated_pools)
}


#[cfg(test)]
mod tests {
    use super::*;

    use std::{fs::read_to_string, sync::atomic::Ordering};
    use crate::{tools::now_str, vira::Vira};
    use colored::Colorize;
    use eyre::Result;


    #[tokio::test]
    async fn test_subscribe() -> Result<()> {
            //使用config/pls.rs的配置生成vira
            let mut vira = Vira::new().await;
    
            //let checkpoint = load_checkpoint("./data/pls/checkpoint.json").unwrap();
            //vira.sync(true, true).await;
            //vira.sm.load_pools(checkpoint);
            //let arc_vira = Arc::new(vira);
            let pools = vira.sm.pools.clone();
            let (mut header_rx, mut log_rx, mut updated_pools_rx, _) = subscribe_blocks_buffered(vira.sm.pools.clone(), vira.connector.clone()).await;

            loop {
                tokio::select! {
                    Some(block) = header_rx.recv() => {
                        println!("{} {}", now_str(), format!("-------------------------------- block num: {} --------------------------------", block.number).on_green());

                        //println!("{:?}", block);
                    }
                    Some(logs) = log_rx.recv() => {
                        println!("{} {}", now_str(), format!("-------------------------------- logs: {} --------------------------------", logs.len()).purple());
                        pools.sync(&logs).unwrap();

                        //for log in logs { println!("{:?}", log);}
                    }
                    Some(updated_pools) = updated_pools_rx.recv() => {
                        println!("{} {}", now_str(), format!("-------------------------------- updated pools: {} --------------------------------", updated_pools.len()).blue());
                    }
                    else => break
                }
            }
            Ok(())
    }

}