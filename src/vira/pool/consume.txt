use std::{collections::{BTreeMap, HashMap}, default, cmp::Reverse};

use alloy::primitives::{Address, U256};

use super::{manager::{PoolIndex, PoolManager}, mev::Mev<PERSON>ath, DexPool};


#[derive(Debug, Default)]
pub struct Consume {
    //pool的初始 reserve
    cache : HashMap<Address, Vec<U256>>,
    //初始index
    cache_index : HashMap<Address, BTreeMap<(Reverse<U256>, Address), PoolIndex>>,
}

impl Consume {
    pub fn new() -> Consume {
        Consume {
            ..default::Default::default()
        }
    }

    /// Consumes the specified MevPath from the PoolManager, updating the pool reserves and index.
    ///
    /// This function iterates through the temporary pool indices in the MevPath, and for each pool:
    /// - Checks if the pool should be excluded from the consumption
    /// - Backs up the current reserve and index values in the Consume struct
    /// - Consumes the pool based on the MevPath's lowest value and values
    /// - Updates the index sorted by the new reserve values
    ///
    /// The function does not return any value, but modifies the PoolManager and Consume struct in-place.
    pub fn consume(&mut self, pm : &mut PoolManager, mev : &MevPath, values : &Vec<U256>, lowest : &U256, exclude: Option<&Address>)
    {
        for (i, p) in mev.tmp_pool_index.iter().enumerate(){
            let pool = pm.data.get_mut(&p.addr).unwrap();
            let tokens = &pool.data().tokens.clone();
            let old_reserve : Vec<U256> = tokens.iter().map(|_p| { _p.reserve }).collect();

            //如果exclude等于p.addr 跳过循环
            if exclude.map_or(false, |addr| *addr == p.addr) {
                continue; // 跳过当前循环迭代
            }

            //备份reserve
            if !self.cache.contains_key(&p.addr) {
                //加入缓存
                self.cache.insert(p.addr.clone(), old_reserve.clone());
            }
            //备份indexs
            tokens.iter().for_each(|t| {
                if !self.cache_index.contains_key(&t.addr) {
                    self.cache_index.insert(t.addr.clone(), pm.indexs_sorted.get(&t.addr).unwrap().clone());
                }
            });

            //根据mev.tmp_values和mev.lowest消耗对应的pool
            let now_reserve = pool.consume(&lowest, &values[i]);

            //遍历tokens，更新indexs_sorted
            for (i, t) in tokens.iter().enumerate(){
                let indexs = pm.indexs_sorted.get_mut(&t.addr).unwrap();
                let key = (Reverse(old_reserve[i]), p.addr.clone());
                let old_index = indexs.get(&key).unwrap().clone();
                indexs.remove(&key);
                indexs.insert((Reverse(now_reserve[i]), p.addr.clone()), old_index);
            }

            /*
            if now_reserve[0].is_zero() {
                for (i, t) in tokens.iter().enumerate(){
                    let indexs = pm.indexs_sorted.get_mut(t.addr.as_str()).unwrap();
                    dbg!(indexs);
                }
                panic!("reserve is zero");
            }
             */
        };
    }

    //根据cache和cache_index恢复pm的data和indexs_sorted
    pub fn resume(&mut self, pm :&mut PoolManager)
    {
        for (addr, reserve) in self.cache.iter(){
            let pool = pm.data.get_mut(addr).unwrap();
            pool.data_mut().tokens.iter_mut().enumerate().for_each(|(i, t)| {
                t.reserve = reserve[i];
            });
        }

        for (addr, indexs) in self.cache_index.iter(){
            let indexs_sorted = pm.indexs_sorted.get_mut(addr).unwrap();
            indexs_sorted.clear();
            indexs.iter().for_each(|(k, v)| {
                indexs_sorted.insert(k.clone(), v.clone());
            });
        }

        self.cache.clear();
        self.cache_index.clear();
    }

}