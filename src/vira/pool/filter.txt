use std::{cmp::Reverse, collections::{BTreeMap, HashMap, HashSet}};

use alloy::primitives::{Address, U256};

use super::{manager::{PoolIndex, PoolManager}, mev::Mev<PERSON>ath, DexPool, POOL};




#[derive(Debu<PERSON>, <PERSON>fault, Clone)]
pub struct Filter {
    //缓存data的属性
    pub cache_data : HashMap<Address, POOL>,
    pub cache_indexs_sorted : HashMap<Address, BTreeMap<(Reverse<U256>, Address), PoolIndex>>,



    pub exclude_pools : HashSet<Address>,
}

impl Filter {
    pub fn new(exclude_pool : &Address) -> Filter {
        Filter {
            ..Default::default()
        }
    }
    
    pub fn add_exclude(&mut self, pool: &Address) {
        self.exclude_pools.insert(pool.clone());
    }

    pub fn batch_add_exclude<T>(&mut self, pools: &Vec<T>)
    where
        T: std::borrow::<PERSON>rrow<PoolIndex>,
    {
        for p in pools {
            self.exclude_pools.insert(p.borrow().addr);
        }
    }

    pub fn is_exclude(&self, pool: &Address) -> bool {
        self.exclude_pools.contains(pool)
    }

    pub fn consume(&mut self, pm : &mut PoolManager, mev : &MevPath, values : &Vec<U256>, lowest_value : &U256, exclude: Option<&Address>)
    {
        for (i, p) in mev.tmp_pool_index.iter().enumerate(){
            //如果exclude等于p.addr 跳过循环
            if let Some(addr) = exclude {
                if *addr == p.addr {
                    continue;
                }
            }

            //缓存POOL
            let pool_cache = self.cache_data.entry(p.addr).or_insert_with(||{
                let pool = pm.data.get(&p.addr).unwrap();
                //缓存indexs
                pool.data().tokens.iter().for_each(|t| {
                    self.cache_indexs_sorted.entry(t.addr.clone())
                        .or_insert_with(|| pm.indexs_sorted[&t.addr].clone());
                });
                pool.clone()
            });

            //根据mev.tmp_values和mev.lowest消耗对应的pool
            let old_reserve : Vec<U256> = pool_cache.data().tokens.iter().map(|_p| { _p.reserve }).collect();
            let new_reserve = pool_cache.consume(lowest_value, &values[i]);

            //遍历tokens，更新indexs_sorted
            for (j, t) in pool_cache.data().tokens.iter().enumerate(){
                let indexses = self.cache_indexs_sorted.get_mut(&t.addr).unwrap();
                let old_key = (Reverse(old_reserve[j]), p.addr);
                let new_key = (Reverse(new_reserve[j]), p.addr);

                if let Some(value) = indexses.remove(&old_key) {
                    indexses.insert(new_key, value);
                }
            }
        };
    }

    pub fn clear_all(&mut self) {
        self.cache_data.clear();
        self.cache_indexs_sorted.clear();
        self.exclude_pools.clear();
    }

}
