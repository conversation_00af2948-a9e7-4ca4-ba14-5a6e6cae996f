use std::time::SystemTimeError;

use alloy::transports::TransportError;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum DEXError {
    #[error(transparent)]
    TransportError(#[from] TransportError),
    #[error(transparent)]
    ContractError(#[from] alloy::contract::Error),
    #[error(transparent)]
    EventLogError(#[from] EventLogError),
    #[error("Block number not found")]
    BlockNumberNotFound,
    #[error(transparent)]
    EyreError(#[from] eyre::Error),
    #[error(transparent)]
    ABICodecError(#[from] alloy::dyn_abi::Error),
    #[error("Unsupported pool type")]
    UnsupportedPoolType,
    #[error(transparent)]
    CheckpointError(#[from] CheckpointError),
    #[error(transparent)]
    JoinError(#[from] tokio::task::JoinError),
    #[error(transparent)]
    AbiDecodeError(#[from] alloy::sol_types::Error),

    #[error("Block Number Does not Exist")]
    MissingBlockNumber,
}


#[derive(Error, Debug)]
pub enum EventLogError {
    #[error("Invalid event signature")]
    InvalidEventSignature,
    #[error("Log Block number not found")]
    LogBlockNumberNotFound,
    #[error(transparent)]
    EthABIError(#[from] alloy::sol_types::Error),
    #[error(transparent)]
    ABIError(#[from] alloy::dyn_abi::Error),
}

#[derive(Error, Debug)]
pub enum CheckpointError {
    #[error(transparent)]
    SystemTimeError(#[from] SystemTimeError),
    #[error(transparent)]
    SerdeJsonError(#[from] serde_json::error::Error),
    #[error(transparent)]
    IOError(#[from] std::io::Error),
}

#[derive(Error, Debug)]
pub enum SwapSimulationError {
    #[error("Could not get next tick")]
    InvalidTick,
    #[error("Liquidity underflow")]
    LiquidityUnderflow,
}