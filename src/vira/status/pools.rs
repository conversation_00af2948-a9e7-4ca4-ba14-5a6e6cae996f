use std::collections::HashSet;

use alloy::primitives::Address;
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use crate::vira::{errors::DEXError, pool::{DexPool, POOL}, status::mev::Mev};


#[derive(Default, Serialize, Deserialize)]
pub struct Pools {
    pub data : DashMap<Address, POOL>,
    pub mevs : DashMap<Address, Vec<Mev>>,
}

impl Pools {
    pub fn new() -> Self {
        Self { data: DashMap::new(), mevs: DashMap::new() }
    }

    /*
    pub fn add(&mut self, pool: POOL) {
        self.data.insert(pool.addr(), pool);
    }
    */
    
    pub fn sync(&self, logs: &[alloy::rpc::types::Log]) -> Result<Vec<Address>, DEXError> {
        let mut affected_amms = HashSet::new();

        for log in logs {
            let address = log.address();
            if let Some(mut amm) = self.data.get_mut(&address) {
                affected_amms.insert(address);
                amm.sync(log)?;
            }
        }

        Ok(affected_amms.into_iter().collect())
    }
}