use std::{cmp::Reverse, collections::{BTreeMap, HashMap}};

use alloy::primitives::{Address, U256};

use crate::vira::pool::{DexPool, POOL};

use super::{{PoolIndex, StatusManager}, mev::Mev};




//不更新index和pools的情况下, 缓存变动的pools
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Cache {
    pub exclude_pool : Address,

    //缓存data的属性
    pub cache_data : HashMap<Address, POOL>,
    pub cache_indexs_sorted : HashMap<Address, BTreeMap<(Reverse<U256>, Address), PoolIndex>>,
}

impl Cache {
    pub fn new(addr : &Address) -> Cache {
        Cache {
            exclude_pool : addr.clone(),
            ..Default::default()
        }
    }

    pub fn consume(&mut self, pm : &StatusManager, mev : &Mev, values : &Vec<U256>, lowest_value : &U256)
    {
        for (i, p) in mev.pools.iter().enumerate(){
            //如果exclude等于p.addr 跳过循环
            if p.addr == self.exclude_pool { continue; }

            //缓存POOL
            let pool_cache = self.cache_data.entry(p.addr).or_insert_with(||{
                let pool = pm.pools.data.get(&p.addr).unwrap();
                //缓存indexs
                pool.data().tokens.iter().for_each(|t| {
                    if !self.cache_indexs_sorted.contains_key(&t.addr) {
                        self.cache_indexs_sorted.insert(t.addr.clone(), pm.index.data.get(&t.addr).unwrap().clone());
                    }
                });
                pool.clone()
            });


            //根据mev.tmp_values和mev.lowest消耗对应的pool
            let old_reserve : Vec<U256> = pool_cache.data().tokens.iter().map(|_p| { _p.reserve }).collect();
            //消费并更新到cache
            let new_reserve = pool_cache.consume(lowest_value, &values[i]);
            //dbg!(&new_reserve);
            //遍历tokens，更新indexs_sorted
            for (j, t) in pool_cache.data().tokens.iter().enumerate(){
                let indexses = self.cache_indexs_sorted.get_mut(&t.addr).unwrap();
                let old_key = (Reverse(old_reserve[j]), p.addr);
                let new_key = (Reverse(new_reserve[j]), p.addr);
                if let Some(v) = indexses.remove(&old_key) {
                    indexses.insert(new_key, v);
                }
            }
        };
    }
}


// 用于计算收益的cache
pub struct CacheArbitrage {
    pub data : HashMap<Address, POOL>,
}

impl CacheArbitrage {
    pub fn new() -> CacheArbitrage {
        CacheArbitrage {
            data : HashMap::new(),
        }
    }

    pub fn add(&mut self, pool : &POOL) {
        let data = pool.data();
        self.data.insert(data.addr, pool.clone());
    }
}