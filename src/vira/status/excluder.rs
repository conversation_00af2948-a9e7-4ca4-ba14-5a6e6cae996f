use std::collections::HashSet;
use alloy::primitives::Address;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct Excluder {
    pub pool : HashSet<Address>,
    //存放主pool，单条path计算后清空
    //pub main_pool : HashSet<Address>,
}

impl Excluder {
    pub fn new() -> Self {
        Excluder {
            ..Default::default()
        }
    }

    pub fn insert(&mut self, addr: Address) {
        self.pool.insert(addr);
    }

    /*
    pub fn insert_main(&mut self, addr: &Address) {
        if addr.contains("_") {
            let main_pool = addr.split("_").collect::<Vec<&str>>()[0];
            self.main_pool.insert(main_pool.to_string());
        }
    }
    */

    //如果是子pool，把父pool也加入到排除列表
    /*
    pub fn insert_all(&mut self, addr: Address) {
        self.pool.insert(addr);
        if addr.contains("_") {
            let main_pool = addr.split("_").collect::<Vec<&str>>()[0];
            self.pool.insert(main_pool.to_string());
        }
    }
    */
    
    pub fn batch_insert(&mut self, addrs: Vec<Address>) {
        for addr in addrs {
            self.pool.insert(addr);
        }
    }
    /*
    pub fn batch_insert_all(&mut self, addrs: &Vec<&PoolIndex>) {
        for addr in addrs {
            self.insert_all(addr.addr);
        }
    }
    */
    //把当前pair的主pool加入到排除列表
    /*
    pub fn refresh_main(&mut self, ps: &Vec<&PoolIndex>) {
        self.clear_main();
        for p in ps {
            self.insert_main(&p.addr);
        }
    }
     */

    //检查是否包含自己和父pool
    //TODO: 去除父pool
    pub fn contains(&self, addr: &Address) -> bool {
        //if addr.contains("_") {
        //    let main_pool = addr.split("_").collect::<Vec<&str>>()[0];
        //    self.pool.contains(addr) || self.pool.contains(main_pool) || self.main_pool.contains(main_pool)
        //} else {
        //    self.pool.contains(addr) || self.main_pool.contains(addr)
        //}
        self.pool.contains(addr)
    }

    /*
    pub fn clear_main(&mut self) {
        self.main_pool.clear();
    }
     */

}

