use std::borrow::<PERSON><PERSON>;
use std::sync::Arc;
use alloy::primitives::Address;
use dashmap::DashMap;

use crate::vira::pool::{DexPool, POOL};
use crate::vira::token::TokenManager;
use super::{PoolIndex, mev::Mev};

/// 显示工具
/// 提供格式化和展示功能
pub struct DisplayUtils;

impl DisplayUtils {

    /// 打印 MEV 路径
    pub fn display_mevs(
        mevs: &Vec<Mev>,
        all_pools: &Arc<DashMap<Address, POOL>>
    ) {
        for mev in mevs {
            let mut str = vec![];
            let len = mev.pools.len();
            for (index, pool) in mev.pools.iter().enumerate() {
                let p = all_pools.get(&pool.addr).unwrap();
                let symbol = &p.data().tokens[pool.in_index].symbol;
                str.push(symbol.to_string());
                if index == len - 1 {
                    let out_symbol = &p.data().tokens[pool.out_index].symbol;
                    str.push(out_symbol.to_string());
                }
            }
            println!("{},  weight: $ {}", str.join(" -> "), mev.weight);
        }
    }

    /// 打印池子索引链
    pub fn display_pool_indexs<T>(
        pool_indexs: &[T],
        tokens: &TokenManager,
        str: Option<String>
    )
    where
        T: Borrow<PoolIndex> + std::fmt::Debug,
    {
        for p in pool_indexs {
            let in_token = tokens.get(&p.borrow().in_token);
            let out_token = tokens.get(&p.borrow().out_token);
            print!("{}->{} ", in_token.symbol, out_token.symbol);
        }
        print!("{:?}", str);
        println!("");
    }
}