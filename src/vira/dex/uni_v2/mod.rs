//uniswap V2 api
pub mod contract;
pub mod pool;
pub mod factory;

use crate::{connector::Connector, vira::pool::{<PERSON><PERSON>ool, POOL}};
use alloy::sol;
use serde::{Deserialize, Serialize};
use super::{<PERSON><PERSON>out<PERSON>, factory::FactoryConfig, DEX};


sol! {
    /// Interface of the UniswapV2Pair
    #[derive(Debug, PartialEq, Eq)]
    #[sol(rpc)]
    contract IUniswapV2Pair {
        event Sync(uint112 reserve0, uint112 reserve1);
        function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
        function token0() external view returns (address);
        function token1() external view returns (address);
        function swap(uint256 amount0Out, uint256 amount1Out, address to, bytes calldata data);
    }
}


#[derive(Default, Debug, Clone, Serialize, Deserialize)]
pub struct UniV2 {
    config : FactoryConfig,
}


impl DexRouter for UniV2 {
    fn new(config : FactoryConfig) -> DEX {
        DEX::UniV2(UniV2 {
            config : config
        })
    }

    fn config(&self) -> &FactoryConfig {
        &self.config
    }

    fn config_mut(&mut self) -> &mut FactoryConfig {
        &mut self.config
    }

}

#[cfg(test)]
mod tests {

}