{"abi": [{"inputs": [{"internalType": "address[]", "name": "addrs", "type": "address[]"}], "stateMutability": "nonpayable", "type": "constructor"}], "bytecode": {"object": "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", "sourceMap": "57:1562:2:-:0;;;224:1393;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;270:25;312:5;:12;-1:-1:-1;;;;;298:27:2;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;57:1562;;;;;;;;;;;;;;;;;;;;;;;;;298:27;;;;;;;;;;;;;;;;;270:55;;340:9;335:745;355:5;:12;351:1;:16;335:745;;;388:12;403:5;409:1;403:8;;;;;;;;:::i;:::-;;;;;;;388:23;;426:12;440:17;461:4;-1:-1:-1;;;;;461:15:2;477:40;;;;;;-1:-1:-1;;477:40:2;;;;;;;;;;;-1:-1:-1;;;477:40:2;;;;;-1:-1:-1;;;;;477:40:2;;;;;;461:57;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;461:57:2;;;;;;;;;-1:-1:-1;461:57:2;;;;;;;;;;;;;;425:93;;;;535:7;532:538;;;561:16;57:1562;;;;;;;;;;;;;;;;;;;;;;;;;561:16;599:4;:11;614:2;599:17;595:428;;723:36;;;734:4;723:36;;;;;;;;:::i;:::-;-1:-1:-1;;;;;696:63:2;;;709:10;;;696:63;;;;595:428;;;788:4;:11;803:2;788:17;784:239;;934:44;;;945:4;934:44;;;;;;;;:::i;:::-;885:93;;910:20;;;885:93;-1:-1:-1;;;;;885:93:2;;;898:10;;;885:93;;;;784:239;1054:1;1040:8;1049:1;1040:11;;;;;;;;:::i;:::-;;;;;;:15;;;;543:527;532:538;374:706;;;369:3;;;;;:::i;:::-;;;;335:745;;;;1282:28;1324:8;1313:20;;;;;;;;:::i;:::-;;;;;;;;;;;;;1282:51;;1541:4;1524:15;1520:26;1590:9;1581:7;1577:23;1566:9;1559:42;565:180:24;-1:-1:-1;;;610:1:24;603:88;710:4;707:1;700:15;734:4;731:1;724:15;751:281;-1:-1:-1;;549:2:24;529:14;;525:28;826:6;822:40;964:6;952:10;949:22;-1:-1:-1;;;;;916:10:24;913:34;910:62;907:88;;;975:18;;:::i;:::-;1011:2;1004:22;-1:-1:-1;;751:281:24:o;1038:129::-;1072:6;1099:20;40:6;73:2;67:9;57:19;;7:75;;1099:20;1089:30;;1128:33;1156:4;1148:6;1128:33;:::i;:::-;1038:129;;;:::o;1173:311::-;1250:4;-1:-1:-1;;;;;1332:6:24;1329:30;1326:56;;;1362:18;;:::i;:::-;-1:-1:-1;1412:4:24;1400:17;;;1462:15;;1173:311::o;1745:96::-;1782:7;-1:-1:-1;;;;;1679:54:24;;1811:24;1800:35;1745:96;-1:-1:-1;;1745:96:24:o;1847:122::-;1920:24;1938:5;1920:24;:::i;:::-;1913:5;1910:35;1900:63;;1959:1;1956;1949:12;1900:63;1847:122;:::o;1975:143::-;2032:5;2063:6;2057:13;2048:22;;2079:33;2106:5;2079:33;:::i;2141:732::-;2248:5;2273:81;2289:64;2346:6;2289:64;:::i;:::-;2273:81;:::i;:::-;2389:21;;;2264:90;-1:-1:-1;2437:4:24;2426:16;;;;2478:17;;2466:30;;2508:15;;;2505:122;;;2538:79;197:1;194;187:12;2538:79;2653:6;2636:231;2670:6;2665:3;2662:15;2636:231;;;2745:3;2774:48;2818:3;2806:10;2774:48;:::i;:::-;2762:61;;-1:-1:-1;2852:4:24;2843:14;;;;2687;2636:231;;;2640:21;2254:619;;2141:732;;;;;:::o;2896:385::-;2978:5;3027:3;3020:4;3012:6;3008:17;3004:27;2994:122;;3035:79;197:1;194;187:12;3035:79;3145:6;3139:13;3170:105;3271:3;3263:6;3256:4;3248:6;3244:17;3170:105;:::i;:::-;3161:114;2896:385;-1:-1:-1;;;;2896:385:24:o;3287:554::-;3382:6;3431:2;3419:9;3410:7;3406:23;3402:32;3399:119;;;3437:79;197:1;194;187:12;3437:79;3567:9;3557:24;-1:-1:-1;;;;;3600:6:24;3597:30;3594:117;;;3630:79;197:1;194;187:12;3630:79;3735:89;3816:7;3807:6;3796:9;3792:22;3735:89;:::i;3847:180::-;-1:-1:-1;;;3892:1:24;3885:88;3992:4;3989:1;3982:15;4016:4;4013:1;4006:15;4290:246;4371:1;4381:113;4395:6;4392:1;4389:13;4381:113;;;4480:1;4475:3;4471:11;4465:18;4452:11;;;4445:39;4417:2;4410:10;4381:113;;;-1:-1:-1;;4528:1:24;4510:16;;4503:27;4290:246::o;4542:386::-;4646:3;4674:38;4706:5;4084:6;4118:5;4112:12;4102:22;4033:98;-1:-1:-1;;4033:98:24;4674:38;4825:65;4883:6;4878:3;4871:4;4864:5;4860:16;4825:65;:::i;:::-;4906:16;;;;;4542:386;-1:-1:-1;;4542:386:24:o;4934:271::-;5064:3;5086:93;5175:3;5166:6;5086:93;:::i;:::-;5079:100;4934:271;-1:-1:-1;;;4934:271:24:o;5331:122::-;-1:-1:-1;;;;;5277:42:24;;5404:24;5211:114;5459:143;5516:5;5547:6;5541:13;5532:22;;5563:33;5590:5;5563:33;:::i;5608:507::-;5687:6;5695;5744:2;5732:9;5723:7;5719:23;5715:32;5712:119;;;5750:79;197:1;194;187:12;5750:79;5870:1;5895:64;5951:7;5931:9;5895:64;:::i;:::-;5885:74;;5841:128;6008:2;6034:64;6090:7;6081:6;6070:9;6066:22;6034:64;:::i;:::-;6024:74;;5979:129;5608:507;;;;;:::o;6220:120::-;6197:10;6186:22;;6292:23;6121:93;6346:141;6402:5;6433:6;6427:13;6418:22;;6449:32;6475:5;6449:32;:::i;6493:661::-;6580:6;6588;6596;6645:2;6633:9;6624:7;6620:23;6616:32;6613:119;;;6651:79;197:1;194;187:12;6651:79;6771:1;6796:64;6852:7;6832:9;6796:64;:::i;:::-;6786:74;;6742:128;6909:2;6935:64;6991:7;6982:6;6971:9;6967:22;6935:64;:::i;:::-;6925:74;;6880:129;7048:2;7074:63;7129:7;7120:6;7109:9;7105:22;7074:63;:::i;:::-;7064:73;;7019:128;6493:661;;;;;:::o;7160:180::-;-1:-1:-1;;;7205:1:24;7198:88;7305:4;7302:1;7295:15;7329:4;7326:1;7319:15;7429:233;7468:3;-1:-1:-1;;7530:5:24;7527:77;7524:103;;7607:18;;:::i;:::-;-1:-1:-1;7654:1:24;7643:13;;7429:233::o;8188:108::-;-1:-1:-1;;;;;5277:42:24;;8265:24;8260:3;8253:37;8188:108;;:::o;8302:105::-;6197:10;6186:22;;8377:23;6121:93;8527:689;8662:4;8653:14;;8746:5;8736:23;8772:63;8824:3;8806:12;8772:63;:::i;:::-;8677:168;8931:4;8924:5;8920:16;8914:23;8950:63;9007:4;9002:3;8998:14;8984:12;8950:63;:::i;:::-;8855:168;9119:4;9112:5;9108:16;9102:23;9138:61;9193:4;9188:3;9184:14;9170:12;9138:61;:::i;:::-;9033:176;8631:585;8527:689;;:::o;9222:275::-;9339:10;9360:94;9450:3;9442:6;9360:94;:::i;:::-;-1:-1:-1;;9486:4:24;9477:14;;9222:275::o;9764:924::-;9931:3;9960:78;10032:5;4084:6;4118:5;4112:12;4102:22;4033:98;-1:-1:-1;;4033:98:24;9960:78;7957:19;;;8009:4;8000:14;;;;8161;;;10322:1;10307:356;10332:6;10329:1;10326:13;10307:356;;;10408:6;10402:13;10435:111;10542:3;10527:13;10435:111;:::i;:::-;10428:118;-1:-1:-1;9629:4:24;9620:14;;10559:94;-1:-1:-1;;10354:1:24;10347:9;10307:356;;;-1:-1:-1;10679:3:24;;9764:924;-1:-1:-1;;;;;9764:924:24:o;10694:469::-;10923:2;10936:47;;;10908:18;;11000:156;10908:18;11142:6;11000:156;:::i", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052600080fdfea26469706673582212202f6b9ef46ca27abce8aa930418045477c870716b8309a74fbf4df330d3dd870c64736f6c63430008150033", "sourceMap": "57:1562:2:-:0;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.21+commit.d9974bed\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"addrs\",\"type\":\"address[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"forge/contracts/GetUniswapV2PoolReservesBatchRequest.sol\":\"GetUniswapV2PoolReservesBatchRequest\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"details\":{\"constantOptimizer\":true,\"cse\":true,\"deduplicate\":true,\"inliner\":true,\"jumpdestRemover\":true,\"orderLiterals\":true,\"peephole\":true,\"yul\":false},\"runs\":200},\"remappings\":[\":@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/\",\":ds-test/=forge/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=forge/lib/forge-std/src/\",\":openzeppelin-contracts/=forge/lib/openzeppelin-contracts/\"]},\"sources\":{\"forge/contracts/GetUniswapV2PoolReservesBatchRequest.sol\":{\"keccak256\":\"0xd9a64f669843e4daafbb59dea9639893140d8ca2f2accf052cb716544a558dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://34b5d11177d3a1f9b5bbd53256dd80082021f4c00c1846d38958be896b86b213\",\"dweb:/ipfs/QmciWb6jCMevnrpmbQwpfESA5mDruJMLEdMhc9z9q8A8Xh\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.21+commit.d9974bed"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address[]", "name": "addrs", "type": "address[]"}], "stateMutability": "nonpayable", "type": "constructor"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/", "ds-test/=forge/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=forge/lib/forge-std/src/", "openzeppelin-contracts/=forge/lib/openzeppelin-contracts/"], "optimizer": {"runs": 200, "details": {"peephole": true, "inliner": true, "jumpdestRemover": true, "orderLiterals": true, "deduplicate": true, "cse": true, "constantOptimizer": true, "yul": false}}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"forge/contracts/GetUniswapV2PoolReservesBatchRequest.sol": "GetUniswapV2PoolReservesBatchRequest"}, "libraries": {}}, "sources": {"forge/contracts/GetUniswapV2PoolReservesBatchRequest.sol": {"keccak256": "0xd9a64f669843e4daafbb59dea9639893140d8ca2f2accf052cb716544a558dcc", "urls": ["bzz-raw://34b5d11177d3a1f9b5bbd53256dd80082021f4c00c1846d38958be896b86b213", "dweb:/ipfs/QmciWb6jCMevnrpmbQwpfESA5mDruJMLEdMhc9z9q8A8Xh"], "license": "MIT"}}, "version": 1}, "ast": {"absolutePath": "forge/contracts/GetUniswapV2PoolReservesBatchRequest.sol", "id": 690, "exportedSymbols": {"GetUniswapV2PoolReservesBatchRequest": [689]}, "nodeType": "SourceUnit", "src": "31:1588:2", "nodes": [{"id": 559, "nodeType": "PragmaDirective", "src": "31:23:2", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 689, "nodeType": "ContractDefinition", "src": "57:1562:2", "nodes": [{"id": 566, "nodeType": "StructDefinition", "src": "109:109:2", "nodes": [], "canonicalName": "GetUniswapV2PoolReservesBatchRequest.Reserve", "members": [{"constant": false, "id": 561, "mutability": "mutable", "name": "reserve0", "nameLocation": "142:8:2", "nodeType": "VariableDeclaration", "scope": 566, "src": "134:16:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}, "typeName": {"id": 560, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "134:7:2", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, "visibility": "internal"}, {"constant": false, "id": 563, "mutability": "mutable", "name": "reserve1", "nameLocation": "168:8:2", "nodeType": "VariableDeclaration", "scope": 566, "src": "160:16:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}, "typeName": {"id": 562, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "160:7:2", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, "visibility": "internal"}, {"constant": false, "id": 565, "mutability": "mutable", "name": "blockTimestampLast", "nameLocation": "193:18:2", "nodeType": "VariableDeclaration", "scope": 566, "src": "186:25:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 564, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "186:6:2", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}], "name": "Reserve", "nameLocation": "116:7:2", "scope": 689, "visibility": "public"}, {"id": 688, "nodeType": "FunctionDefinition", "src": "224:1393:2", "nodes": [], "body": {"id": 687, "nodeType": "Block", "src": "260:1357:2", "nodes": [], "statements": [{"assignments": [576], "declarations": [{"constant": false, "id": 576, "mutability": "mutable", "name": "reserves", "nameLocation": "287:8:2", "nodeType": "VariableDeclaration", "scope": 687, "src": "270:25:2", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Reserve_$566_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve[]"}, "typeName": {"baseType": {"id": 574, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 573, "name": "Reserve", "nameLocations": ["270:7:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 566, "src": "270:7:2"}, "referencedDeclaration": 566, "src": "270:7:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_storage_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve"}}, "id": 575, "nodeType": "ArrayTypeName", "src": "270:9:2", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Reserve_$566_storage_$dyn_storage_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve[]"}}, "visibility": "internal"}], "id": 584, "initialValue": {"arguments": [{"expression": {"id": 581, "name": "addrs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 569, "src": "312:5:2", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 582, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "318:6:2", "memberName": "length", "nodeType": "MemberAccess", "src": "312:12:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 580, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "298:13:2", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_struct$_Reserve_$566_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (struct GetUniswapV2PoolReservesBatchRequest.Reserve memory[] memory)"}, "typeName": {"baseType": {"id": 578, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 577, "name": "Reserve", "nameLocations": ["302:7:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 566, "src": "302:7:2"}, "referencedDeclaration": 566, "src": "302:7:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_storage_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve"}}, "id": 579, "nodeType": "ArrayTypeName", "src": "302:9:2", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Reserve_$566_storage_$dyn_storage_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve[]"}}}, "id": 583, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "298:27:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Reserve_$566_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "270:55:2"}, {"body": {"id": 677, "nodeType": "Block", "src": "374:706:2", "statements": [{"assignments": [596], "declarations": [{"constant": false, "id": 596, "mutability": "mutable", "name": "addr", "nameLocation": "396:4:2", "nodeType": "VariableDeclaration", "scope": 677, "src": "388:12:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 595, "name": "address", "nodeType": "ElementaryTypeName", "src": "388:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 600, "initialValue": {"baseExpression": {"id": 597, "name": "addrs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 569, "src": "403:5:2", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 599, "indexExpression": {"id": 598, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 586, "src": "409:1:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "403:8:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "388:23:2"}, {"assignments": [602, 604], "declarations": [{"constant": false, "id": 602, "mutability": "mutable", "name": "success", "nameLocation": "431:7:2", "nodeType": "VariableDeclaration", "scope": 677, "src": "426:12:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 601, "name": "bool", "nodeType": "ElementaryTypeName", "src": "426:4:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 604, "mutability": "mutable", "name": "data", "nameLocation": "453:4:2", "nodeType": "VariableDeclaration", "scope": 677, "src": "440:17:2", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 603, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "440:5:2", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 612, "initialValue": {"arguments": [{"arguments": [{"hexValue": "67657452657365727665732829", "id": 609, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "501:15:2", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0902f1ac5dbaeedd3217f11b3cbaf929216c9c5abc2d69da89d54964bead575d", "typeString": "literal_string \"getReserves()\""}, "value": "getReserves()"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_0902f1ac5dbaeedd3217f11b3cbaf929216c9c5abc2d69da89d54964bead575d", "typeString": "literal_string \"getReserves()\""}], "expression": {"id": 607, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "477:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 608, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "481:19:2", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "477:23:2", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 610, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "477:40:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 605, "name": "addr", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 596, "src": "461:4:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 606, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "466:10:2", "memberName": "staticcall", "nodeType": "MemberAccess", "src": "461:15:2", "typeDescriptions": {"typeIdentifier": "t_function_barestaticcall_view$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) view returns (bool,bytes memory)"}}, "id": 611, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "461:57:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "425:93:2"}, {"condition": {"id": 613, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 602, "src": "535:7:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 676, "nodeType": "IfStatement", "src": "532:538:2", "trueBody": {"id": 675, "nodeType": "Block", "src": "543:527:2", "statements": [{"assignments": [616], "declarations": [{"constant": false, "id": 616, "mutability": "mutable", "name": "r", "nameLocation": "576:1:2", "nodeType": "VariableDeclaration", "scope": 675, "src": "561:16:2", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve"}, "typeName": {"id": 615, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 614, "name": "Reserve", "nameLocations": ["561:7:2"], "nodeType": "IdentifierPath", "referencedDeclaration": 566, "src": "561:7:2"}, "referencedDeclaration": 566, "src": "561:7:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_storage_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve"}}, "visibility": "internal"}], "id": 617, "nodeType": "VariableDeclarationStatement", "src": "561:16:2"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 621, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 618, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 604, "src": "599:4:2", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 619, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "604:6:2", "memberName": "length", "nodeType": "MemberAccess", "src": "599:11:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3634", "id": 620, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "614:2:2", "typeDescriptions": {"typeIdentifier": "t_rational_64_by_1", "typeString": "int_const 64"}, "value": "64"}, "src": "599:17:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 643, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 640, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 604, "src": "788:4:2", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 641, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "793:6:2", "memberName": "length", "nodeType": "MemberAccess", "src": "788:11:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3936", "id": 642, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "803:2:2", "typeDescriptions": {"typeIdentifier": "t_rational_96_by_1", "typeString": "int_const 96"}, "value": "96"}, "src": "788:17:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 666, "nodeType": "Block", "src": "1003:20:2", "statements": []}, "id": 667, "nodeType": "IfStatement", "src": "784:239:2", "trueBody": {"id": 665, "nodeType": "Block", "src": "807:190:2", "statements": [{"expression": {"id": 663, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"components": [{"expression": {"id": 644, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 616, "src": "886:1:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory"}}, "id": 646, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "888:8:2", "memberName": "reserve0", "nodeType": "MemberAccess", "referencedDeclaration": 561, "src": "886:10:2", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, {"expression": {"id": 647, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 616, "src": "898:1:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory"}}, "id": 648, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "900:8:2", "memberName": "reserve1", "nodeType": "MemberAccess", "referencedDeclaration": 563, "src": "898:10:2", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, {"expression": {"id": 649, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 616, "src": "910:1:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory"}}, "id": 650, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "912:18:2", "memberName": "blockTimestampLast", "nodeType": "MemberAccess", "referencedDeclaration": 565, "src": "910:20:2", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "id": 651, "isConstant": false, "isInlineArray": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "TupleExpression", "src": "885:46:2", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint112_$_t_uint112_$_t_uint32_$", "typeString": "tuple(uint112,uint112,uint32)"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 654, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 604, "src": "945:4:2", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 656, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "952:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint112_$", "typeString": "type(uint112)"}, "typeName": {"id": 655, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "952:7:2", "typeDescriptions": {}}}, {"id": 658, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "961:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint112_$", "typeString": "type(uint112)"}, "typeName": {"id": 657, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "961:7:2", "typeDescriptions": {}}}, {"id": 660, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "970:6:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint32_$", "typeString": "type(uint32)"}, "typeName": {"id": 659, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "970:6:2", "typeDescriptions": {}}}], "id": 661, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "951:26:2", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_type$_t_uint112_$_$_t_type$_t_uint112_$_$_t_type$_t_uint32_$_$", "typeString": "tuple(type(uint112),type(uint112),type(uint32))"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_tuple$_t_type$_t_uint112_$_$_t_type$_t_uint112_$_$_t_type$_t_uint32_$_$", "typeString": "tuple(type(uint112),type(uint112),type(uint32))"}], "expression": {"id": 652, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "934:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 653, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "938:6:2", "memberName": "decode", "nodeType": "MemberAccess", "src": "934:10:2", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 662, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "934:44:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint112_$_t_uint112_$_t_uint32_$", "typeString": "tuple(uint112,uint112,uint32)"}}, "src": "885:93:2", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 664, "nodeType": "ExpressionStatement", "src": "885:93:2"}]}}, "id": 668, "nodeType": "IfStatement", "src": "595:428:2", "trueBody": {"id": 639, "nodeType": "Block", "src": "618:160:2", "statements": [{"expression": {"id": 637, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"components": [{"expression": {"id": 622, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 616, "src": "697:1:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory"}}, "id": 624, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "699:8:2", "memberName": "reserve0", "nodeType": "MemberAccess", "referencedDeclaration": 561, "src": "697:10:2", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, {"expression": {"id": 625, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 616, "src": "709:1:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory"}}, "id": 626, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "711:8:2", "memberName": "reserve1", "nodeType": "MemberAccess", "referencedDeclaration": 563, "src": "709:10:2", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}], "id": 627, "isConstant": false, "isInlineArray": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "TupleExpression", "src": "696:24:2", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint112_$_t_uint112_$", "typeString": "tuple(uint112,uint112)"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 630, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 604, "src": "734:4:2", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 632, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "741:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint112_$", "typeString": "type(uint112)"}, "typeName": {"id": 631, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "741:7:2", "typeDescriptions": {}}}, {"id": 634, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "750:7:2", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint112_$", "typeString": "type(uint112)"}, "typeName": {"id": 633, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "750:7:2", "typeDescriptions": {}}}], "id": 635, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "740:18:2", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_type$_t_uint112_$_$_t_type$_t_uint112_$_$", "typeString": "tuple(type(uint112),type(uint112))"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_tuple$_t_type$_t_uint112_$_$_t_type$_t_uint112_$_$", "typeString": "tuple(type(uint112),type(uint112))"}], "expression": {"id": 628, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "723:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 629, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "727:6:2", "memberName": "decode", "nodeType": "MemberAccess", "src": "723:10:2", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 636, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "723:36:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint112_$_t_uint112_$", "typeString": "tuple(uint112,uint112)"}}, "src": "696:63:2", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 638, "nodeType": "ExpressionStatement", "src": "696:63:2"}]}}, {"expression": {"id": 673, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 669, "name": "reserves", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 576, "src": "1040:8:2", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Reserve_$566_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory[] memory"}}, "id": 671, "indexExpression": {"id": 670, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 586, "src": "1049:1:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1040:11:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 672, "name": "r", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 616, "src": "1054:1:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory"}}, "src": "1040:15:2", "typeDescriptions": {"typeIdentifier": "t_struct$_Reserve_$566_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory"}}, "id": 674, "nodeType": "ExpressionStatement", "src": "1040:15:2"}]}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 591, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 588, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 586, "src": "351:1:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 589, "name": "addrs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 569, "src": "355:5:2", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 590, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "361:6:2", "memberName": "length", "nodeType": "MemberAccess", "src": "355:12:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "351:16:2", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 678, "initializationExpression": {"assignments": [586], "declarations": [{"constant": false, "id": 586, "mutability": "mutable", "name": "i", "nameLocation": "348:1:2", "nodeType": "VariableDeclaration", "scope": 678, "src": "340:9:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 585, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "340:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 587, "nodeType": "VariableDeclarationStatement", "src": "340:9:2"}, "loopExpression": {"expression": {"id": 593, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "369:3:2", "subExpression": {"id": 592, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 586, "src": "369:1:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 594, "nodeType": "ExpressionStatement", "src": "369:3:2"}, "nodeType": "ForStatement", "src": "335:745:2"}, {"assignments": [680], "declarations": [{"constant": false, "id": 680, "mutability": "mutable", "name": "_abiEncodedData", "nameLocation": "1295:15:2", "nodeType": "VariableDeclaration", "scope": 687, "src": "1282:28:2", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 679, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1282:5:2", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 685, "initialValue": {"arguments": [{"id": 683, "name": "reserves", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 576, "src": "1324:8:2", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Reserve_$566_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_array$_t_struct$_Reserve_$566_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolReservesBatchRequest.Reserve memory[] memory"}], "expression": {"id": 681, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "1313:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 682, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1317:6:2", "memberName": "encode", "nodeType": "MemberAccess", "src": "1313:10:2", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 684, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1313:20:2", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "1282:51:2"}, {"AST": {"nativeSrc": "1353:258:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1353:258:2", "statements": [{"nativeSrc": "1503:43:2", "nodeType": "YulVariableDeclaration", "src": "1503:43:2", "value": {"arguments": [{"name": "_abiEncodedData", "nativeSrc": "1524:15:2", "nodeType": "YulIdentifier", "src": "1524:15:2"}, {"kind": "number", "nativeSrc": "1541:4:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1541:4:2", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "1520:3:2", "nodeType": "YulIdentifier", "src": "1520:3:2"}, "nativeSrc": "1520:26:2", "nodeType": "YulFunctionCall", "src": "1520:26:2"}, "variables": [{"name": "dataStart", "nativeSrc": "1507:9:2", "nodeType": "YulTypedName", "src": "1507:9:2", "type": ""}]}, {"expression": {"arguments": [{"name": "dataStart", "nativeSrc": "1566:9:2", "nodeType": "YulIdentifier", "src": "1566:9:2"}, {"arguments": [{"arguments": [], "functionName": {"name": "msize", "nativeSrc": "1581:5:2", "nodeType": "YulIdentifier", "src": "1581:5:2"}, "nativeSrc": "1581:7:2", "nodeType": "YulFunctionCall", "src": "1581:7:2"}, {"name": "dataStart", "nativeSrc": "1590:9:2", "nodeType": "YulIdentifier", "src": "1590:9:2"}], "functionName": {"name": "sub", "nativeSrc": "1577:3:2", "nodeType": "YulIdentifier", "src": "1577:3:2"}, "nativeSrc": "1577:23:2", "nodeType": "YulFunctionCall", "src": "1577:23:2"}], "functionName": {"name": "return", "nativeSrc": "1559:6:2", "nodeType": "YulIdentifier", "src": "1559:6:2"}, "nativeSrc": "1559:42:2", "nodeType": "YulFunctionCall", "src": "1559:42:2"}, "nativeSrc": "1559:42:2", "nodeType": "YulExpressionStatement", "src": "1559:42:2"}]}, "evmVersion": "paris", "externalReferences": [{"declaration": 680, "isOffset": false, "isSlot": false, "src": "1524:15:2", "valueSize": 1}], "id": 686, "nodeType": "InlineAssembly", "src": "1344:267:2"}]}, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 570, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 569, "mutability": "mutable", "name": "addrs", "nameLocation": "253:5:2", "nodeType": "VariableDeclaration", "scope": 688, "src": "236:22:2", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 567, "name": "address", "nodeType": "ElementaryTypeName", "src": "236:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 568, "nodeType": "ArrayTypeName", "src": "236:9:2", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "235:24:2"}, "returnParameters": {"id": 571, "nodeType": "ParameterList", "parameters": [], "src": "260:0:2"}, "scope": 689, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "abstract": false, "baseContracts": [], "canonicalName": "GetUniswapV2PoolReservesBatchRequest", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [689], "name": "GetUniswapV2PoolReservesBatchRequest", "nameLocation": "66:36:2", "scope": 690, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 2}