{"abi": [{"inputs": [{"internalType": "address[]", "name": "addrs", "type": "address[]"}], "stateMutability": "nonpayable", "type": "constructor"}], "bytecode": {"object": "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", "sourceMap": "1235:5659:1:-:0;;;1283:2264;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1329:33;1393:5;:12;-1:-1:-1;;;;;1365:41:1;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;1235:5659;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1365:41;;;;;;;;;;;;;;;;;1329:77;;1418:12;1432:17;1453:5;1459:1;1453:8;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;1453:19:1;1473:35;;;;;;-1:-1:-1;;1473:35:1;;;;;;;;;;;-1:-1:-1;;;1473:35:1;;;;;-1:-1:-1;;;;;1473:35:1;;;;;;1453:56;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1453:56:1;;;;;;;;;-1:-1:-1;1453:56:1;;;;;;;;;;;;;;1417:92;;;;1519:13;1535:7;:35;;;;-1:-1:-1;1546:24:1;;;1557:4;1546:24;;;;;;;;:::i;:::-;1519:51;;1586:9;1581:1435;1601:5;:12;1597:1;:16;1581:1435;;;1634:12;1649:5;1655:1;1649:8;;;;;;;;:::i;:::-;;;;;;;1634:23;-1:-1:-1;1672:9:1;;;;;;;1779:21;1634:23;1779:15;:21::i;:::-;1671:129;;;;;;;;;;;;;;1815:30;1848:426;;;;;;;;;-1:-1:-1;;;;;1848:426:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1848:426:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2323:40:1;;;;;-1:-1:-1;;2323:40:1;;;;;;;;;;;-1:-1:-1;;;2323:40:1;;;;;-1:-1:-1;;;;;2323:40:1;;;;;;2307:57;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;2307:57:1;;;;;;;;;-1:-1:-1;2307:57:1;;;;;;;;;;;;;-1:-1:-1;2289:75:1;;-1:-1:-1;2289:75:1;-1:-1:-1;2432:528:1;;;;2465:4;:11;2480:2;2465:17;2461:485;;2577:36;;;2588:4;2577:36;;;;;;;;:::i;:::-;-1:-1:-1;;;;;2562:51:1;;;2569:4;;;2562:51;;2563:4;;;2562:51;2461:485;;;2642:4;:11;2657:2;2642:17;2638:308;;2768:44;;;2779:4;2768:44;;;;;;;;:::i;:::-;2739:73;;2752:12;;;2739:73;-1:-1:-1;;;;;2739:73:1;;;2746:4;;;2739:73;;2740:4;;;2739:73;2638:308;;;2867:4;2859:5;;;:12;2638:308;3004:1;2996:2;2999:1;2996:5;;;;;;;;:::i;:::-;;;;;;:9;;;;1620:1396;;;;;;;;;1615:3;;;;;:::i;:::-;;;;1581:1435;;;;3218:28;3260:2;3249:14;;;;;;;;:::i;:::-;;;;;;;;;;;;;3218:45;;3471:4;3454:15;3450:26;3520:9;3511:7;3507:23;3496:9;3489:42;3902:1624;3988:8;3998:14;4014;4030:16;4048;4066:8;4076;4110:4;-1:-1:-1;;;;;4104:18:1;;:20;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4104:20:1;;;;;;;;;;;;:::i;:::-;;;4100:133;;4218:4;4212:10;;4100:133;;;4173:7;-1:-1:-1;4100:133:1;4253:4;-1:-1:-1;;;;;4247:18:1;;:20;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4247:20:1;;;;;;;;;;;;:::i;:::-;;;4243:133;;4361:4;4355:10;;4243:133;;;4316:7;-1:-1:-1;4243:133:1;4704:12;4718:17;4739:6;-1:-1:-1;;;;;4739:17:1;4757:35;;;;;;-1:-1:-1;;4757:35:1;;;;;;;;;;;-1:-1:-1;;;4757:35:1;;;;;-1:-1:-1;;;;;4757:35:1;;;;;;4739:54;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4739:54:1;;;;;;;;;-1:-1:-1;4739:54:1;;;;;;;;;;;;;;4703:90;;;;4807:7;4803:181;;;4913:19;4927:4;4913:13;:19::i;:::-;4908:24;;4803:181;;;4969:4;4963:10;;4803:181;5020:6;-1:-1:-1;;;;;5020:17:1;5038:35;;;;;;-1:-1:-1;;5038:35:1;;;;;;;;;;;-1:-1:-1;;;5038:35:1;;;;;-1:-1:-1;;;;;5038:35:1;;;;;;5020:54;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5020:54:1;;;;;;;;;-1:-1:-1;5020:54:1;;;;;;;;;;;;;-1:-1:-1;5002:72:1;;-1:-1:-1;5002:72:1;-1:-1:-1;5084:168:1;;;;5181:19;5195:4;5181:13;:19::i;:::-;5176:24;;5084:168;;;5237:4;5231:10;;5084:168;5273:6;-1:-1:-1;;;;;5266:23:1;;:25;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5266:25:1;;;;;;;;;;;;:::i;:::-;;;5262:124;;5371:4;5365:10;;5262:124;;;5330:3;-1:-1:-1;5262:124:1;5407:6;-1:-1:-1;;;;;5400:23:1;;:25;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5400:25:1;;;;;;;;;;;;:::i;:::-;;;5396:124;;5505:4;5499:10;;5396:124;;;5464:3;-1:-1:-1;5396:124:1;4090:1436;;3902:1624;;;;;;;;;:::o;5532:590::-;5599:13;5643:2;5627:6;:13;:18;5624:492;;;5742:7;5767:68;5777:2;5773:1;:6;;;:24;;;;;5783:6;5790:1;5783:9;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;;5783:9:1;:14;;5773:24;5767:68;;;5817:3;;;;:::i;:::-;;;;5767:68;;;5848:23;5884:1;5874:12;;-1:-1:-1;;;;;5874:12:1;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;5874:12:1;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;5874:12:1;;5848:38;;5909:1;5905:5;;5900:101;5916:2;5912:1;:6;;;:24;;;;;5922:6;5929:1;5922:9;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;;5922:9:1;:14;;5912:24;5900:101;;;5977:6;5984:1;5977:9;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;;5977:9:1;5961:10;:13;;;:10;:13;;;;;;;:::i;:::-;;;;:25;-1:-1:-1;;;;;5961:25:1;;;;;;;;-1:-1:-1;5938:3:1;;;;:::i;:::-;;;;5900:101;;;6028:10;5532:590;-1:-1:-1;;;5532:590:1:o;5624:492::-;6077:28;;;6088:6;6077:28;;;;;;;;:::i;:::-;6070:35;5532:590;-1:-1:-1;;5532:590:1:o;5624:492::-;5532:590;;;:::o;565:180:23:-;-1:-1:-1;;;610:1:23;603:88;710:4;707:1;700:15;734:4;731:1;724:15;751:281;-1:-1:-1;;549:2:23;529:14;;525:28;826:6;822:40;964:6;952:10;949:22;-1:-1:-1;;;;;916:10:23;913:34;910:62;907:88;;;975:18;;:::i;:::-;1011:2;1004:22;-1:-1:-1;;751:281:23:o;1038:129::-;1072:6;1099:20;40:6;73:2;67:9;57:19;;7:75;;1099:20;1089:30;;1128:33;1156:4;1148:6;1128:33;:::i;1173:311::-;1250:4;-1:-1:-1;;;;;1332:6:23;1329:30;1326:56;;;1362:18;;:::i;:::-;-1:-1:-1;1412:4:23;1400:17;;;1462:15;;1173:311::o;1745:96::-;1782:7;-1:-1:-1;;;;;1679:54:23;;1811:24;1613:126;1847:122;1920:24;1938:5;1920:24;:::i;:::-;1913:5;1910:35;1900:63;;1959:1;1956;1949:12;1900:63;1847:122;:::o;1975:143::-;2032:5;2063:6;2057:13;2048:22;;2079:33;2106:5;2079:33;:::i;2141:732::-;2248:5;2273:81;2289:64;2346:6;2289:64;:::i;:::-;2273:81;:::i;:::-;2389:21;;;2264:90;-1:-1:-1;2437:4:23;2426:16;;;;2478:17;;2466:30;;2508:15;;;2505:122;;;2538:79;197:1;194;187:12;2538:79;2653:6;2636:231;2670:6;2665:3;2662:15;2636:231;;;2745:3;2774:48;2818:3;2806:10;2774:48;:::i;:::-;2762:61;;-1:-1:-1;2852:4:23;2843:14;;;;2687;2636:231;;;2640:21;2254:619;;2141:732;;;;;:::o;2896:385::-;2978:5;3027:3;3020:4;3012:6;3008:17;3004:27;2994:122;;3035:79;197:1;194;187:12;3035:79;3145:6;3139:13;3170:105;3271:3;3263:6;3256:4;3248:6;3244:17;3170:105;:::i;:::-;3161:114;2896:385;-1:-1:-1;;;;2896:385:23:o;3287:554::-;3382:6;3431:2;3419:9;3410:7;3406:23;3402:32;3399:119;;;3437:79;197:1;194;187:12;3437:79;3567:9;3557:24;-1:-1:-1;;;;;3600:6:23;3597:30;3594:117;;;3630:79;197:1;194;187:12;3630:79;3735:89;3816:7;3807:6;3796:9;3792:22;3735:89;:::i;3847:180::-;-1:-1:-1;;;3892:1:23;3885:88;3992:4;3989:1;3982:15;4016:4;4013:1;4006:15;4290:246;4371:1;4381:113;4395:6;4392:1;4389:13;4381:113;;;4480:1;4475:3;4471:11;4465:18;4452:11;;;4445:39;4417:2;4410:10;4381:113;;;-1:-1:-1;;4528:1:23;4510:16;;4503:27;4290:246::o;4542:386::-;4646:3;4674:38;4706:5;4084:6;4118:5;4112:12;4102:22;4033:98;-1:-1:-1;;4033:98:23;4674:38;4825:65;4883:6;4878:3;4871:4;4864:5;4860:16;4825:65;:::i;:::-;4906:16;;;;;4542:386;-1:-1:-1;;4542:386:23:o;4934:271::-;5064:3;5086:93;5175:3;5166:6;5086:93;:::i;5307:116::-;5281:13;;5274:21;5377;5211:90;5429:137;5483:5;5514:6;5508:13;5499:22;;5530:30;5554:5;5530:30;:::i;5572:345::-;5639:6;5688:2;5676:9;5667:7;5663:23;5659:32;5656:119;;;5694:79;197:1;194;187:12;5694:79;5814:1;5839:61;5892:7;5872:9;5839:61;:::i;6043:122::-;-1:-1:-1;;;;;5989:42:23;;6116:24;5923:114;6171:143;6228:5;6259:6;6253:13;6244:22;;6275:33;6302:5;6275:33;:::i;6320:507::-;6399:6;6407;6456:2;6444:9;6435:7;6431:23;6427:32;6424:119;;;6462:79;197:1;194;187:12;6462:79;6582:1;6607:64;6663:7;6643:9;6607:64;:::i;:::-;6597:74;;6553:128;6720:2;6746:64;6802:7;6793:6;6782:9;6778:22;6746:64;:::i;:::-;6736:74;;6691:129;6320:507;;;;;:::o;6932:120::-;6909:10;6898:22;;7004:23;6833:93;7058:141;7114:5;7145:6;7139:13;7130:22;;7161:32;7187:5;7161:32;:::i;7205:661::-;7292:6;7300;7308;7357:2;7345:9;7336:7;7332:23;7328:32;7325:119;;;7363:79;197:1;194;187:12;7363:79;7483:1;7508:64;7564:7;7544:9;7508:64;:::i;:::-;7498:74;;7454:128;7621:2;7647:64;7703:7;7694:6;7683:9;7679:22;7647:64;:::i;:::-;7637:74;;7592:129;7760:2;7786:63;7841:7;7832:6;7821:9;7817:22;7786:63;:::i;:::-;7776:73;;7731:128;7205:661;;;;;:::o;7872:180::-;-1:-1:-1;;;7917:1:23;7910:88;8017:4;8014:1;8007:15;8041:4;8038:1;8031:15;8141:233;8180:3;-1:-1:-1;;8242:5:23;8239:77;8236:103;;8319:18;;:::i;:::-;-1:-1:-1;8366:1:23;8355:13;;8141:233::o;8942:108::-;9019:24;9037:5;9019:24;:::i;:::-;9014:3;9007:37;8942:108;;:::o;9326:357::-;9404:3;9432:39;9465:5;4084:6;4118:5;4112:12;4102:22;4033:98;-1:-1:-1;;4033:98:23;9432:39;8697:19;;;8749:4;8740:14;;9480:68;;9557:65;9615:6;9610:3;9603:4;9596:5;9592:16;9557:65;:::i;:::-;549:2;529:14;-1:-1:-1;;525:28:23;9638:39;;;;;;-1:-1:-1;;9326:357:23:o;9689:108::-;-1:-1:-1;;;;;5989:42:23;;9766:24;5923:114;9895:102;9878:4;9867:16;;9968:22;9803:86;10003:105;6909:10;6898:22;;10078:23;6833:93;10114:99;5281:13;;5274:21;10185;5211:90;10353:2428;10488:3;10524:6;10515:16;;10606:5;10596:23;10632:63;10684:3;10666:12;10632:63;:::i;:::-;10541:164;10789:4;10782:5;10778:16;10772:23;10808:63;10865:4;10860:3;10856:14;10842:12;10808:63;:::i;:::-;10715:166;10965:4;10958:5;10954:16;10948:23;10984:63;11041:4;11036:3;11032:14;11018:12;10984:63;:::i;:::-;10891:166;11137:4;11130:5;11126:16;11120:23;11190:3;11184:4;11180:14;11173:4;11168:3;11164:14;11157:38;11216:73;11284:4;11270:12;11216:73;:::i;:::-;11208:81;;11067:233;11380:4;11373:5;11369:16;11363:23;11433:3;11427:4;11423:14;11416:4;11411:3;11407:14;11400:38;11459:73;11527:4;11513:12;11459:73;:::i;:::-;11451:81;;11310:233;11623:4;11616:5;11612:16;11606:23;11642:63;11699:4;11694:3;11690:14;11676:12;11642:63;:::i;:::-;11553:162;11795:4;11788:5;11784:16;11778:23;11814:63;11871:4;11866:3;11862:14;11848:12;11814:63;:::i;:::-;11725:162;11967:4;11960:5;11956:16;11950:23;11986:59;12039:4;12034:3;12030:14;12016:12;11986:59;:::i;:::-;11897:158;12135:6;12128:5;12124:18;12118:25;12156:61;12209:6;12204:3;12200:16;12186:12;12156:61;:::i;:::-;12065:162;12315:6;12308:5;12304:18;12298:25;12336:63;12391:6;12386:3;12382:16;12368:12;12336:63;:::i;:::-;12237:172;12493:6;12486:5;12482:18;12476:25;12514:59;12565:6;12560:3;12556:16;12542:12;12514:59;:::i;:::-;12419:164;12664:6;12657:5;12653:18;12647:25;12685:59;12736:6;12731:3;12727:16;12713:12;12685:59;:::i;:::-;-1:-1:-1;12771:4:23;10353:2428;-1:-1:-1;;;10353:2428:23:o;12787:308::-;12932:10;12967:122;13085:3;13077:6;12967:122;:::i;13396:1215::-;13591:3;13620:92;13706:5;4084:6;4118:5;4112:12;4102:22;4033:98;-1:-1:-1;;4033:98:23;13620:92;8697:19;;;8749:4;8740:14;;13721:131;;13878:3;13923:4;13915:6;13911:17;13906:3;13902:27;13953:94;14041:5;8924:4;8915:14;;8766:170;13953:94;14070:7;14101:1;14086:480;14111:6;14108:1;14105:13;14086:480;;;14182:9;14176:4;14172:20;14167:3;14160:33;14233:6;14227:13;14261:140;14396:4;14381:13;14261:140;:::i;:::-;14253:148;-1:-1:-1;13241:4:23;13232:14;;14551:4;14542:14;;;;;14414:108;-1:-1:-1;;14133:1:23;14126:9;14086:480;;;-1:-1:-1;14582:4:23;;13396:1215;-1:-1:-1;;;;;;;13396:1215:23:o;14617:525::-;14874:2;14887:47;;;14859:18;;14951:184;14859:18;15121:6;14951:184;:::i;15148:351::-;15218:6;15267:2;15255:9;15246:7;15242:23;15238:32;15235:119;;;15273:79;197:1;194;187:12;15273:79;15393:1;15418:64;15474:7;15454:9;15418:64;:::i;15505:118::-;9878:4;9867:16;;15576:22;9803:86;15629:139;15684:5;15715:6;15709:13;15700:22;;15731:31;15756:5;15731:31;:::i;15774:347::-;15842:6;15891:2;15879:9;15870:7;15866:23;15862:32;15859:119;;;15897:79;197:1;194;187:12;15897:79;16017:1;16042:62;16096:7;16076:9;16042:62;:::i;16127:167::-;9878:4;9867:16;16164:3;-1:-1:-1;;16221:15:23;;16218:41;;16239:18;;:::i;16423:308::-;16485:4;-1:-1:-1;;;;;16567:6:23;16564:30;16561:56;;;16597:18;;:::i;:::-;-1:-1:-1;;549:2:23;529:14;;525:28;16719:4;16709:15;;16423:308;-1:-1:-1;;16423:308:23:o;16737:434::-;16826:5;16851:66;16867:49;16909:6;16867:49;:::i;16851:66::-;16842:75;;16940:6;16933:5;16926:21;16978:4;16971:5;16967:16;17016:3;17007:6;17002:3;16998:16;16995:25;16992:112;;;17023:79;197:1;194;187:12;17023:79;17113:52;17158:6;17153:3;17148;17113:52;:::i;17191:355::-;17258:5;17307:3;17300:4;17292:6;17288:17;17284:27;17274:122;;17315:79;197:1;194;187:12;17315:79;17425:6;17419:13;17450:90;17536:3;17528:6;17521:4;17513:6;17509:17;17450:90;:::i;17552:524::-;17632:6;17681:2;17669:9;17660:7;17656:23;17652:32;17649:119;;;17687:79;197:1;194;187:12;17687:79;17817:9;17807:24;-1:-1:-1;;;;;17850:6:23;17847:30;17844:117;;;17880:79;197:1;194;187:12;17880:79;17985:74;18051:7;18042:6;18031:9;18027:22;17985:74;:::i", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052600080fdfea2646970667358221220716fcc86d5a5c9e00993e75fe302d5d70c17f71f5bf9e9e179f84ace6010ffdd64736f6c63430008150033", "sourceMap": "1235:5659:1:-:0;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.21+commit.d9974bed\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"addrs\",\"type\":\"address[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"}],\"devdoc\":{\"details\":\"This contract is not meant to be deployed. Instead, use a static call with the       deployment bytecode as payload.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"forge/contracts/GetUniswapV2PoolDataBatchRequest.sol\":\"GetUniswapV2PoolDataBatchRequest\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"details\":{\"constantOptimizer\":true,\"cse\":true,\"deduplicate\":true,\"inliner\":true,\"jumpdestRemover\":true,\"orderLiterals\":true,\"peephole\":true,\"yul\":false},\"runs\":200},\"remappings\":[\":@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/\",\":ds-test/=forge/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=forge/lib/forge-std/src/\",\":openzeppelin-contracts/=forge/lib/openzeppelin-contracts/\"]},\"sources\":{\"forge/contracts/GetUniswapV2PoolDataBatchRequest.sol\":{\"keccak256\":\"0xfb00f992d671cce121c909d33ce47ee4f6640a53844e9db334e75ac58509f665\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://40feafa6cf8b53cb43515fd5a0d3f5450831e02fb5c68cb8c2724ac4ce7c057f\",\"dweb:/ipfs/QmNPuoV9LLMKFF5rrXQVHuTYzSNUXvCR78tA3YX5cUqeLA\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.21+commit.d9974bed"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address[]", "name": "addrs", "type": "address[]"}], "stateMutability": "nonpayable", "type": "constructor"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/", "ds-test/=forge/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=forge/lib/forge-std/src/", "openzeppelin-contracts/=forge/lib/openzeppelin-contracts/"], "optimizer": {"runs": 200, "details": {"peephole": true, "inliner": true, "jumpdestRemover": true, "orderLiterals": true, "deduplicate": true, "cse": true, "constantOptimizer": true, "yul": false}}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"forge/contracts/GetUniswapV2PoolDataBatchRequest.sol": "GetUniswapV2PoolDataBatchRequest"}, "libraries": {}}, "sources": {"forge/contracts/GetUniswapV2PoolDataBatchRequest.sol": {"keccak256": "0xfb00f992d671cce121c909d33ce47ee4f6640a53844e9db334e75ac58509f665", "urls": ["bzz-raw://40feafa6cf8b53cb43515fd5a0d3f5450831e02fb5c68cb8c2724ac4ce7c057f", "dweb:/ipfs/QmNPuoV9LLMKFF5rrXQVHuTYzSNUXvCR78tA3YX5cUqeLA"], "license": "MIT"}}, "version": 1}, "ast": {"absolutePath": "forge/contracts/GetUniswapV2PoolDataBatchRequest.sol", "id": 558, "exportedSymbols": {"GetUniswapV2PoolDataBatchRequest": [557], "IERC20": [109], "IPair": [98]}, "nodeType": "SourceUnit", "src": "31:6864:1", "nodes": [{"id": 73, "nodeType": "PragmaDirective", "src": "31:23:1", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 98, "nodeType": "ContractDefinition", "src": "56:553:1", "nodes": [{"id": 78, "nodeType": "FunctionDefinition", "src": "78:50:1", "nodes": [], "functionSelector": "0dfe1681", "implemented": false, "kind": "function", "modifiers": [], "name": "token0", "nameLocation": "87:6:1", "parameters": {"id": 74, "nodeType": "ParameterList", "parameters": [], "src": "93:2:1"}, "returnParameters": {"id": 77, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 76, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 78, "src": "119:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 75, "name": "address", "nodeType": "ElementaryTypeName", "src": "119:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "118:9:1"}, "scope": 98, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 83, "nodeType": "FunctionDefinition", "src": "133:50:1", "nodes": [], "functionSelector": "d21220a7", "implemented": false, "kind": "function", "modifiers": [], "name": "token1", "nameLocation": "142:6:1", "parameters": {"id": 79, "nodeType": "ParameterList", "parameters": [], "src": "148:2:1"}, "returnParameters": {"id": 82, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 81, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 83, "src": "174:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 80, "name": "address", "nodeType": "ElementaryTypeName", "src": "174:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "173:9:1"}, "scope": 98, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 92, "nodeType": "FunctionDefinition", "src": "188:109:1", "nodes": [], "functionSelector": "0902f1ac", "implemented": false, "kind": "function", "modifiers": [], "name": "getReserves", "nameLocation": "197:11:1", "parameters": {"id": 84, "nodeType": "ParameterList", "parameters": [], "src": "208:2:1"}, "returnParameters": {"id": 91, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 86, "mutability": "mutable", "name": "reserve0", "nameLocation": "242:8:1", "nodeType": "VariableDeclaration", "scope": 92, "src": "234:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}, "typeName": {"id": 85, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "234:7:1", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, "visibility": "internal"}, {"constant": false, "id": 88, "mutability": "mutable", "name": "reserve1", "nameLocation": "260:8:1", "nodeType": "VariableDeclaration", "scope": 92, "src": "252:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}, "typeName": {"id": 87, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "252:7:1", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, "visibility": "internal"}, {"constant": false, "id": 90, "mutability": "mutable", "name": "blockTimestampLast", "nameLocation": "277:18:1", "nodeType": "VariableDeclaration", "scope": 92, "src": "270:25:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 89, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "270:6:1", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}], "src": "233:63:1"}, "scope": 98, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 97, "nodeType": "FunctionDefinition", "src": "464:47:1", "nodes": [], "functionSelector": "22be3de1", "implemented": false, "kind": "function", "modifiers": [], "name": "stable", "nameLocation": "473:6:1", "parameters": {"id": 93, "nodeType": "ParameterList", "parameters": [], "src": "479:2:1"}, "returnParameters": {"id": 96, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 95, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 97, "src": "505:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 94, "name": "bool", "nodeType": "ElementaryTypeName", "src": "505:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "504:6:1"}, "scope": 98, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IPair", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [98], "name": "IPair", "nameLocation": "66:5:1", "scope": 558, "usedErrors": [], "usedEvents": []}, {"id": 109, "nodeType": "ContractDefinition", "src": "960:136:1", "nodes": [{"id": 103, "nodeType": "FunctionDefinition", "src": "983:50:1", "nodes": [], "functionSelector": "313ce567", "implemented": false, "kind": "function", "modifiers": [], "name": "decimals", "nameLocation": "992:8:1", "parameters": {"id": 99, "nodeType": "ParameterList", "parameters": [], "src": "1000:2:1"}, "returnParameters": {"id": 102, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 101, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 103, "src": "1026:5:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 100, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1026:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "1025:7:1"}, "scope": 109, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 108, "nodeType": "FunctionDefinition", "src": "1038:56:1", "nodes": [], "functionSelector": "95d89b41", "implemented": false, "kind": "function", "modifiers": [], "name": "symbol", "nameLocation": "1047:6:1", "parameters": {"id": 104, "nodeType": "ParameterList", "parameters": [], "src": "1053:2:1"}, "returnParameters": {"id": 107, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 106, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 108, "src": "1079:13:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 105, "name": "string", "nodeType": "ElementaryTypeName", "src": "1079:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1078:15:1"}, "scope": 109, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IERC20", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [109], "name": "IERC20", "nameLocation": "970:6:1", "scope": 558, "usedErrors": [], "usedEvents": []}, {"id": 557, "nodeType": "ContractDefinition", "src": "1235:5659:1", "nodes": [{"id": 296, "nodeType": "FunctionDefinition", "src": "1283:2264:1", "nodes": [], "body": {"id": 295, "nodeType": "Block", "src": "1319:2228:1", "nodes": [], "statements": [{"assignments": [120], "declarations": [{"constant": false, "id": 120, "mutability": "mutable", "name": "ps", "nameLocation": "1360:2:1", "nodeType": "VariableDeclaration", "scope": 295, "src": "1329:33:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_GetPoolInfoResultDesc_$321_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc[]"}, "typeName": {"baseType": {"id": 118, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 117, "name": "GetPoolInfoResultDesc", "nameLocations": ["1329:21:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 321, "src": "1329:21:1"}, "referencedDeclaration": 321, "src": "1329:21:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_storage_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc"}}, "id": 119, "nodeType": "ArrayTypeName", "src": "1329:23:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_GetPoolInfoResultDesc_$321_storage_$dyn_storage_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc[]"}}, "visibility": "internal"}], "id": 128, "initialValue": {"arguments": [{"expression": {"id": 125, "name": "addrs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 113, "src": "1393:5:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 126, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1399:6:1", "memberName": "length", "nodeType": "MemberAccess", "src": "1393:12:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 124, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "1365:27:1", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_struct$_GetPoolInfoResultDesc_$321_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory[] memory)"}, "typeName": {"baseType": {"id": 122, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 121, "name": "GetPoolInfoResultDesc", "nameLocations": ["1369:21:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 321, "src": "1369:21:1"}, "referencedDeclaration": 321, "src": "1369:21:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_storage_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc"}}, "id": 123, "nodeType": "ArrayTypeName", "src": "1369:23:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_GetPoolInfoResultDesc_$321_storage_$dyn_storage_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc[]"}}}, "id": 127, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1365:41:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_GetPoolInfoResultDesc_$321_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "1329:77:1"}, {"assignments": [130, 132], "declarations": [{"constant": false, "id": 130, "mutability": "mutable", "name": "success", "nameLocation": "1423:7:1", "nodeType": "VariableDeclaration", "scope": 295, "src": "1418:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 129, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1418:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 132, "mutability": "mutable", "name": "data", "nameLocation": "1445:4:1", "nodeType": "VariableDeclaration", "scope": 295, "src": "1432:17:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 131, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1432:5:1", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 142, "initialValue": {"arguments": [{"arguments": [{"hexValue": "737461626c652829", "id": 139, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1497:10:1", "typeDescriptions": {"typeIdentifier": "t_stringliteral_22be3de1a7414985511eaf15ae1b6d89c8986405c25ae17af5470d4f0d0de2cc", "typeString": "literal_string \"stable()\""}, "value": "stable()"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_22be3de1a7414985511eaf15ae1b6d89c8986405c25ae17af5470d4f0d0de2cc", "typeString": "literal_string \"stable()\""}], "expression": {"id": 137, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "1473:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 138, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1477:19:1", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "1473:23:1", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 140, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1473:35:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"baseExpression": {"id": 133, "name": "addrs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 113, "src": "1453:5:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 135, "indexExpression": {"hexValue": "30", "id": 134, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1459:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1453:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 136, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1462:10:1", "memberName": "staticcall", "nodeType": "MemberAccess", "src": "1453:19:1", "typeDescriptions": {"typeIdentifier": "t_function_barestaticcall_view$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) view returns (bool,bytes memory)"}}, "id": 141, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1453:56:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "1417:92:1"}, {"assignments": [144], "declarations": [{"constant": false, "id": 144, "mutability": "mutable", "name": "isStable", "nameLocation": "1524:8:1", "nodeType": "VariableDeclaration", "scope": 295, "src": "1519:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 143, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1519:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "id": 154, "initialValue": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 153, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 145, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 130, "src": "1535:7:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"arguments": [{"id": 148, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 132, "src": "1557:4:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 150, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1564:4:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_bool_$", "typeString": "type(bool)"}, "typeName": {"id": 149, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1564:4:1", "typeDescriptions": {}}}], "id": 151, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1563:6:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_bool_$", "typeString": "type(bool)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_type$_t_bool_$", "typeString": "type(bool)"}], "expression": {"id": 146, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "1546:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 147, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1550:6:1", "memberName": "decode", "nodeType": "MemberAccess", "src": "1546:10:1", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 152, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1546:24:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "1535:35:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "VariableDeclarationStatement", "src": "1519:51:1"}, {"body": {"id": 285, "nodeType": "Block", "src": "1620:1396:1", "statements": [{"assignments": [166], "declarations": [{"constant": false, "id": 166, "mutability": "mutable", "name": "addr", "nameLocation": "1642:4:1", "nodeType": "VariableDeclaration", "scope": 285, "src": "1634:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 165, "name": "address", "nodeType": "ElementaryTypeName", "src": "1634:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 170, "initialValue": {"baseExpression": {"id": 167, "name": "addrs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 113, "src": "1649:5:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 169, "indexExpression": {"id": 168, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 156, "src": "1655:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1649:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "1634:23:1"}, {"assignments": [172, 174, 176, 178, 180, 182, 184], "declarations": [{"constant": false, "id": 172, "mutability": "mutable", "name": "_err", "nameLocation": "1677:4:1", "nodeType": "VariableDeclaration", "scope": 285, "src": "1672:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 171, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1672:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 174, "mutability": "mutable", "name": "_token0", "nameLocation": "1691:7:1", "nodeType": "VariableDeclaration", "scope": 285, "src": "1683:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 173, "name": "address", "nodeType": "ElementaryTypeName", "src": "1683:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 176, "mutability": "mutable", "name": "_token1", "nameLocation": "1708:7:1", "nodeType": "VariableDeclaration", "scope": 285, "src": "1700:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 175, "name": "address", "nodeType": "ElementaryTypeName", "src": "1700:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 178, "mutability": "mutable", "name": "_s0", "nameLocation": "1731:3:1", "nodeType": "VariableDeclaration", "scope": 285, "src": "1717:17:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 177, "name": "string", "nodeType": "ElementaryTypeName", "src": "1717:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 180, "mutability": "mutable", "name": "_s1", "nameLocation": "1750:3:1", "nodeType": "VariableDeclaration", "scope": 285, "src": "1736:17:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 179, "name": "string", "nodeType": "ElementaryTypeName", "src": "1736:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 182, "mutability": "mutable", "name": "_d0", "nameLocation": "1761:3:1", "nodeType": "VariableDeclaration", "scope": 285, "src": "1755:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 181, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1755:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}, {"constant": false, "id": 184, "mutability": "mutable", "name": "_d1", "nameLocation": "1772:3:1", "nodeType": "VariableDeclaration", "scope": 285, "src": "1766:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 183, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1766:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "id": 188, "initialValue": {"arguments": [{"id": 186, "name": "addr", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 166, "src": "1795:4:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 185, "name": "getPairBaseInfo", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 477, "src": "1779:15:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_address_$returns$_t_bool_$_t_address_$_t_address_$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_uint8_$_t_uint8_$", "typeString": "function (address) view returns (bool,address,address,string memory,string memory,uint8,uint8)"}}, "id": 187, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1779:21:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_address_$_t_address_$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_uint8_$_t_uint8_$", "typeString": "tuple(bool,address,address,string memory,string memory,uint8,uint8)"}}, "nodeType": "VariableDeclarationStatement", "src": "1671:129:1"}, {"assignments": [191], "declarations": [{"constant": false, "id": 191, "mutability": "mutable", "name": "p", "nameLocation": "1844:1:1", "nodeType": "VariableDeclaration", "scope": 285, "src": "1815:30:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc"}, "typeName": {"id": 190, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 189, "name": "GetPoolInfoResultDesc", "nameLocations": ["1815:21:1"], "nodeType": "IdentifierPath", "referencedDeclaration": 321, "src": "1815:21:1"}, "referencedDeclaration": 321, "src": "1815:21:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_storage_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc"}}, "visibility": "internal"}], "id": 206, "initialValue": {"arguments": [{"id": 193, "name": "addr", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 166, "src": "1894:4:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 194, "name": "_token0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 174, "src": "1924:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 195, "name": "_token1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 176, "src": "1957:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 196, "name": "_s0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 178, "src": "1986:3:1", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 197, "name": "_s1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 180, "src": "2011:3:1", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"hexValue": "30", "id": 198, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2036:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, {"hexValue": "30", "id": 199, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2059:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, {"id": 200, "name": "_d0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 182, "src": "2082:3:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, {"id": 201, "name": "_d1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 184, "src": "2107:3:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, {"hexValue": "30", "id": 202, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2140:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, {"id": 203, "name": "isStable", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 144, "src": "2224:8:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"id": 204, "name": "_err", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 172, "src": "2255:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, {"typeIdentifier": "t_uint8", "typeString": "uint8"}, {"typeIdentifier": "t_uint8", "typeString": "uint8"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, {"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 192, "name": "GetPoolInfoResultDesc", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 321, "src": "1848:21:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_GetPoolInfoResultDesc_$321_storage_ptr_$", "typeString": "type(struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc storage pointer)"}}, "id": 205, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["1888:4:1", "1916:6:1", "1949:6:1", "1982:2:1", "2007:2:1", "2032:2:1", "2055:2:1", "2078:2:1", "2103:2:1", "2128:10:1", "2216:6:1", "2250:3:1"], "names": ["addr", "token0", "token1", "s0", "s1", "r0", "r1", "d0", "d1", "lastUpdate", "stable", "err"], "nodeType": "FunctionCall", "src": "1848:426:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "nodeType": "VariableDeclarationStatement", "src": "1815:459:1"}, {"expression": {"id": 217, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"components": [{"id": 207, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 130, "src": "2290:7:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"id": 208, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 132, "src": "2299:4:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "id": 209, "isConstant": false, "isInlineArray": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "TupleExpression", "src": "2289:15:1", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"arguments": [{"hexValue": "67657452657365727665732829", "id": 214, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2347:15:1", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0902f1ac5dbaeedd3217f11b3cbaf929216c9c5abc2d69da89d54964bead575d", "typeString": "literal_string \"getReserves()\""}, "value": "getReserves()"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_0902f1ac5dbaeedd3217f11b3cbaf929216c9c5abc2d69da89d54964bead575d", "typeString": "literal_string \"getReserves()\""}], "expression": {"id": 212, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "2323:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 213, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2327:19:1", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "2323:23:1", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 215, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2323:40:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 210, "name": "addr", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 166, "src": "2307:4:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 211, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2312:10:1", "memberName": "staticcall", "nodeType": "MemberAccess", "src": "2307:15:1", "typeDescriptions": {"typeIdentifier": "t_function_barestaticcall_view$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) view returns (bool,bytes memory)"}}, "id": 216, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2307:57:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "src": "2289:75:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 218, "nodeType": "ExpressionStatement", "src": "2289:75:1"}, {"condition": {"id": 219, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 130, "src": "2435:7:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 278, "nodeType": "IfStatement", "src": "2432:528:1", "trueBody": {"id": 277, "nodeType": "Block", "src": "2443:517:1", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 223, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 220, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 132, "src": "2465:4:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 221, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2470:6:1", "memberName": "length", "nodeType": "MemberAccess", "src": "2465:11:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3634", "id": 222, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2480:2:1", "typeDescriptions": {"typeIdentifier": "t_rational_64_by_1", "typeString": "int_const 64"}, "value": "64"}, "src": "2465:17:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 245, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 242, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 132, "src": "2642:4:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 243, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2647:6:1", "memberName": "length", "nodeType": "MemberAccess", "src": "2642:11:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "3936", "id": 244, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2657:2:1", "typeDescriptions": {"typeIdentifier": "t_rational_96_by_1", "typeString": "int_const 96"}, "value": "96"}, "src": "2642:17:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 274, "nodeType": "Block", "src": "2837:109:1", "statements": [{"expression": {"id": 272, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 268, "name": "p", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 191, "src": "2859:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "id": 270, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2861:3:1", "memberName": "err", "nodeType": "MemberAccess", "referencedDeclaration": 320, "src": "2859:5:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 271, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2867:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "2859:12:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 273, "nodeType": "ExpressionStatement", "src": "2859:12:1"}]}, "id": 275, "nodeType": "IfStatement", "src": "2638:308:1", "trueBody": {"id": 267, "nodeType": "Block", "src": "2661:170:1", "statements": [{"expression": {"id": 265, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"components": [{"expression": {"id": 246, "name": "p", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 191, "src": "2740:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "id": 248, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2742:2:1", "memberName": "r0", "nodeType": "MemberAccess", "referencedDeclaration": 308, "src": "2740:4:1", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, {"expression": {"id": 249, "name": "p", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 191, "src": "2746:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "id": 250, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2748:2:1", "memberName": "r1", "nodeType": "MemberAccess", "referencedDeclaration": 310, "src": "2746:4:1", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, {"expression": {"id": 251, "name": "p", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 191, "src": "2752:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "id": 252, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2754:10:1", "memberName": "lastUpdate", "nodeType": "MemberAccess", "referencedDeclaration": 316, "src": "2752:12:1", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "id": 253, "isConstant": false, "isInlineArray": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "TupleExpression", "src": "2739:26:1", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint112_$_t_uint112_$_t_uint32_$", "typeString": "tuple(uint112,uint112,uint32)"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 256, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 132, "src": "2779:4:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 258, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2786:7:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint112_$", "typeString": "type(uint112)"}, "typeName": {"id": 257, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "2786:7:1", "typeDescriptions": {}}}, {"id": 260, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2795:7:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint112_$", "typeString": "type(uint112)"}, "typeName": {"id": 259, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "2795:7:1", "typeDescriptions": {}}}, {"id": 262, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2804:6:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint32_$", "typeString": "type(uint32)"}, "typeName": {"id": 261, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "2804:6:1", "typeDescriptions": {}}}], "id": 263, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2785:26:1", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_type$_t_uint112_$_$_t_type$_t_uint112_$_$_t_type$_t_uint32_$_$", "typeString": "tuple(type(uint112),type(uint112),type(uint32))"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_tuple$_t_type$_t_uint112_$_$_t_type$_t_uint112_$_$_t_type$_t_uint32_$_$", "typeString": "tuple(type(uint112),type(uint112),type(uint32))"}], "expression": {"id": 254, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "2768:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 255, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2772:6:1", "memberName": "decode", "nodeType": "MemberAccess", "src": "2768:10:1", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 264, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2768:44:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint112_$_t_uint112_$_t_uint32_$", "typeString": "tuple(uint112,uint112,uint32)"}}, "src": "2739:73:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 266, "nodeType": "ExpressionStatement", "src": "2739:73:1"}]}}, "id": 276, "nodeType": "IfStatement", "src": "2461:485:1", "trueBody": {"id": 241, "nodeType": "Block", "src": "2484:148:1", "statements": [{"expression": {"id": 239, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"components": [{"expression": {"id": 224, "name": "p", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 191, "src": "2563:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "id": 226, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2565:2:1", "memberName": "r0", "nodeType": "MemberAccess", "referencedDeclaration": 308, "src": "2563:4:1", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, {"expression": {"id": 227, "name": "p", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 191, "src": "2569:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "id": 228, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2571:2:1", "memberName": "r1", "nodeType": "MemberAccess", "referencedDeclaration": 310, "src": "2569:4:1", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}], "id": 229, "isConstant": false, "isInlineArray": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "TupleExpression", "src": "2562:12:1", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint112_$_t_uint112_$", "typeString": "tuple(uint112,uint112)"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 232, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 132, "src": "2588:4:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 234, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2595:7:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint112_$", "typeString": "type(uint112)"}, "typeName": {"id": 233, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "2595:7:1", "typeDescriptions": {}}}, {"id": 236, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2604:7:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint112_$", "typeString": "type(uint112)"}, "typeName": {"id": 235, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "2604:7:1", "typeDescriptions": {}}}], "id": 237, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "2594:18:1", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_type$_t_uint112_$_$_t_type$_t_uint112_$_$", "typeString": "tuple(type(uint112),type(uint112))"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_tuple$_t_type$_t_uint112_$_$_t_type$_t_uint112_$_$", "typeString": "tuple(type(uint112),type(uint112))"}], "expression": {"id": 230, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "2577:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 231, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2581:6:1", "memberName": "decode", "nodeType": "MemberAccess", "src": "2577:10:1", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 238, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2577:36:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint112_$_t_uint112_$", "typeString": "tuple(uint112,uint112)"}}, "src": "2562:51:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 240, "nodeType": "ExpressionStatement", "src": "2562:51:1"}]}}]}}, {"expression": {"id": 283, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 279, "name": "ps", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 120, "src": "2996:2:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_GetPoolInfoResultDesc_$321_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory[] memory"}}, "id": 281, "indexExpression": {"id": 280, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 156, "src": "2999:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2996:5:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 282, "name": "p", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 191, "src": "3004:1:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "src": "2996:9:1", "typeDescriptions": {"typeIdentifier": "t_struct$_GetPoolInfoResultDesc_$321_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory"}}, "id": 284, "nodeType": "ExpressionStatement", "src": "2996:9:1"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 161, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 158, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 156, "src": "1597:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 159, "name": "addrs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 113, "src": "1601:5:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 160, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1607:6:1", "memberName": "length", "nodeType": "MemberAccess", "src": "1601:12:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1597:16:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 286, "initializationExpression": {"assignments": [156], "declarations": [{"constant": false, "id": 156, "mutability": "mutable", "name": "i", "nameLocation": "1594:1:1", "nodeType": "VariableDeclaration", "scope": 286, "src": "1586:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 155, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1586:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 157, "nodeType": "VariableDeclarationStatement", "src": "1586:9:1"}, "loopExpression": {"expression": {"id": 163, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "1615:3:1", "subExpression": {"id": 162, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 156, "src": "1615:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 164, "nodeType": "ExpressionStatement", "src": "1615:3:1"}, "nodeType": "ForStatement", "src": "1581:1435:1"}, {"assignments": [288], "declarations": [{"constant": false, "id": 288, "mutability": "mutable", "name": "_abiEncodedData", "nameLocation": "3231:15:1", "nodeType": "VariableDeclaration", "scope": 295, "src": "3218:28:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 287, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "3218:5:1", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 293, "initialValue": {"arguments": [{"id": 291, "name": "ps", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 120, "src": "3260:2:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_GetPoolInfoResultDesc_$321_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_array$_t_struct$_GetPoolInfoResultDesc_$321_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc memory[] memory"}], "expression": {"id": 289, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "3249:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 290, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "3253:6:1", "memberName": "encode", "nodeType": "MemberAccess", "src": "3249:10:1", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 292, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3249:14:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "3218:45:1"}, {"AST": {"nativeSrc": "3283:258:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3283:258:1", "statements": [{"nativeSrc": "3433:43:1", "nodeType": "YulVariableDeclaration", "src": "3433:43:1", "value": {"arguments": [{"name": "_abiEncodedData", "nativeSrc": "3454:15:1", "nodeType": "YulIdentifier", "src": "3454:15:1"}, {"kind": "number", "nativeSrc": "3471:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3471:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "3450:3:1", "nodeType": "YulIdentifier", "src": "3450:3:1"}, "nativeSrc": "3450:26:1", "nodeType": "YulFunctionCall", "src": "3450:26:1"}, "variables": [{"name": "dataStart", "nativeSrc": "3437:9:1", "nodeType": "YulTypedName", "src": "3437:9:1", "type": ""}]}, {"expression": {"arguments": [{"name": "dataStart", "nativeSrc": "3496:9:1", "nodeType": "YulIdentifier", "src": "3496:9:1"}, {"arguments": [{"arguments": [], "functionName": {"name": "msize", "nativeSrc": "3511:5:1", "nodeType": "YulIdentifier", "src": "3511:5:1"}, "nativeSrc": "3511:7:1", "nodeType": "YulFunctionCall", "src": "3511:7:1"}, {"name": "dataStart", "nativeSrc": "3520:9:1", "nodeType": "YulIdentifier", "src": "3520:9:1"}], "functionName": {"name": "sub", "nativeSrc": "3507:3:1", "nodeType": "YulIdentifier", "src": "3507:3:1"}, "nativeSrc": "3507:23:1", "nodeType": "YulFunctionCall", "src": "3507:23:1"}], "functionName": {"name": "return", "nativeSrc": "3489:6:1", "nodeType": "YulIdentifier", "src": "3489:6:1"}, "nativeSrc": "3489:42:1", "nodeType": "YulFunctionCall", "src": "3489:42:1"}, "nativeSrc": "3489:42:1", "nodeType": "YulExpressionStatement", "src": "3489:42:1"}]}, "evmVersion": "paris", "externalReferences": [{"declaration": 288, "isOffset": false, "isSlot": false, "src": "3454:15:1", "valueSize": 1}], "id": 294, "nodeType": "InlineAssembly", "src": "3274:267:1"}]}, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 114, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 113, "mutability": "mutable", "name": "addrs", "nameLocation": "1312:5:1", "nodeType": "VariableDeclaration", "scope": 296, "src": "1295:22:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 111, "name": "address", "nodeType": "ElementaryTypeName", "src": "1295:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 112, "nodeType": "ArrayTypeName", "src": "1295:9:1", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "1294:24:1"}, "returnParameters": {"id": 115, "nodeType": "ParameterList", "parameters": [], "src": "1319:0:1"}, "scope": 557, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 321, "nodeType": "StructDefinition", "src": "3553:308:1", "nodes": [], "canonicalName": "GetUniswapV2PoolDataBatchRequest.GetPoolInfoResultDesc", "members": [{"constant": false, "id": 298, "mutability": "mutable", "name": "addr", "nameLocation": "3622:4:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3614:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 297, "name": "address", "nodeType": "ElementaryTypeName", "src": "3614:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 300, "mutability": "mutable", "name": "token0", "nameLocation": "3644:6:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3636:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 299, "name": "address", "nodeType": "ElementaryTypeName", "src": "3636:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 302, "mutability": "mutable", "name": "token1", "nameLocation": "3668:6:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3660:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 301, "name": "address", "nodeType": "ElementaryTypeName", "src": "3660:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 304, "mutability": "mutable", "name": "s0", "nameLocation": "3691:2:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3684:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 303, "name": "string", "nodeType": "ElementaryTypeName", "src": "3684:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 306, "mutability": "mutable", "name": "s1", "nameLocation": "3710:2:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3703:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 305, "name": "string", "nodeType": "ElementaryTypeName", "src": "3703:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 308, "mutability": "mutable", "name": "r0", "nameLocation": "3730:2:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3722:10:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}, "typeName": {"id": 307, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "3722:7:1", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, "visibility": "internal"}, {"constant": false, "id": 310, "mutability": "mutable", "name": "r1", "nameLocation": "3750:2:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3742:10:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}, "typeName": {"id": 309, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "3742:7:1", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, "visibility": "internal"}, {"constant": false, "id": 312, "mutability": "mutable", "name": "d0", "nameLocation": "3768:2:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3762:8:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 311, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "3762:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}, {"constant": false, "id": 314, "mutability": "mutable", "name": "d1", "nameLocation": "3786:2:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3780:8:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 313, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "3780:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}, {"constant": false, "id": 316, "mutability": "mutable", "name": "lastUpdate", "nameLocation": "3805:10:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3798:17:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 315, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "3798:6:1", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}, {"constant": false, "id": 318, "mutability": "mutable", "name": "stable", "nameLocation": "3830:6:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3825:11:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 317, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3825:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 320, "mutability": "mutable", "name": "err", "nameLocation": "3851:3:1", "nodeType": "VariableDeclaration", "scope": 321, "src": "3846:8:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 319, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3846:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "name": "GetPoolInfoResultDesc", "nameLocation": "3560:21:1", "scope": 557, "visibility": "public"}, {"id": 477, "nodeType": "FunctionDefinition", "src": "3902:1624:1", "nodes": [], "body": {"id": 476, "nodeType": "Block", "src": "4090:1436:1", "nodes": [], "statements": [{"clauses": [{"block": {"id": 352, "nodeType": "Block", "src": "4150:41:1", "statements": [{"expression": {"id": 350, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 348, "name": "token0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 328, "src": "4164:6:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 349, "name": "_token0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 346, "src": "4173:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "4164:16:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 351, "nodeType": "ExpressionStatement", "src": "4164:16:1"}]}, "errorName": "", "id": 353, "nodeType": "TryCatchClause", "parameters": {"id": 347, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 346, "mutability": "mutable", "name": "_token0", "nameLocation": "4142:7:1", "nodeType": "VariableDeclaration", "scope": 353, "src": "4134:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 345, "name": "address", "nodeType": "ElementaryTypeName", "src": "4134:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4133:17:1"}, "src": "4125:66:1"}, {"block": {"id": 358, "nodeType": "Block", "src": "4198:35:1", "statements": [{"expression": {"id": 356, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 354, "name": "err", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 326, "src": "4212:3:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 355, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4218:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "4212:10:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 357, "nodeType": "ExpressionStatement", "src": "4212:10:1"}]}, "errorName": "", "id": 359, "nodeType": "TryCatchClause", "src": "4192:41:1"}], "externalCall": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 341, "name": "addr", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 323, "src": "4110:4:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 340, "name": "IPair", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 98, "src": "4104:5:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IPair_$98_$", "typeString": "type(contract IPair)"}}, "id": 342, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4104:11:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IPair_$98", "typeString": "contract IPair"}}, "id": 343, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4116:6:1", "memberName": "token0", "nodeType": "MemberAccess", "referencedDeclaration": 78, "src": "4104:18:1", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_address_$", "typeString": "function () view external returns (address)"}}, "id": 344, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4104:20:1", "tryCall": true, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 360, "nodeType": "TryStatement", "src": "4100:133:1"}, {"clauses": [{"block": {"id": 373, "nodeType": "Block", "src": "4293:41:1", "statements": [{"expression": {"id": 371, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 369, "name": "token1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 330, "src": "4307:6:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 370, "name": "_token1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 367, "src": "4316:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "4307:16:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 372, "nodeType": "ExpressionStatement", "src": "4307:16:1"}]}, "errorName": "", "id": 374, "nodeType": "TryCatchClause", "parameters": {"id": 368, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 367, "mutability": "mutable", "name": "_token1", "nameLocation": "4285:7:1", "nodeType": "VariableDeclaration", "scope": 374, "src": "4277:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 366, "name": "address", "nodeType": "ElementaryTypeName", "src": "4277:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4276:17:1"}, "src": "4268:66:1"}, {"block": {"id": 379, "nodeType": "Block", "src": "4341:35:1", "statements": [{"expression": {"id": 377, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 375, "name": "err", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 326, "src": "4355:3:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 376, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4361:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "4355:10:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 378, "nodeType": "ExpressionStatement", "src": "4355:10:1"}]}, "errorName": "", "id": 380, "nodeType": "TryCatchClause", "src": "4335:41:1"}], "externalCall": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 362, "name": "addr", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 323, "src": "4253:4:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 361, "name": "IPair", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 98, "src": "4247:5:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IPair_$98_$", "typeString": "type(contract IPair)"}}, "id": 363, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4247:11:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IPair_$98", "typeString": "contract IPair"}}, "id": 364, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4259:6:1", "memberName": "token1", "nodeType": "MemberAccess", "referencedDeclaration": 83, "src": "4247:18:1", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_address_$", "typeString": "function () view external returns (address)"}}, "id": 365, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4247:20:1", "tryCall": true, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 381, "nodeType": "TryStatement", "src": "4243:133:1"}, {"assignments": [383, 385], "declarations": [{"constant": false, "id": 383, "mutability": "mutable", "name": "success", "nameLocation": "4709:7:1", "nodeType": "VariableDeclaration", "scope": 476, "src": "4704:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 382, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4704:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 385, "mutability": "mutable", "name": "data", "nameLocation": "4731:4:1", "nodeType": "VariableDeclaration", "scope": 476, "src": "4718:17:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 384, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4718:5:1", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 393, "initialValue": {"arguments": [{"arguments": [{"hexValue": "73796d626f6c2829", "id": 390, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4781:10:1", "typeDescriptions": {"typeIdentifier": "t_stringliteral_95d89b41e2f5f391a79ec54e9d87c79d6e777c63e32c28da95b4e9e4a79250ec", "typeString": "literal_string \"symbol()\""}, "value": "symbol()"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_95d89b41e2f5f391a79ec54e9d87c79d6e777c63e32c28da95b4e9e4a79250ec", "typeString": "literal_string \"symbol()\""}], "expression": {"id": 388, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "4757:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 389, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4761:19:1", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "4757:23:1", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 391, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4757:35:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 386, "name": "token0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 328, "src": "4739:6:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 387, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4746:10:1", "memberName": "staticcall", "nodeType": "MemberAccess", "src": "4739:17:1", "typeDescriptions": {"typeIdentifier": "t_function_barestaticcall_view$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) view returns (bool,bytes memory)"}}, "id": 392, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4739:54:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "4703:90:1"}, {"condition": {"id": 394, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 383, "src": "4807:7:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 406, "nodeType": "Block", "src": "4949:35:1", "statements": [{"expression": {"id": 404, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 402, "name": "err", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 326, "src": "4963:3:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 403, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4969:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "4963:10:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 405, "nodeType": "ExpressionStatement", "src": "4963:10:1"}]}, "id": 407, "nodeType": "IfStatement", "src": "4803:181:1", "trueBody": {"id": 401, "nodeType": "Block", "src": "4816:127:1", "statements": [{"expression": {"id": 399, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 395, "name": "s0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 332, "src": "4908:2:1", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 397, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 385, "src": "4927:4:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 396, "name": "bytesToString", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 556, "src": "4913:13:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure returns (string memory)"}}, "id": 398, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4913:19:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "4908:24:1", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 400, "nodeType": "ExpressionStatement", "src": "4908:24:1"}]}}, {"expression": {"id": 418, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"components": [{"id": 408, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 383, "src": "5003:7:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"id": 409, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 385, "src": "5012:4:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "id": 410, "isConstant": false, "isInlineArray": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "TupleExpression", "src": "5002:15:1", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"arguments": [{"hexValue": "73796d626f6c2829", "id": 415, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5062:10:1", "typeDescriptions": {"typeIdentifier": "t_stringliteral_95d89b41e2f5f391a79ec54e9d87c79d6e777c63e32c28da95b4e9e4a79250ec", "typeString": "literal_string \"symbol()\""}, "value": "symbol()"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_95d89b41e2f5f391a79ec54e9d87c79d6e777c63e32c28da95b4e9e4a79250ec", "typeString": "literal_string \"symbol()\""}], "expression": {"id": 413, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "5038:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 414, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5042:19:1", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "5038:23:1", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 416, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5038:35:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 411, "name": "token1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 330, "src": "5020:6:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 412, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5027:10:1", "memberName": "staticcall", "nodeType": "MemberAccess", "src": "5020:17:1", "typeDescriptions": {"typeIdentifier": "t_function_barestaticcall_view$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) view returns (bool,bytes memory)"}}, "id": 417, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5020:54:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "src": "5002:72:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 419, "nodeType": "ExpressionStatement", "src": "5002:72:1"}, {"condition": {"id": 420, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 383, "src": "5088:7:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 432, "nodeType": "Block", "src": "5217:35:1", "statements": [{"expression": {"id": 430, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 428, "name": "err", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 326, "src": "5231:3:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 429, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5237:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "5231:10:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 431, "nodeType": "ExpressionStatement", "src": "5231:10:1"}]}, "id": 433, "nodeType": "IfStatement", "src": "5084:168:1", "trueBody": {"id": 427, "nodeType": "Block", "src": "5097:114:1", "statements": [{"expression": {"id": 425, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 421, "name": "s1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 334, "src": "5176:2:1", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 423, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 385, "src": "5195:4:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 422, "name": "bytesToString", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 556, "src": "5181:13:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_bytes_memory_ptr_$returns$_t_string_memory_ptr_$", "typeString": "function (bytes memory) pure returns (string memory)"}}, "id": 424, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5181:19:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "5176:24:1", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 426, "nodeType": "ExpressionStatement", "src": "5176:24:1"}]}}, {"clauses": [{"block": {"id": 446, "nodeType": "Block", "src": "5311:33:1", "statements": [{"expression": {"id": 444, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 442, "name": "d0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 336, "src": "5325:2:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 443, "name": "_d0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 440, "src": "5330:3:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "5325:8:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 445, "nodeType": "ExpressionStatement", "src": "5325:8:1"}]}, "errorName": "", "id": 447, "nodeType": "TryCatchClause", "parameters": {"id": 441, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 440, "mutability": "mutable", "name": "_d0", "nameLocation": "5307:3:1", "nodeType": "VariableDeclaration", "scope": 447, "src": "5301:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 439, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "5301:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "5300:11:1"}, "src": "5292:52:1"}, {"block": {"id": 452, "nodeType": "Block", "src": "5351:35:1", "statements": [{"expression": {"id": 450, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 448, "name": "err", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 326, "src": "5365:3:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 449, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5371:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "5365:10:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 451, "nodeType": "ExpressionStatement", "src": "5365:10:1"}]}, "errorName": "", "id": 453, "nodeType": "TryCatchClause", "src": "5345:41:1"}], "externalCall": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 435, "name": "token0", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 328, "src": "5273:6:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 434, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 109, "src": "5266:6:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$109_$", "typeString": "type(contract IERC20)"}}, "id": 436, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5266:14:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$109", "typeString": "contract IERC20"}}, "id": 437, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5281:8:1", "memberName": "decimals", "nodeType": "MemberAccess", "referencedDeclaration": 103, "src": "5266:23:1", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint8_$", "typeString": "function () view external returns (uint8)"}}, "id": 438, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5266:25:1", "tryCall": true, "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 454, "nodeType": "TryStatement", "src": "5262:124:1"}, {"clauses": [{"block": {"id": 467, "nodeType": "Block", "src": "5445:33:1", "statements": [{"expression": {"id": 465, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 463, "name": "d1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 338, "src": "5459:2:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 464, "name": "_d1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 461, "src": "5464:3:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "5459:8:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 466, "nodeType": "ExpressionStatement", "src": "5459:8:1"}]}, "errorName": "", "id": 468, "nodeType": "TryCatchClause", "parameters": {"id": 462, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 461, "mutability": "mutable", "name": "_d1", "nameLocation": "5441:3:1", "nodeType": "VariableDeclaration", "scope": 468, "src": "5435:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 460, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "5435:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "5434:11:1"}, "src": "5426:52:1"}, {"block": {"id": 473, "nodeType": "Block", "src": "5485:35:1", "statements": [{"expression": {"id": 471, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 469, "name": "err", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 326, "src": "5499:3:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 470, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5505:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "5499:10:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 472, "nodeType": "ExpressionStatement", "src": "5499:10:1"}]}, "errorName": "", "id": 474, "nodeType": "TryCatchClause", "src": "5479:41:1"}], "externalCall": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 456, "name": "token1", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 330, "src": "5407:6:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 455, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 109, "src": "5400:6:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$109_$", "typeString": "type(contract IERC20)"}}, "id": 457, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5400:14:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$109", "typeString": "contract IERC20"}}, "id": 458, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5415:8:1", "memberName": "decimals", "nodeType": "MemberAccess", "referencedDeclaration": 103, "src": "5400:23:1", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint8_$", "typeString": "function () view external returns (uint8)"}}, "id": 459, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5400:25:1", "tryCall": true, "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 475, "nodeType": "TryStatement", "src": "5396:124:1"}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "getPairBaseInfo", "nameLocation": "3911:15:1", "parameters": {"id": 324, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 323, "mutability": "mutable", "name": "addr", "nameLocation": "3935:4:1", "nodeType": "VariableDeclaration", "scope": 477, "src": "3927:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 322, "name": "address", "nodeType": "ElementaryTypeName", "src": "3927:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3926:14:1"}, "returnParameters": {"id": 339, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 326, "mutability": "mutable", "name": "err", "nameLocation": "3993:3:1", "nodeType": "VariableDeclaration", "scope": 477, "src": "3988:8:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 325, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3988:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 328, "mutability": "mutable", "name": "token0", "nameLocation": "4006:6:1", "nodeType": "VariableDeclaration", "scope": 477, "src": "3998:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 327, "name": "address", "nodeType": "ElementaryTypeName", "src": "3998:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 330, "mutability": "mutable", "name": "token1", "nameLocation": "4022:6:1", "nodeType": "VariableDeclaration", "scope": 477, "src": "4014:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 329, "name": "address", "nodeType": "ElementaryTypeName", "src": "4014:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 332, "mutability": "mutable", "name": "s0", "nameLocation": "4044:2:1", "nodeType": "VariableDeclaration", "scope": 477, "src": "4030:16:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 331, "name": "string", "nodeType": "ElementaryTypeName", "src": "4030:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 334, "mutability": "mutable", "name": "s1", "nameLocation": "4062:2:1", "nodeType": "VariableDeclaration", "scope": 477, "src": "4048:16:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 333, "name": "string", "nodeType": "ElementaryTypeName", "src": "4048:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 336, "mutability": "mutable", "name": "d0", "nameLocation": "4072:2:1", "nodeType": "VariableDeclaration", "scope": 477, "src": "4066:8:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 335, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "4066:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}, {"constant": false, "id": 338, "mutability": "mutable", "name": "d1", "nameLocation": "4082:2:1", "nodeType": "VariableDeclaration", "scope": 477, "src": "4076:8:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 337, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "4076:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "3987:98:1"}, "scope": 557, "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"id": 556, "nodeType": "FunctionDefinition", "src": "5532:590:1", "nodes": [], "body": {"id": 555, "nodeType": "Block", "src": "5614:508:1", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 487, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 484, "name": "_bytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 479, "src": "5627:6:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 485, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5634:6:1", "memberName": "length", "nodeType": "MemberAccess", "src": "5627:13:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "3936", "id": 486, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5643:2:1", "typeDescriptions": {"typeIdentifier": "t_rational_96_by_1", "typeString": "int_const 96"}, "value": "96"}, "src": "5627:18:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 553, "nodeType": "Block", "src": "6056:60:1", "statements": [{"expression": {"arguments": [{"id": 547, "name": "_bytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 479, "src": "6088:6:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 549, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6097:6:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 548, "name": "string", "nodeType": "ElementaryTypeName", "src": "6097:6:1", "typeDescriptions": {}}}], "id": 550, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "6096:8:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}], "expression": {"id": 545, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "6077:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 546, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "6081:6:1", "memberName": "decode", "nodeType": "MemberAccess", "src": "6077:10:1", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 551, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6077:28:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 483, "id": 552, "nodeType": "Return", "src": "6070:35:1"}]}, "id": 554, "nodeType": "IfStatement", "src": "5624:492:1", "trueBody": {"id": 544, "nodeType": "Block", "src": "5646:404:1", "statements": [{"assignments": [489], "declarations": [{"constant": false, "id": 489, "mutability": "mutable", "name": "i", "nameLocation": "5748:1:1", "nodeType": "VariableDeclaration", "scope": 544, "src": "5742:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 488, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "5742:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "id": 491, "initialValue": {"hexValue": "30", "id": 490, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5752:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "5742:11:1"}, {"body": {"id": 504, "nodeType": "Block", "src": "5799:36:1", "statements": [{"expression": {"id": 502, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "5817:3:1", "subExpression": {"id": 501, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5817:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 503, "nodeType": "ExpressionStatement", "src": "5817:3:1"}]}, "condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 500, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 494, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 492, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5773:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "3332", "id": 493, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5777:2:1", "typeDescriptions": {"typeIdentifier": "t_rational_32_by_1", "typeString": "int_const 32"}, "value": "32"}, "src": "5773:6:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}, "id": 499, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"baseExpression": {"id": 495, "name": "_bytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 479, "src": "5783:6:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 497, "indexExpression": {"id": 496, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5790:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5783:9:1", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 498, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5796:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "5783:14:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "5773:24:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 505, "nodeType": "WhileStatement", "src": "5767:68:1"}, {"assignments": [507], "declarations": [{"constant": false, "id": 507, "mutability": "mutable", "name": "bytesArray", "nameLocation": "5861:10:1", "nodeType": "VariableDeclaration", "scope": 544, "src": "5848:23:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 506, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "5848:5:1", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 512, "initialValue": {"arguments": [{"id": 510, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5884:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint8", "typeString": "uint8"}], "id": 509, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "5874:9:1", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_bytes_memory_ptr_$", "typeString": "function (uint256) pure returns (bytes memory)"}, "typeName": {"id": 508, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "5878:5:1", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}}, "id": 511, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5874:12:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "5848:38:1"}, {"body": {"id": 537, "nodeType": "Block", "src": "5943:58:1", "statements": [{"expression": {"id": 535, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 529, "name": "bytesArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 507, "src": "5961:10:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 531, "indexExpression": {"id": 530, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5972:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5961:13:1", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"baseExpression": {"id": 532, "name": "_bytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 479, "src": "5977:6:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 534, "indexExpression": {"id": 533, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5984:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5977:9:1", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "src": "5961:25:1", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "id": 536, "nodeType": "ExpressionStatement", "src": "5961:25:1"}]}, "condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 525, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "id": 519, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 517, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5912:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "3332", "id": 518, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5916:2:1", "typeDescriptions": {"typeIdentifier": "t_rational_32_by_1", "typeString": "int_const 32"}, "value": "32"}, "src": "5912:6:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}, "id": 524, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"baseExpression": {"id": 520, "name": "_bytes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 479, "src": "5922:6:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 522, "indexExpression": {"id": 521, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5929:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5922:9:1", "typeDescriptions": {"typeIdentifier": "t_bytes1", "typeString": "bytes1"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 523, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5935:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "5922:14:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "5912:24:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 538, "initializationExpression": {"expression": {"id": 515, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 513, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5905:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "30", "id": 514, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5909:1:1", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "5905:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 516, "nodeType": "ExpressionStatement", "src": "5905:5:1"}, "loopExpression": {"expression": {"id": 527, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "5938:3:1", "subExpression": {"id": 526, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5938:1:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 528, "nodeType": "ExpressionStatement", "src": "5938:3:1"}, "nodeType": "ForStatement", "src": "5900:101:1"}, {"expression": {"arguments": [{"id": 541, "name": "bytesArray", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 507, "src": "6028:10:1", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 540, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6021:6:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_string_storage_ptr_$", "typeString": "type(string storage pointer)"}, "typeName": {"id": 539, "name": "string", "nodeType": "ElementaryTypeName", "src": "6021:6:1", "typeDescriptions": {}}}, "id": 542, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6021:18:1", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "functionReturnParameters": 483, "id": 543, "nodeType": "Return", "src": "6014:25:1"}]}}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "bytesToString", "nameLocation": "5541:13:1", "parameters": {"id": 480, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 479, "mutability": "mutable", "name": "_bytes", "nameLocation": "5568:6:1", "nodeType": "VariableDeclaration", "scope": 556, "src": "5555:19:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 478, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "5555:5:1", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "5554:21:1"}, "returnParameters": {"id": 483, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 482, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 556, "src": "5599:13:1", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 481, "name": "string", "nodeType": "ElementaryTypeName", "src": "5599:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "5598:15:1"}, "scope": 557, "stateMutability": "pure", "virtual": false, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "GetUniswapV2PoolDataBatchRequest", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 110, "nodeType": "StructuredDocumentation", "src": "1099:135:1", "text": " @dev This contract is not meant to be deployed. Instead, use a static call with the\n       deployment bytecode as payload."}, "fullyImplemented": true, "linearizedBaseContracts": [557], "name": "GetUniswapV2PoolDataBatchRequest", "nameLocation": "1244:32:1", "scope": 558, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 1}