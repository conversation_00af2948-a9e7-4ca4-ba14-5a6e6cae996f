{"abi": [{"inputs": [{"internalType": "uint256", "name": "from", "type": "uint256"}, {"internalType": "uint256", "name": "step", "type": "uint256"}, {"internalType": "address", "name": "factory", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}], "bytecode": {"object": "0x608060405234801561000f575f80fd5b506040516103c93803806103c983398101604081905261002e91610228565b5f816001600160a01b031663574f2ba36040518163ffffffff1660e01b81526004016020604051808303815f875af115801561006c573d5f803e3d5ffd5b50505050604051601f3d908101601f1916820160405261008f9190810190610274565b90508061009c84866102ae565b116100a757826100b1565b6100b184826102c1565b92505f836001600160401b038111156100cc576100cc6102d4565b6040519080825280602002602001820160405280156100f5578160200160208202803683370190505b5090505f5b848110156101b0576001600160a01b038416631e3dd18b61011b83896102ae565b6040518263ffffffff1660e01b815260040161013791906102f0565b6020604051808303815f875af1158015610153573d5f803e3d5ffd5b50505050604051601f3d908101601f1916820160405261017691908101906102fe565b8282815181106101885761018861031c565b6001600160a01b03909216602092830291909101909101526101a981610330565b90506100fa565b505f816040516020016101c391906103b0565b60405160208183030381529060405290506020810180590381f35b805b81146101ea575f80fd5b50565b5f815190506101fb816101de565b92915050565b5f6001600160a01b0382166101fb565b6101e081610201565b5f815190506101fb81610211565b5f805f6060848603121561023d5761023d5f80fd5b5f61024886866101ed565b9350506020610259868287016101ed565b925050604061026a8682870161021a565b9150509250925092565b5f60208284031215610287576102875f80fd5b5f61029284846101ed565b949350505050565b634e487b7160e01b5f52601160045260245ffd5b808201808211156101fb576101fb61029a565b818103818111156101fb576101fb61029a565b634e487b7160e01b5f52604160045260245ffd5b805b82525050565b602081016101fb82846102e8565b5f60208284031215610311576103115f80fd5b5f610292848461021a565b634e487b7160e01b5f52603260045260245ffd5b5f5f1982036103415761034161029a565b5060010190565b6102ea81610201565b5f61035c8383610348565b505060200190565b5f610372825f815192915050565b8084526020938401938301805f5b838110156103a55781516103948882610351565b975060208301925050600101610380565b509495945050505050565b602080825281016103c18184610364565b939250505056fe", "sourceMap": "337:1058:0:-:0;;;388:1005;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;455:22;489:7;-1:-1:-1;;;;;480:32:0;;:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;480:34:0;;;;;;;;;;;;:::i;:::-;455:59;-1:-1:-1;455:59:0;532:11;539:4;532;:11;:::i;:::-;:28;:59;;587:4;532:59;;;563:21;580:4;563:14;:21;:::i;:::-;525:66;;688:25;730:4;-1:-1:-1;;;;;716:19:0;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;716:19:0;;688:47;;751:9;746:110;770:4;766:1;:8;746:110;;;-1:-1:-1;;;;;809:26:0;;;836:8;843:1;836:4;:8;:::i;:::-;809:36;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;809:36:0;;;;;;;;;;;;:::i;:::-;795:8;804:1;795:11;;;;;;;;:::i;:::-;-1:-1:-1;;;;;795:50:0;;;:11;;;;;;;;;;;:50;776:3;;;:::i;:::-;;;746:110;;;;1058:28;1100:8;1089:20;;;;;;;;:::i;:::-;;;;;;;;;;;;;1058:51;;1317:4;1300:15;1296:26;1366:9;1357:7;1353:23;1342:9;1335:42;417:122:30;508:5;490:24;483:5;480:35;470:63;;529:1;526;519:12;470:63;417:122;:::o;545:143::-;602:5;633:6;627:13;618:22;;649:33;676:5;649:33;:::i;:::-;545:143;;;;:::o;826:96::-;863:7;-1:-1:-1;;;;;760:54:30;;892:24;694:126;928:122;1001:24;1019:5;1001:24;:::i;1056:143::-;1113:5;1144:6;1138:13;1129:22;;1160:33;1187:5;1160:33;:::i;1205:663::-;1293:6;1301;1309;1358:2;1346:9;1337:7;1333:23;1329:32;1326:119;;;1364:79;197:1;194;187:12;1364:79;1484:1;1509:64;1565:7;1545:9;1509:64;:::i;:::-;1499:74;;1455:128;1622:2;1648:64;1704:7;1695:6;1684:9;1680:22;1648:64;:::i;:::-;1638:74;;1593:129;1761:2;1787:64;1843:7;1834:6;1823:9;1819:22;1787:64;:::i;:::-;1777:74;;1732:129;1205:663;;;;;:::o;1874:351::-;1944:6;1993:2;1981:9;1972:7;1968:23;1964:32;1961:119;;;1999:79;197:1;194;187:12;1999:79;2119:1;2144:64;2200:7;2180:9;2144:64;:::i;:::-;2134:74;1874:351;-1:-1:-1;;;;1874:351:30:o;2231:180::-;-1:-1:-1;;;2276:1:30;2269:88;2376:4;2373:1;2366:15;2400:4;2397:1;2390:15;2417:191;2546:9;;;2568:10;;;2565:36;;;2581:18;;:::i;2614:194::-;2745:9;;;2767:11;;;2764:37;;;2781:18;;:::i;2814:180::-;-1:-1:-1;;;2859:1:30;2852:88;2959:4;2956:1;2949:15;2983:4;2980:1;2973:15;3000:118;3105:5;3087:24;3082:3;3075:37;3000:118;;:::o;3124:222::-;3255:2;3240:18;;3268:71;3244:9;3312:6;3268:71;:::i;3352:351::-;3422:6;3471:2;3459:9;3450:7;3446:23;3442:32;3439:119;;;3477:79;197:1;194;187:12;3477:79;3597:1;3622:64;3678:7;3658:9;3622:64;:::i;3709:180::-;-1:-1:-1;;;3754:1:30;3747:88;3854:4;3851:1;3844:15;3878:4;3875:1;3868:15;3895:233;3934:3;-1:-1:-1;;3996:5:30;3993:77;3990:103;;4073:18;;:::i;:::-;-1:-1:-1;4120:1:30;4109:13;;3895:233::o;4582:108::-;4659:24;4677:5;4659:24;:::i;4696:179::-;4765:10;4786:46;4828:3;4820:6;4786:46;:::i;:::-;-1:-1:-1;;4864:4:30;4855:14;;4696:179::o;5030:732::-;5149:3;5178:54;5226:5;4201:6;4235:5;4229:12;4219:22;4134:114;-1:-1:-1;;4134:114:30;5178:54;4375:19;;;4427:4;4418:14;;;;4555;;;5468:1;5453:284;5478:6;5475:1;5472:13;5453:284;;;5554:6;5548:13;5581:63;5640:3;5625:13;5581:63;:::i;:::-;5574:70;-1:-1:-1;4983:4:30;4974:14;;5657:70;-1:-1:-1;;5500:1:30;5493:9;5453:284;;;-1:-1:-1;5753:3:30;;5030:732;-1:-1:-1;;;;;5030:732:30:o;5768:373::-;5949:2;5962:47;;;5934:18;;6026:108;5934:18;6120:6;6026:108;:::i;:::-;6018:116;5768:373;-1:-1:-1;;;5768:373:30:o", "linkReferences": {}}, "deployedBytecode": {"object": "0x60806040525f80fdfea264697066735822122056ebb09f2280fb0e86f395149edf247d37e6a7f0bf659b64f6fabde08fc0105e64736f6c63430008150033", "sourceMap": "337:1058:0:-:0;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.21+commit.d9974bed\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"from\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"step\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"factory\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"}],\"devdoc\":{\"details\":\"This contract is not meant to be deployed. Instead, use a static call with the       deployment bytecode as payload.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"forge/contracts/GetUniswapV2PoolAddressBatchRequest.sol\":\"GetUniswapV2PoolAddressBatchRequest\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"details\":{\"constantOptimizer\":true,\"cse\":true,\"deduplicate\":true,\"inliner\":true,\"jumpdestRemover\":true,\"orderLiterals\":true,\"peephole\":true,\"yul\":false},\"runs\":200},\"remappings\":[\":@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/\",\":ds-test/=forge/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=forge/lib/forge-std/src/\",\":openzeppelin-contracts/=forge/lib/openzeppelin-contracts/\"]},\"sources\":{\"forge/contracts/GetUniswapV2PoolAddressBatchRequest.sol\":{\"keccak256\":\"0x5dc91db4298c4e7d16c7e771427d61ecc68b1cd047979e6ad05c99526432f148\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab13204d41058d6f72ed6c79142d2a8479270eba9228e491fb63b986136e72dc\",\"dweb:/ipfs/QmRB6F5LqKtvd6x5DZSxG1Tip9uNHbD6HwYh2nKvLa3sWs\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.21+commit.d9974bed"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "from", "type": "uint256"}, {"internalType": "uint256", "name": "step", "type": "uint256"}, {"internalType": "address", "name": "factory", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/", "ds-test/=forge/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=forge/lib/forge-std/src/", "openzeppelin-contracts/=forge/lib/openzeppelin-contracts/"], "optimizer": {"runs": 200, "details": {"peephole": true, "inliner": true, "jumpdestRemover": true, "orderLiterals": true, "deduplicate": true, "cse": true, "constantOptimizer": true, "yul": false}}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"forge/contracts/GetUniswapV2PoolAddressBatchRequest.sol": "GetUniswapV2PoolAddressBatchRequest"}, "libraries": {}}, "sources": {"forge/contracts/GetUniswapV2PoolAddressBatchRequest.sol": {"keccak256": "0x5dc91db4298c4e7d16c7e771427d61ecc68b1cd047979e6ad05c99526432f148", "urls": ["bzz-raw://ab13204d41058d6f72ed6c79142d2a8479270eba9228e491fb63b986136e72dc", "dweb:/ipfs/QmRB6F5LqKtvd6x5DZSxG1Tip9uNHbD6HwYh2nKvLa3sWs"], "license": "MIT"}}, "version": 1}, "ast": {"absolutePath": "forge/contracts/GetUniswapV2PoolAddressBatchRequest.sol", "id": 92, "exportedSymbols": {"GetUniswapV2PoolAddressBatchRequest": [91], "IFactory": [14]}, "nodeType": "SourceUnit", "src": "31:1365:0", "nodes": [{"id": 1, "nodeType": "PragmaDirective", "src": "31:23:0", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 14, "nodeType": "ContractDefinition", "src": "56:143:0", "nodes": [{"id": 8, "nodeType": "FunctionDefinition", "src": "81:58:0", "nodes": [], "functionSelector": "1e3dd18b", "implemented": false, "kind": "function", "modifiers": [], "name": "allPairs", "nameLocation": "90:8:0", "parameters": {"id": 4, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3, "mutability": "mutable", "name": "idx", "nameLocation": "107:3:0", "nodeType": "VariableDeclaration", "scope": 8, "src": "99:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "99:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "98:13:0"}, "returnParameters": {"id": 7, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 6, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8, "src": "130:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 5, "name": "address", "nodeType": "ElementaryTypeName", "src": "130:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "129:9:0"}, "scope": 14, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 13, "nodeType": "FunctionDefinition", "src": "144:53:0", "nodes": [], "functionSelector": "574f2ba3", "implemented": false, "kind": "function", "modifiers": [], "name": "allPairsLength", "nameLocation": "153:14:0", "parameters": {"id": 9, "nodeType": "ParameterList", "parameters": [], "src": "167:2:0"}, "returnParameters": {"id": 12, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 13, "src": "188:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 10, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "188:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "187:9:0"}, "scope": 14, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IFactory", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [14], "name": "IFactory", "nameLocation": "66:8:0", "scope": 92, "usedErrors": [], "usedEvents": []}, {"id": 91, "nodeType": "ContractDefinition", "src": "337:1058:0", "nodes": [{"id": 90, "nodeType": "FunctionDefinition", "src": "388:1005:0", "nodes": [], "body": {"id": 89, "nodeType": "Block", "src": "445:948:0", "nodes": [], "statements": [{"assignments": [25], "declarations": [{"constant": false, "id": 25, "mutability": "mutable", "name": "allPairsLength", "nameLocation": "463:14:0", "nodeType": "VariableDeclaration", "scope": 89, "src": "455:22:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 24, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "455:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 31, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 27, "name": "factory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "489:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 26, "name": "IFactory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 14, "src": "480:8:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IFactory_$14_$", "typeString": "type(contract IFactory)"}}, "id": 28, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "480:17:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IFactory_$14", "typeString": "contract IFactory"}}, "id": 29, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "498:14:0", "memberName": "allPairsLength", "nodeType": "MemberAccess", "referencedDeclaration": 13, "src": "480:32:0", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$__$returns$_t_uint256_$", "typeString": "function () external returns (uint256)"}}, "id": 30, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "480:34:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "455:59:0"}, {"expression": {"id": 43, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 32, "name": "step", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "525:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 37, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 35, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 33, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17, "src": "532:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 34, "name": "step", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "539:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "532:11:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 36, "name": "allPairsLength", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "546:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "532:28:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseExpression": {"id": 41, "name": "step", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "587:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 42, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "Conditional", "src": "532:59:0", "trueExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 40, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 38, "name": "allPairsLength", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "563:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 39, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17, "src": "580:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "563:21:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "525:66:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 44, "nodeType": "ExpressionStatement", "src": "525:66:0"}, {"assignments": [49], "declarations": [{"constant": false, "id": 49, "mutability": "mutable", "name": "allPairs", "nameLocation": "705:8:0", "nodeType": "VariableDeclaration", "scope": 89, "src": "688:25:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 47, "name": "address", "nodeType": "ElementaryTypeName", "src": "688:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 48, "nodeType": "ArrayTypeName", "src": "688:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "id": 55, "initialValue": {"arguments": [{"id": 53, "name": "step", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "730:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 52, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "716:13:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_address_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (address[] memory)"}, "typeName": {"baseType": {"id": 50, "name": "address", "nodeType": "ElementaryTypeName", "src": "720:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 51, "nodeType": "ArrayTypeName", "src": "720:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}}, "id": 54, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "716:19:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "688:47:0"}, {"body": {"id": 79, "nodeType": "Block", "src": "781:75:0", "statements": [{"expression": {"id": 77, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 66, "name": "allPairs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49, "src": "795:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 68, "indexExpression": {"id": 67, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 57, "src": "804:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "795:11:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 75, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 73, "name": "from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17, "src": "836:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 74, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 57, "src": "843:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "836:8:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"id": 70, "name": "factory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "818:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 69, "name": "IFactory", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 14, "src": "809:8:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IFactory_$14_$", "typeString": "type(contract IFactory)"}}, "id": 71, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "809:17:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IFactory_$14", "typeString": "contract IFactory"}}, "id": 72, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "827:8:0", "memberName": "allPairs", "nodeType": "MemberAccess", "referencedDeclaration": 8, "src": "809:26:0", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$_t_address_$", "typeString": "function (uint256) external returns (address)"}}, "id": 76, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "809:36:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "795:50:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 78, "nodeType": "ExpressionStatement", "src": "795:50:0"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 62, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 60, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 57, "src": "766:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 61, "name": "step", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "770:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "766:8:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 80, "initializationExpression": {"assignments": [57], "declarations": [{"constant": false, "id": 57, "mutability": "mutable", "name": "i", "nameLocation": "759:1:0", "nodeType": "VariableDeclaration", "scope": 80, "src": "751:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 56, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "751:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 59, "initialValue": {"hexValue": "30", "id": 58, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "763:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "751:13:0"}, "loopExpression": {"expression": {"id": 64, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": true, "src": "776:3:0", "subExpression": {"id": 63, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 57, "src": "778:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 65, "nodeType": "ExpressionStatement", "src": "776:3:0"}, "nodeType": "ForStatement", "src": "746:110:0"}, {"assignments": [82], "declarations": [{"constant": false, "id": 82, "mutability": "mutable", "name": "_abiEncodedData", "nameLocation": "1071:15:0", "nodeType": "VariableDeclaration", "scope": 89, "src": "1058:28:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 81, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1058:5:0", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 87, "initialValue": {"arguments": [{"id": 85, "name": "allPairs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49, "src": "1100:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}], "expression": {"id": 83, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "1089:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 84, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1093:6:0", "memberName": "encode", "nodeType": "MemberAccess", "src": "1089:10:0", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 86, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1089:20:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "1058:51:0"}, {"AST": {"nativeSrc": "1129:258:0", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1129:258:0", "statements": [{"nativeSrc": "1279:43:0", "nodeType": "YulVariableDeclaration", "src": "1279:43:0", "value": {"arguments": [{"name": "_abiEncodedData", "nativeSrc": "1300:15:0", "nodeType": "YulIdentifier", "src": "1300:15:0"}, {"kind": "number", "nativeSrc": "1317:4:0", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1317:4:0", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "1296:3:0", "nodeType": "YulIdentifier", "src": "1296:3:0"}, "nativeSrc": "1296:26:0", "nodeType": "YulFunctionCall", "src": "1296:26:0"}, "variables": [{"name": "dataStart", "nativeSrc": "1283:9:0", "nodeType": "YulTypedName", "src": "1283:9:0", "type": ""}]}, {"expression": {"arguments": [{"name": "dataStart", "nativeSrc": "1342:9:0", "nodeType": "YulIdentifier", "src": "1342:9:0"}, {"arguments": [{"arguments": [], "functionName": {"name": "msize", "nativeSrc": "1357:5:0", "nodeType": "YulIdentifier", "src": "1357:5:0"}, "nativeSrc": "1357:7:0", "nodeType": "YulFunctionCall", "src": "1357:7:0"}, {"name": "dataStart", "nativeSrc": "1366:9:0", "nodeType": "YulIdentifier", "src": "1366:9:0"}], "functionName": {"name": "sub", "nativeSrc": "1353:3:0", "nodeType": "YulIdentifier", "src": "1353:3:0"}, "nativeSrc": "1353:23:0", "nodeType": "YulFunctionCall", "src": "1353:23:0"}], "functionName": {"name": "return", "nativeSrc": "1335:6:0", "nodeType": "YulIdentifier", "src": "1335:6:0"}, "nativeSrc": "1335:42:0", "nodeType": "YulFunctionCall", "src": "1335:42:0"}, "nativeSrc": "1335:42:0", "nodeType": "YulExpressionStatement", "src": "1335:42:0"}]}, "evmVersion": "shanghai", "externalReferences": [{"declaration": 82, "isOffset": false, "isSlot": false, "src": "1300:15:0", "valueSize": 1}], "id": 88, "nodeType": "InlineAssembly", "src": "1120:267:0"}]}, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 22, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 17, "mutability": "mutable", "name": "from", "nameLocation": "408:4:0", "nodeType": "VariableDeclaration", "scope": 90, "src": "400:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 16, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "400:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 19, "mutability": "mutable", "name": "step", "nameLocation": "422:4:0", "nodeType": "VariableDeclaration", "scope": 90, "src": "414:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 18, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "414:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 21, "mutability": "mutable", "name": "factory", "nameLocation": "436:7:0", "nodeType": "VariableDeclaration", "scope": 90, "src": "428:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 20, "name": "address", "nodeType": "ElementaryTypeName", "src": "428:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "399:45:0"}, "returnParameters": {"id": 23, "nodeType": "ParameterList", "parameters": [], "src": "445:0:0"}, "scope": 91, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "abstract": false, "baseContracts": [], "canonicalName": "GetUniswapV2PoolAddressBatchRequest", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 15, "nodeType": "StructuredDocumentation", "src": "201:135:0", "text": " @dev This contract is not meant to be deployed. Instead, use a static call with the\n       deployment bytecode as payload."}, "fullyImplemented": true, "linearizedBaseContracts": [91], "name": "GetUniswapV2PoolAddressBatchRequest", "nameLocation": "346:35:0", "scope": 92, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 0}