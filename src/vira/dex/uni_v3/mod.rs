pub mod factory;
pub mod pool;

use crate::{connector::Connector, vira::pool::{<PERSON><PERSON><PERSON>, POOL}};
use alloy::sol;
use serde::{Deserialize, Serialize};
use super::{DexRouter, factory::FactoryConfig, DEX};

#[derive(De<PERSON>ult, Debug, Clone, Serialize, Deserialize)]
pub struct UniV3 {
    config : FactoryConfig,
}


impl DexRouter for UniV3 {
    fn new(config : FactoryConfig) -> DEX {
        DEX::UniV3(UniV3 {
            config : config
        })
    }

    fn config(&self) -> &FactoryConfig {
        &self.config
    }

    fn config_mut(&mut self) -> &mut FactoryConfig {
        &mut self.config
    }

}
