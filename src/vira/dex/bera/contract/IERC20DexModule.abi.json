[{"type": "function", "name": "addLiquidity", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "assetsIn", "type": "address[]", "internalType": "address[]"}, {"name": "amountsIn", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "shares", "type": "address[]", "internalType": "address[]"}, {"name": "shareAmounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "liquidity", "type": "address[]", "internalType": "address[]"}, {"name": "liquidityAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "payable"}, {"type": "function", "name": "batchSwap", "inputs": [{"name": "kind", "type": "uint8", "internalType": "enum IERC20DexModule.SwapKind"}, {"name": "swaps", "type": "tuple[]", "internalType": "struct IERC20DexModule.BatchSwapStep[]", "components": [{"name": "poolId", "type": "address", "internalType": "address"}, {"name": "assetIn", "type": "address", "internalType": "address"}, {"name": "amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "assetOut", "type": "address", "internalType": "address"}, {"name": "amountOut", "type": "uint256", "internalType": "uint256"}, {"name": "userData", "type": "bytes", "internalType": "bytes"}]}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "payable"}, {"type": "function", "name": "createPool", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "assetsIn", "type": "address[]", "internalType": "address[]"}, {"name": "amountsIn", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "poolType", "type": "string", "internalType": "string"}, {"name": "options", "type": "tuple", "internalType": "struct IERC20DexModule.PoolOptions", "components": [{"name": "weights", "type": "tuple[]", "internalType": "struct IERC20DexModule.AssetWeight[]", "components": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "weight", "type": "uint256", "internalType": "uint256"}]}, {"name": "swapFee", "type": "uint256", "internalType": "uint256"}]}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "payable"}, {"type": "function", "name": "getExchangeRate", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "baseAsset", "type": "address", "internalType": "address"}, {"name": "quoteAsset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getLiquidity", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}], "outputs": [{"name": "asset", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getPoolName", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "getPoolOptions", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IERC20DexModule.PoolOptions", "components": [{"name": "weights", "type": "tuple[]", "internalType": "struct IERC20DexModule.AssetWeight[]", "components": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "weight", "type": "uint256", "internalType": "uint256"}]}, {"name": "swapFee", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "getPreviewAddLiquidityNoSwap", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "assets", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "shares", "type": "address[]", "internalType": "address[]"}, {"name": "shareAmounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "liqOut", "type": "address[]", "internalType": "address[]"}, {"name": "liquidityAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getPreviewAddLiquidityStaticPrice", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "liquidity", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "shares", "type": "address[]", "internalType": "address[]"}, {"name": "shareAmounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "liqOut", "type": "address[]", "internalType": "address[]"}, {"name": "liquidityAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getPreviewBatchSwap", "inputs": [{"name": "kind", "type": "uint8", "internalType": "enum IERC20DexModule.SwapKind"}, {"name": "swaps", "type": "tuple[]", "internalType": "struct IERC20DexModule.BatchSwapStep[]", "components": [{"name": "poolId", "type": "address", "internalType": "address"}, {"name": "assetIn", "type": "address", "internalType": "address"}, {"name": "amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "assetOut", "type": "address", "internalType": "address"}, {"name": "amountOut", "type": "uint256", "internalType": "uint256"}, {"name": "userData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getPreviewBurnShares", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getPreviewSharesForLiquidity", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "assets", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "shares", "type": "address[]", "internalType": "address[]"}, {"name": "shareAmounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "liquidity", "type": "address[]", "internalType": "address[]"}, {"name": "liquidityAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getPreviewSharesForSingleSidedLiquidityRequest", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getPreviewSwapExact", "inputs": [{"name": "kind", "type": "uint8", "internalType": "enum IERC20DexModule.SwapKind"}, {"name": "pool", "type": "address", "internalType": "address"}, {"name": "baseAsset", "type": "address", "internalType": "address"}, {"name": "baseAssetAmount", "type": "uint256", "internalType": "uint256"}, {"name": "quoteAsset", "type": "address", "internalType": "address"}], "outputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRemoveLiquidityExactAmountOut", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "assetIn", "type": "address", "internalType": "address"}, {"name": "assetAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getRemoveLiquidityOneSideOut", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "assetOut", "type": "address", "internalType": "address"}, {"name": "sharesIn", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalShares", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "removeLiquidityBurningShares", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "withdraw<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "assetIn", "type": "address", "internalType": "address"}, {"name": "amountIn", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "liquidity", "type": "address[]", "internalType": "address[]"}, {"name": "liquidityAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "payable"}, {"type": "function", "name": "removeLiquidityExactAmount", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "withdraw<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "assetOut", "type": "address", "internalType": "address"}, {"name": "amountOut", "type": "uint256", "internalType": "uint256"}, {"name": "sharesIn", "type": "address", "internalType": "address"}, {"name": "maxSharesIn", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "shares", "type": "address[]", "internalType": "address[]"}, {"name": "shareAmounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "liquidity", "type": "address[]", "internalType": "address[]"}, {"name": "liquidityAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "payable"}, {"type": "function", "name": "swap", "inputs": [{"name": "kind", "type": "uint8", "internalType": "enum IERC20DexModule.SwapKind"}, {"name": "poolId", "type": "address", "internalType": "address"}, {"name": "assetIn", "type": "address", "internalType": "address"}, {"name": "amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "assetOut", "type": "address", "internalType": "address"}, {"name": "amountOut", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "payable"}]