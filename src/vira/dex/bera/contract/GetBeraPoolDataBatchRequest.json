{"abi": [{"inputs": [{"internalType": "address", "name": "router", "type": "address"}, {"internalType": "address[]", "name": "pools", "type": "address[]"}], "stateMutability": "nonpayable", "type": "constructor"}], "bytecode": {"object": "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", "sourceMap": "882:2447:0:-:0;;;1130:1998;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1193:29;1240:5;:12;-1:-1:-1;;;;;1225:28:0;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;882:2447;;;;;;;;;-1:-1:-1;;;;;882:2447:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1225:28;;;;;;;;;;;;;;;;;1193:60;;1269:9;1264:1066;1288:5;:12;1284:1;:16;1264:1066;;;1321:19;1343:5;1349:1;1343:8;;;;;;;;:::i;:::-;;;;;;;1321:30;;1366:24;882:2447;;;;;;;;;-1:-1:-1;;;;;882:2447:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1366:24;1455:6;-1:-1:-1;;;;;1443:32:0;;1476:11;1443:45;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1443:45:0;;;;;;;;;;;;:::i;:::-;1422:17;;;1404:84;1405:15;;;1404:84;-1:-1:-1;;;;;;1531:34:0;;;1566:11;-1:-1:-1;1531:47:0;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1531:47:0;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;1593:27:0;;;;1502:76;-1:-1:-1;1666:15:0;;;;:22;-1:-1:-1;;;;;1654:35:0;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1654:35:0;-1:-1:-1;1634:17:0;;;:55;1735:15;;;;:22;-1:-1:-1;;;;;1722:36:0;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1703:16:0;;;:55;1778:9;1773:248;1798:8;:15;;;:22;1794:1;:26;1773:248;;;1845:13;1861:8;:15;;;1877:1;1861:18;;;;;;;;:::i;:::-;;;;;;;1845:34;;1927:5;-1:-1:-1;;;;;1920:22:0;;:24;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1920:24:0;;;;;;;;;;;;:::i;:::-;1897:8;:17;;;1915:1;1897:20;;;;;;;;:::i;:::-;;;;;;:47;;;;;;;;;;;1991:5;-1:-1:-1;;;;;1984:20:0;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1984:22:0;;;;;;;;;;;;:::i;:::-;1962:8;:16;;;1979:1;1962:19;;;;;;;;:::i;:::-;;;;;;:44;;;;1827:194;1822:3;;;;:::i;:::-;;;1773:248;;;-1:-1:-1;2067:7:0;:15;:22;-1:-1:-1;;;;;2053:37:0;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2053:37:0;-1:-1:-1;2034:16:0;;;:56;2109:9;2104:133;2128:7;:15;:22;2124:1;:26;2104:133;;;2197:7;:15;2213:1;2197:18;;;;;;;;:::i;:::-;;;;;;;:25;;;2175:8;:16;;;2192:1;2175:19;;;;;;;;:::i;:::-;;;;;;;;;;:47;2152:3;;;:::i;:::-;;;2104:133;;;;2265:7;:15;;;2250:12;;;:30;:8;2294:11;2306:1;2294:11;:14;;;;;;;:::i;:::-;;;;;;:25;;;;1307:1023;;;1302:3;;;;:::i;:::-;;;1264:1066;;;;2670:28;2712:11;2701:23;;;;;;;;:::i;:::-;;;;;;;;;;;;;2670:54;;3051:4;3034:15;3030:26;3100:9;3091:7;3087:23;3076:9;3069:42;466:96:25;503:7;-1:-1:-1;;;;;400:54:25;;532:24;521:35;466:96;-1:-1:-1;;466:96:25:o;568:122::-;641:24;659:5;641:24;:::i;:::-;634:5;631:35;621:63;;680:1;677;670:12;621:63;568:122;:::o;696:143::-;753:5;784:6;778:13;769:22;;800:33;827:5;800:33;:::i;1076:180::-;-1:-1:-1;;;1121:1:25;1114:88;1221:4;1218:1;1211:15;1245:4;1242:1;1235:15;1262:281;-1:-1:-1;;1060:2:25;1040:14;;1036:28;1337:6;1333:40;1475:6;1463:10;1460:22;-1:-1:-1;;;;;1427:10:25;1424:34;1421:62;1418:88;;;1486:18;;:::i;:::-;1522:2;1515:22;-1:-1:-1;;1262:281:25:o;1549:129::-;1583:6;1610:20;40:6;73:2;67:9;57:19;;7:75;;1610:20;1600:30;;1639:33;1667:4;1659:6;1639:33;:::i;:::-;1549:129;;;:::o;1684:311::-;1761:4;-1:-1:-1;;;;;1843:6:25;1840:30;1837:56;;;1873:18;;:::i;:::-;-1:-1:-1;1923:4:25;1911:17;;;1973:15;;1684:311::o;2141:732::-;2248:5;2273:81;2289:64;2346:6;2289:64;:::i;:::-;2273:81;:::i;:::-;2389:21;;;2264:90;-1:-1:-1;2437:4:25;2426:16;;;;2478:17;;2466:30;;2508:15;;;2505:122;;;2538:79;197:1;194;187:12;2538:79;2653:6;2636:231;2670:6;2665:3;2662:15;2636:231;;;2745:3;2774:48;2818:3;2806:10;2774:48;:::i;:::-;2762:61;;-1:-1:-1;2852:4:25;2843:14;;;;2687;2636:231;;;2640:21;2254:619;;2141:732;;;;;:::o;2896:385::-;2978:5;3027:3;3020:4;3012:6;3008:17;3004:27;2994:122;;3035:79;197:1;194;187:12;3035:79;3145:6;3139:13;3170:105;3271:3;3263:6;3256:4;3248:6;3244:17;3170:105;:::i;:::-;3161:114;2896:385;-1:-1:-1;;;;2896:385:25:o;3287:710::-;3391:6;3399;3448:2;3436:9;3427:7;3423:23;3419:32;3416:119;;;3454:79;197:1;194;187:12;3454:79;3574:1;3599:64;3655:7;3635:9;3599:64;:::i;:::-;3589:74;;3545:128;3733:2;3722:9;3718:18;3712:25;-1:-1:-1;;;;;3756:6:25;3753:30;3750:117;;;3786:79;197:1;194;187:12;3786:79;3891:89;3972:7;3963:6;3952:9;3948:22;3891:89;:::i;:::-;3881:99;;3683:307;3287:710;;;;;:::o;4003:180::-;-1:-1:-1;;;4048:1:25;4041:88;4148:4;4145:1;4138:15;4172:4;4169:1;4162:15;4189:118;4276:24;4294:5;4276:24;:::i;:::-;4271:3;4264:37;4189:118;;:::o;4313:222::-;4444:2;4429:18;;4457:71;4433:9;4501:6;4457:71;:::i;4941:122::-;5032:5;5014:24;4858:77;5069:143;5126:5;5157:6;5151:13;5142:22;;5173:33;5200:5;5173:33;:::i;5235:732::-;5342:5;5367:81;5383:64;5440:6;5383:64;:::i;5367:81::-;5483:21;;;5358:90;-1:-1:-1;5531:4:25;5520:16;;;;5572:17;;5560:30;;5602:15;;;5599:122;;;5632:79;197:1;194;187:12;5632:79;5747:6;5730:231;5764:6;5759:3;5756:15;5730:231;;;5839:3;5868:48;5912:3;5900:10;5868:48;:::i;:::-;5856:61;;-1:-1:-1;5946:4:25;5937:14;;;;5781;5730:231;;5990:385;6072:5;6121:3;6114:4;6106:6;6102:17;6098:27;6088:122;;6129:79;197:1;194;187:12;6129:79;6239:6;6233:13;6264:105;6365:3;6357:6;6350:4;6342:6;6338:17;6264:105;:::i;6381:913::-;6510:6;6518;6567:2;6555:9;6546:7;6542:23;6538:32;6535:119;;;6573:79;197:1;194;187:12;6573:79;6703:9;6693:24;-1:-1:-1;;;;;6736:6:25;6733:30;6730:117;;;6766:79;197:1;194;187:12;6766:79;6871:89;6952:7;6943:6;6932:9;6928:22;6871:89;:::i;:::-;6861:99;;6664:306;7030:2;7019:9;7015:18;7009:25;-1:-1:-1;;;;;7053:6:25;7050:30;7047:117;;;7083:79;197:1;194;187:12;7083:79;7188:89;7269:7;7260:6;7249:9;7245:22;7188:89;:::i;7915:611::-;8001:5;8045:4;8033:9;8028:3;8024:19;8020:30;8017:117;;;8053:79;197:1;194;187:12;8053:79;8152:21;8168:4;8152:21;:::i;:::-;8143:30;-1:-1:-1;8233:1:25;8273:60;8329:3;8309:9;8273:60;:::i;:::-;8248:86;;-1:-1:-1;8406:2:25;8447:60;8503:3;8479:22;;;8447:60;:::i;:::-;8440:4;8433:5;8429:16;8422:86;8355:164;7915:611;;;;:::o;8560:810::-;8693:5;8718:107;8734:90;8817:6;8734:90;:::i;8718:107::-;8860:21;;;8709:116;-1:-1:-1;8908:4:25;8897:16;;8961:4;8949:17;;8937:30;;8979:15;;;8976:122;;;9009:79;197:1;194;187:12;9009:79;9124:6;9107:257;9141:6;9136:3;9133:15;9107:257;;;9216:3;9245:74;9315:3;9303:10;9245:74;:::i;:::-;9233:87;;-1:-1:-1;9349:4:25;9340:14;;;;9167:4;9158:14;9107:257;;9404:437;9512:5;9561:3;9554:4;9546:6;9542:17;9538:27;9528:122;;9569:79;197:1;194;187:12;9569:79;9679:6;9673:13;9704:131;9831:3;9823:6;9816:4;9808:6;9804:17;9704:131;:::i;9873:819::-;9960:5;10004:4;9992:9;9987:3;9983:19;9979:30;9976:117;;;10012:79;197:1;194;187:12;10012:79;10111:21;10127:4;10111:21;:::i;:::-;10102:30;-1:-1:-1;10204:9:25;10194:24;-1:-1:-1;;;;;10237:6:25;10234:30;10231:117;;;10267:79;197:1;194;187:12;10267:79;10387:111;10494:3;10485:6;10474:9;10470:22;10387:111;:::i;10698:558::-;10795:6;10844:2;10832:9;10823:7;10819:23;10815:32;10812:119;;;10850:79;197:1;194;187:12;10850:79;10980:9;10970:24;-1:-1:-1;;;;;11013:6:25;11010:30;11007:117;;;11043:79;197:1;194;187:12;11043:79;11148:91;11231:7;11222:6;11211:9;11207:22;11148:91;:::i;11354:118::-;11337:4;11326:16;;11425:22;11262:86;11478:139;11533:5;11564:6;11558:13;11549:22;;11580:31;11605:5;11580:31;:::i;11623:347::-;11691:6;11740:2;11728:9;11719:7;11715:23;11711:32;11708:119;;;11746:79;197:1;194;187:12;11746:79;11866:1;11891:62;11945:7;11925:9;11891:62;:::i;12099:308::-;12161:4;-1:-1:-1;;;;;12243:6:25;12240:30;12237:56;;;12273:18;;:::i;:::-;-1:-1:-1;;1060:2:25;1040:14;;1036:28;12395:4;12385:15;;12099:308;-1:-1:-1;;12099:308:25:o;12413:246::-;12494:1;12504:113;12518:6;12515:1;12512:13;12504:113;;;12603:1;12598:3;12594:11;12588:18;12575:11;;;12568:39;12540:2;12533:10;12504:113;;;-1:-1:-1;;12651:1:25;12633:16;;12626:27;12413:246::o;12665:434::-;12754:5;12779:66;12795:49;12837:6;12795:49;:::i;12779:66::-;12770:75;;12868:6;12861:5;12854:21;12906:4;12899:5;12895:16;12944:3;12935:6;12930:3;12926:16;12923:25;12920:112;;;12951:79;197:1;194;187:12;12951:79;13041:52;13086:6;13081:3;13076;13041:52;:::i;:::-;12760:339;12665:434;;;;;:::o;13119:355::-;13186:5;13235:3;13228:4;13220:6;13216:17;13212:27;13202:122;;13243:79;197:1;194;187:12;13243:79;13353:6;13347:13;13378:90;13464:3;13456:6;13449:4;13441:6;13437:17;13378:90;:::i;13480:524::-;13560:6;13609:2;13597:9;13588:7;13584:23;13580:32;13577:119;;;13615:79;197:1;194;187:12;13615:79;13745:9;13735:24;-1:-1:-1;;;;;13778:6:25;13775:30;13772:117;;;13808:79;197:1;194;187:12;13808:79;13913:74;13979:7;13970:6;13959:9;13955:22;13913:74;:::i;14010:180::-;-1:-1:-1;;;14055:1:25;14048:88;14155:4;14152:1;14145:15;14179:4;14176:1;14169:15;14196:233;14235:3;-1:-1:-1;;14297:5:25;14294:77;14291:103;;14374:18;;:::i;:::-;-1:-1:-1;14421:1:25;14410:13;;14196:233::o;15507:108::-;15602:5;15584:24;4858:77;15621:179;15690:10;15711:46;15753:3;15745:6;15711:46;:::i;:::-;-1:-1:-1;;15789:4:25;15780:14;;15621:179::o;15955:712::-;16064:3;16093:54;16141:5;14526:6;14560:5;14554:12;14544:22;14435:138;-1:-1:-1;;14435:138:25;16093:54;14724:19;;;14776:4;14767:14;;;;14928;;;16373:1;16358:284;16383:6;16380:1;16377:13;16358:284;;;16459:6;16453:13;16486:63;16545:3;16530:13;16486:63;:::i;:::-;16479:70;-1:-1:-1;15908:4:25;15899:14;;16562:70;-1:-1:-1;;16405:1:25;16398:9;16358:284;;;-1:-1:-1;16658:3:25;;15955:712;-1:-1:-1;;;;;15955:712:25:o;17411:357::-;17489:3;17517:39;17550:5;14526:6;14560:5;14554:12;14544:22;14435:138;-1:-1:-1;;14435:138:25;17517:39;14724:19;;;14776:4;14767:14;;17565:68;;17642:65;17700:6;17695:3;17688:4;17681:5;17677:16;17642:65;:::i;:::-;1060:2;1040:14;-1:-1:-1;;1036:28:25;17723:39;;;;;;-1:-1:-1;;17411:357:25:o;17774:196::-;17863:10;17898:66;17960:3;17952:6;17898:66;:::i;:::-;17884:80;17774:196;-1:-1:-1;;;17774:196:25:o;18133:971::-;18262:3;18291:64;18349:5;14526:6;14560:5;14554:12;14544:22;14435:138;-1:-1:-1;;14435:138:25;18291:64;14724:19;;;14776:4;14767:14;;18364:93;;18483:3;18528:4;18520:6;18516:17;18511:3;18507:27;18558:66;18618:5;14937:4;14928:14;;14793:156;18558:66;18647:7;18678:1;18663:396;18688:6;18685:1;18682:13;18663:396;;;18759:9;18753:4;18749:20;18744:3;18737:33;18810:6;18804:13;18838:84;18917:4;18902:13;18838:84;:::i;:::-;18830:92;-1:-1:-1;15908:4:25;15899:14;;19044:4;19035:14;;;;;18935:80;-1:-1:-1;;18710:1:25;18703:9;18663:396;;;-1:-1:-1;19075:4:25;;18133:971;-1:-1:-1;;;;;;;18133:971:25:o;19548:179::-;19617:10;19638:46;19680:3;19672:6;19638:46;:::i;19882:712::-;19991:3;20020:54;20068:5;14526:6;14560:5;14554:12;14544:22;14435:138;-1:-1:-1;;14435:138:25;20020:54;14724:19;;;14776:4;14767:14;;;;14928;;;20300:1;20285:284;20310:6;20307:1;20304:13;20285:284;;;20386:6;20380:13;20413:63;20472:3;20457:13;20413:63;:::i;:::-;20406:70;-1:-1:-1;15908:4:25;15899:14;;20489:70;-1:-1:-1;;20332:1:25;20325:9;20285:284;;21032:102;11337:4;11326:16;;21105:22;11262:86;21140:171;21205:10;21226:42;21264:3;21256:6;21226:42;:::i;21460:696::-;21565:3;21594:52;21640:5;14526:6;14560:5;14554:12;14544:22;14435:138;-1:-1:-1;;14435:138:25;21594:52;14724:19;;;14776:4;14767:14;;;;14928;;;21868:1;21853:278;21878:6;21875:1;21872:13;21853:278;;;21954:6;21948:13;21981:59;22036:3;22021:13;21981:59;:::i;:::-;21974:66;-1:-1:-1;15908:4:25;15899:14;;22053:68;-1:-1:-1;;21900:1:25;21893:9;21853:278;;22260:1929;22367:3;22403:4;22394:14;;22483:5;22473:23;22509:63;22561:3;22543:12;22509:63;:::i;:::-;22418:164;22667:4;22660:5;22656:16;22650:23;22720:3;22714:4;22710:14;22703:4;22698:3;22694:14;22687:38;22746:103;22844:4;22830:12;22746:103;:::i;:::-;22738:111;;22592:268;22945:4;22938:5;22934:16;22928:23;22998:3;22992:4;22988:14;22981:4;22976:3;22972:14;22965:38;23024:123;23142:4;23128:12;23024:123;:::i;:::-;23016:131;;22870:288;23242:4;23235:5;23231:16;23225:23;23295:3;23289:4;23285:14;23278:4;23273:3;23269:14;23262:38;23321:103;23419:4;23405:12;23321:103;:::i;:::-;23313:111;;23168:267;23521:4;23514:5;23510:16;23504:23;23574:3;23568:4;23564:14;23557:4;23552:3;23548:14;23541:38;23600:99;23694:4;23680:12;23600:99;:::i;:::-;23592:107;;23445:265;23796:4;23789:5;23785:16;23779:23;23849:3;23843:4;23839:14;23832:4;23827:3;23823:14;23816:38;23875:103;23973:4;23959:12;23875:103;:::i;:::-;23867:111;;23720:269;24070:4;24063:5;24059:16;24053:23;24089:63;24146:4;24141:3;24137:14;24123:12;24089:63;:::i;24195:252::-;24312:10;24347:94;24437:3;24429:6;24347:94;:::i;24698:1103::-;24865:3;24894:78;24966:5;14526:6;14560:5;14554:12;14544:22;14435:138;-1:-1:-1;;14435:138:25;24894:78;14724:19;;;14776:4;14767:14;;24981:117;;25124:3;25169:4;25161:6;25157:17;25152:3;25148:27;25199:80;25273:5;14937:4;14928:14;;14793:156;25199:80;25302:7;25333:1;25318:438;25343:6;25340:1;25337:13;25318:438;;;25414:9;25408:4;25404:20;25399:3;25392:33;25465:6;25459:13;25493:112;25600:4;25585:13;25493:112;:::i;:::-;25485:120;-1:-1:-1;15908:4:25;15899:14;;25741:4;25732:14;;;;;25618:94;-1:-1:-1;;25365:1:25;25358:9;25318:438;;25807:469;26036:2;26049:47;;;26021:18;;26113:156;26021:18;26255:6;26113:156;:::i", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052600080fdfea264697066735822122016e6e402e736e45f3ae3ab93b72e5a471d7113ab3ef4c081dcfdcb25c1a00c2964736f6c63430008150033", "sourceMap": "882:2447:0:-:0;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.21+commit.d9974bed\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"router\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"pools\",\"type\":\"address[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"}],\"devdoc\":{\"details\":\"This contract is not meant to be deployed. Instead, use a static call with the deployment bytecode as payload.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"forge/contracts/GetBeraPoolDataBatchRequest.sol\":\"GetBeraPoolDataBatchRequest\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"details\":{\"constantOptimizer\":true,\"cse\":true,\"deduplicate\":true,\"inliner\":true,\"jumpdestRemover\":true,\"orderLiterals\":true,\"peephole\":true,\"yul\":false},\"runs\":200},\"remappings\":[\":@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/\",\":ds-test/=forge/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=forge/lib/forge-std/src/\",\":openzeppelin-contracts/=forge/lib/openzeppelin-contracts/\"]},\"sources\":{\"forge/contracts/GetBeraPoolDataBatchRequest.sol\":{\"keccak256\":\"0x01e38b608d5774b6703865dac1cbeb6c3fd360a888910cd164af283d99a5153c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e21178d9859a86c2a203ca70408e1917c0bb28eb05d2ef36cc0e3da997ffb792\",\"dweb:/ipfs/QmSFPDwd5kvMn4RivxaN24kNQntUKXKpanMdLsgDgsfmck\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.21+commit.d9974bed"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "router", "type": "address"}, {"internalType": "address[]", "name": "pools", "type": "address[]"}], "stateMutability": "nonpayable", "type": "constructor"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/", "ds-test/=forge/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=forge/lib/forge-std/src/", "openzeppelin-contracts/=forge/lib/openzeppelin-contracts/"], "optimizer": {"runs": 200, "details": {"peephole": true, "inliner": true, "jumpdestRemover": true, "orderLiterals": true, "deduplicate": true, "cse": true, "constantOptimizer": true, "yul": false}}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"forge/contracts/GetBeraPoolDataBatchRequest.sol": "GetBeraPoolDataBatchRequest"}, "libraries": {}}, "sources": {"forge/contracts/GetBeraPoolDataBatchRequest.sol": {"keccak256": "0x01e38b608d5774b6703865dac1cbeb6c3fd360a888910cd164af283d99a5153c", "urls": ["bzz-raw://e21178d9859a86c2a203ca70408e1917c0bb28eb05d2ef36cc0e3da997ffb792", "dweb:/ipfs/QmSFPDwd5kvMn4RivxaN24kNQntUKXKpanMdLsgDgsfmck"], "license": "MIT"}}, "version": 1}, "ast": {"absolutePath": "forge/contracts/GetBeraPoolDataBatchRequest.sol", "id": 308, "exportedSymbols": {"AssetWeight": [6], "GetBeraPoolDataBatchRequest": [307], "IBeraRouter": [51], "IERC20": [62], "PoolOptions": [13]}, "nodeType": "SourceUnit", "src": "31:3299:0", "nodes": [{"id": 1, "nodeType": "PragmaDirective", "src": "31:23:0", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 6, "nodeType": "StructDefinition", "src": "56:61:0", "nodes": [], "canonicalName": "AssetWeight", "members": [{"constant": false, "id": 3, "mutability": "mutable", "name": "asset", "nameLocation": "89:5:0", "nodeType": "VariableDeclaration", "scope": 6, "src": "81:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 2, "name": "address", "nodeType": "ElementaryTypeName", "src": "81:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 5, "mutability": "mutable", "name": "weight", "nameLocation": "108:6:0", "nodeType": "VariableDeclaration", "scope": 6, "src": "100:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "100:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "name": "AssetWeight", "nameLocation": "63:11:0", "scope": 308, "visibility": "public"}, {"id": 13, "nodeType": "StructDefinition", "src": "119:70:0", "nodes": [], "canonicalName": "PoolOptions", "members": [{"constant": false, "id": 10, "mutability": "mutable", "name": "weights", "nameLocation": "158:7:0", "nodeType": "VariableDeclaration", "scope": 13, "src": "144:21:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_AssetWeight_$6_storage_$dyn_storage_ptr", "typeString": "struct AssetWeight[]"}, "typeName": {"baseType": {"id": 8, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 7, "name": "AssetWeight", "nameLocations": ["144:11:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 6, "src": "144:11:0"}, "referencedDeclaration": 6, "src": "144:11:0", "typeDescriptions": {"typeIdentifier": "t_struct$_AssetWeight_$6_storage_ptr", "typeString": "struct AssetWeight"}}, "id": 9, "nodeType": "ArrayTypeName", "src": "144:13:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_AssetWeight_$6_storage_$dyn_storage_ptr", "typeString": "struct AssetWeight[]"}}, "visibility": "internal"}, {"constant": false, "id": 12, "mutability": "mutable", "name": "swapFee", "nameLocation": "179:7:0", "nodeType": "VariableDeclaration", "scope": 13, "src": "171:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 11, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "171:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "name": "PoolOptions", "nameLocation": "126:11:0", "scope": 308, "visibility": "public"}, {"id": 51, "nodeType": "ContractDefinition", "src": "191:420:0", "nodes": [{"id": 24, "nodeType": "FunctionDefinition", "src": "219:109:0", "nodes": [], "functionSelector": "a747b93b", "implemented": false, "kind": "function", "modifiers": [], "name": "getLiquidity", "nameLocation": "228:12:0", "parameters": {"id": 16, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 15, "mutability": "mutable", "name": "pool", "nameLocation": "249:4:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "241:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 14, "name": "address", "nodeType": "ElementaryTypeName", "src": "241:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "240:14:0"}, "returnParameters": {"id": 23, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 19, "mutability": "mutable", "name": "asset", "nameLocation": "295:5:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "278:22:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 17, "name": "address", "nodeType": "ElementaryTypeName", "src": "278:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 18, "nodeType": "ArrayTypeName", "src": "278:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}, {"constant": false, "id": 22, "mutability": "mutable", "name": "amounts", "nameLocation": "319:7:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "302:24:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 20, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "302:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 21, "nodeType": "ArrayTypeName", "src": "302:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "277:50:0"}, "scope": 51, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 31, "nodeType": "FunctionDefinition", "src": "333:73:0", "nodes": [], "functionSelector": "72afbc30", "implemented": false, "kind": "function", "modifiers": [], "name": "getPoolName", "nameLocation": "342:11:0", "parameters": {"id": 27, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 26, "mutability": "mutable", "name": "pool", "nameLocation": "362:4:0", "nodeType": "VariableDeclaration", "scope": 31, "src": "354:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 25, "name": "address", "nodeType": "ElementaryTypeName", "src": "354:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "353:14:0"}, "returnParameters": {"id": 30, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 29, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 31, "src": "391:13:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 28, "name": "string", "nodeType": "ElementaryTypeName", "src": "391:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "390:15:0"}, "scope": 51, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 39, "nodeType": "FunctionDefinition", "src": "411:81:0", "nodes": [], "functionSelector": "7ee84de4", "implemented": false, "kind": "function", "modifiers": [], "name": "getPoolOptions", "nameLocation": "420:14:0", "parameters": {"id": 34, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 33, "mutability": "mutable", "name": "pool", "nameLocation": "443:4:0", "nodeType": "VariableDeclaration", "scope": 39, "src": "435:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 32, "name": "address", "nodeType": "ElementaryTypeName", "src": "435:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "434:14:0"}, "returnParameters": {"id": 38, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 37, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 39, "src": "472:18:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolOptions_$13_memory_ptr", "typeString": "struct PoolOptions"}, "typeName": {"id": 36, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 35, "name": "PoolOptions", "nameLocations": ["472:11:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 13, "src": "472:11:0"}, "referencedDeclaration": 13, "src": "472:11:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolOptions_$13_storage_ptr", "typeString": "struct PoolOptions"}}, "visibility": "internal"}], "src": "471:20:0"}, "scope": 51, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 50, "nodeType": "FunctionDefinition", "src": "497:112:0", "nodes": [], "functionSelector": "f20d8634", "implemented": false, "kind": "function", "modifiers": [], "name": "getTotalShares", "nameLocation": "506:14:0", "parameters": {"id": 42, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 41, "mutability": "mutable", "name": "pool", "nameLocation": "529:4:0", "nodeType": "VariableDeclaration", "scope": 50, "src": "521:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 40, "name": "address", "nodeType": "ElementaryTypeName", "src": "521:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "520:14:0"}, "returnParameters": {"id": 49, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 45, "mutability": "mutable", "name": "assets", "nameLocation": "575:6:0", "nodeType": "VariableDeclaration", "scope": 50, "src": "558:23:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 43, "name": "address", "nodeType": "ElementaryTypeName", "src": "558:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 44, "nodeType": "ArrayTypeName", "src": "558:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}, {"constant": false, "id": 48, "mutability": "mutable", "name": "amounts", "nameLocation": "600:7:0", "nodeType": "VariableDeclaration", "scope": 50, "src": "583:24:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 46, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "583:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 47, "nodeType": "ArrayTypeName", "src": "583:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "557:51:0"}, "scope": 51, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IBeraRouter", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [51], "name": "IBeraRouter", "nameLocation": "201:11:0", "scope": 308, "usedErrors": [], "usedEvents": []}, {"id": 62, "nodeType": "ContractDefinition", "src": "613:136:0", "nodes": [{"id": 56, "nodeType": "FunctionDefinition", "src": "636:56:0", "nodes": [], "functionSelector": "95d89b41", "implemented": false, "kind": "function", "modifiers": [], "name": "symbol", "nameLocation": "645:6:0", "parameters": {"id": 52, "nodeType": "ParameterList", "parameters": [], "src": "651:2:0"}, "returnParameters": {"id": 55, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 54, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 56, "src": "677:13:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 53, "name": "string", "nodeType": "ElementaryTypeName", "src": "677:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "676:15:0"}, "scope": 62, "stateMutability": "view", "virtual": false, "visibility": "external"}, {"id": 61, "nodeType": "FunctionDefinition", "src": "697:50:0", "nodes": [], "functionSelector": "313ce567", "implemented": false, "kind": "function", "modifiers": [], "name": "decimals", "nameLocation": "706:8:0", "parameters": {"id": 57, "nodeType": "ParameterList", "parameters": [], "src": "714:2:0"}, "returnParameters": {"id": 60, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 59, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 61, "src": "740:5:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 58, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "740:5:0", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "visibility": "internal"}], "src": "739:7:0"}, "scope": 62, "stateMutability": "view", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [], "canonicalName": "IERC20", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "linearizedBaseContracts": [62], "name": "IERC20", "nameLocation": "623:6:0", "scope": 308, "usedErrors": [], "usedEvents": []}, {"id": 307, "nodeType": "ContractDefinition", "src": "882:2447:0", "nodes": [{"id": 83, "nodeType": "StructDefinition", "src": "925:199:0", "nodes": [], "canonicalName": "GetBeraPoolDataBatchRequest.PoolData", "members": [{"constant": false, "id": 65, "mutability": "mutable", "name": "addr", "nameLocation": "959:4:0", "nodeType": "VariableDeclaration", "scope": 83, "src": "951:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 64, "name": "address", "nodeType": "ElementaryTypeName", "src": "951:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 68, "mutability": "mutable", "name": "weights", "nameLocation": "983:7:0", "nodeType": "VariableDeclaration", "scope": 83, "src": "973:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 66, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "973:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 67, "nodeType": "ArrayTypeName", "src": "973:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}, {"constant": false, "id": 71, "mutability": "mutable", "name": "symbols", "nameLocation": "1009:7:0", "nodeType": "VariableDeclaration", "scope": 83, "src": "1000:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}, "typeName": {"baseType": {"id": 69, "name": "string", "nodeType": "ElementaryTypeName", "src": "1000:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 70, "nodeType": "ArrayTypeName", "src": "1000:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}, "visibility": "internal"}, {"constant": false, "id": 74, "mutability": "mutable", "name": "tokens", "nameLocation": "1036:6:0", "nodeType": "VariableDeclaration", "scope": 83, "src": "1026:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 72, "name": "address", "nodeType": "ElementaryTypeName", "src": "1026:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 73, "nodeType": "ArrayTypeName", "src": "1026:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}, {"constant": false, "id": 77, "mutability": "mutable", "name": "decimals", "nameLocation": "1060:8:0", "nodeType": "VariableDeclaration", "scope": 83, "src": "1052:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint8_$dyn_storage_ptr", "typeString": "uint8[]"}, "typeName": {"baseType": {"id": 75, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1052:5:0", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 76, "nodeType": "ArrayTypeName", "src": "1052:7:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint8_$dyn_storage_ptr", "typeString": "uint8[]"}}, "visibility": "internal"}, {"constant": false, "id": 80, "mutability": "mutable", "name": "reserves", "nameLocation": "1088:8:0", "nodeType": "VariableDeclaration", "scope": 83, "src": "1078:18:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 78, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1078:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 79, "nodeType": "ArrayTypeName", "src": "1078:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}, {"constant": false, "id": 82, "mutability": "mutable", "name": "fee", "nameLocation": "1114:3:0", "nodeType": "VariableDeclaration", "scope": 83, "src": "1106:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 81, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1106:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "name": "PoolData", "nameLocation": "932:8:0", "scope": 307, "visibility": "public"}, {"id": 286, "nodeType": "FunctionDefinition", "src": "1130:1998:0", "nodes": [], "body": {"id": 285, "nodeType": "Block", "src": "1182:1946:0", "nodes": [], "statements": [{"assignments": [95], "declarations": [{"constant": false, "id": 95, "mutability": "mutable", "name": "allPoolData", "nameLocation": "1211:11:0", "nodeType": "VariableDeclaration", "scope": 285, "src": "1193:29:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_PoolData_$83_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData[]"}, "typeName": {"baseType": {"id": 93, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 92, "name": "PoolData", "nameLocations": ["1193:8:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 83, "src": "1193:8:0"}, "referencedDeclaration": 83, "src": "1193:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_storage_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData"}}, "id": 94, "nodeType": "ArrayTypeName", "src": "1193:10:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_PoolData_$83_storage_$dyn_storage_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData[]"}}, "visibility": "internal"}], "id": 103, "initialValue": {"arguments": [{"expression": {"id": 100, "name": "pools", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 88, "src": "1240:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 101, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1246:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1240:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 99, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "1225:14:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_struct$_PoolData_$83_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (struct GetBeraPoolDataBatchRequest.PoolData memory[] memory)"}, "typeName": {"baseType": {"id": 97, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 96, "name": "PoolData", "nameLocations": ["1229:8:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 83, "src": "1229:8:0"}, "referencedDeclaration": 83, "src": "1229:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_storage_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData"}}, "id": 98, "nodeType": "ArrayTypeName", "src": "1229:10:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_PoolData_$83_storage_$dyn_storage_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData[]"}}}, "id": 102, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1225:28:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_PoolData_$83_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "1193:60:0"}, {"body": {"id": 275, "nodeType": "Block", "src": "1307:1023:0", "statements": [{"assignments": [116], "declarations": [{"constant": false, "id": 116, "mutability": "mutable", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1329:11:0", "nodeType": "VariableDeclaration", "scope": 275, "src": "1321:19:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 115, "name": "address", "nodeType": "ElementaryTypeName", "src": "1321:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 120, "initialValue": {"baseExpression": {"id": 117, "name": "pools", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 88, "src": "1343:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 119, "indexExpression": {"id": 118, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "1349:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1343:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "1321:30:0"}, {"assignments": [123], "declarations": [{"constant": false, "id": 123, "mutability": "mutable", "name": "poolData", "nameLocation": "1382:8:0", "nodeType": "VariableDeclaration", "scope": 275, "src": "1366:24:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData"}, "typeName": {"id": 122, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 121, "name": "PoolData", "nameLocations": ["1366:8:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 83, "src": "1366:8:0"}, "referencedDeclaration": 83, "src": "1366:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_storage_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData"}}, "visibility": "internal"}], "id": 124, "nodeType": "VariableDeclarationStatement", "src": "1366:24:0"}, {"expression": {"id": 137, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"components": [{"expression": {"id": 125, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1405:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 127, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "1414:6:0", "memberName": "tokens", "nodeType": "MemberAccess", "referencedDeclaration": 74, "src": "1405:15:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, {"expression": {"id": 128, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1422:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 129, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "1431:8:0", "memberName": "reserves", "nodeType": "MemberAccess", "referencedDeclaration": 80, "src": "1422:17:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}], "id": 130, "isConstant": false, "isInlineArray": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "TupleExpression", "src": "1404:36:0", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_array$_t_address_$dyn_memory_ptr_$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "tuple(address[] memory,uint256[] memory)"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 135, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 116, "src": "1476:11:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"arguments": [{"id": 132, "name": "router", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 85, "src": "1455:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 131, "name": "IBeraRouter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 51, "src": "1443:11:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IBeraRouter_$51_$", "typeString": "type(contract IBeraRouter)"}}, "id": 133, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1443:19:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IBeraRouter_$51", "typeString": "contract IBeraRouter"}}, "id": 134, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1463:12:0", "memberName": "getLiquidity", "nodeType": "MemberAccess", "referencedDeclaration": 24, "src": "1443:32:0", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_array$_t_address_$dyn_memory_ptr_$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (address) view external returns (address[] memory,uint256[] memory)"}}, "id": 136, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1443:45:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_array$_t_address_$dyn_memory_ptr_$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "tuple(address[] memory,uint256[] memory)"}}, "src": "1404:84:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 138, "nodeType": "ExpressionStatement", "src": "1404:84:0"}, {"assignments": [141], "declarations": [{"constant": false, "id": 141, "mutability": "mutable", "name": "options", "nameLocation": "1521:7:0", "nodeType": "VariableDeclaration", "scope": 275, "src": "1502:26:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolOptions_$13_memory_ptr", "typeString": "struct PoolOptions"}, "typeName": {"id": 140, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 139, "name": "PoolOptions", "nameLocations": ["1502:11:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 13, "src": "1502:11:0"}, "referencedDeclaration": 13, "src": "1502:11:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolOptions_$13_storage_ptr", "typeString": "struct PoolOptions"}}, "visibility": "internal"}], "id": 148, "initialValue": {"arguments": [{"id": 146, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 116, "src": "1566:11:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"arguments": [{"id": 143, "name": "router", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 85, "src": "1543:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 142, "name": "IBeraRouter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 51, "src": "1531:11:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IBeraRouter_$51_$", "typeString": "type(contract IBeraRouter)"}}, "id": 144, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1531:19:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IBeraRouter_$51", "typeString": "contract IBeraRouter"}}, "id": 145, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1551:14:0", "memberName": "getPoolOptions", "nodeType": "MemberAccess", "referencedDeclaration": 39, "src": "1531:34:0", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_struct$_PoolOptions_$13_memory_ptr_$", "typeString": "function (address) view external returns (struct PoolOptions memory)"}}, "id": 147, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1531:47:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_PoolOptions_$13_memory_ptr", "typeString": "struct PoolOptions memory"}}, "nodeType": "VariableDeclarationStatement", "src": "1502:76:0"}, {"expression": {"id": 153, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 149, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1593:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 151, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "1602:4:0", "memberName": "addr", "nodeType": "MemberAccess", "referencedDeclaration": 65, "src": "1593:13:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 152, "name": "<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 116, "src": "1609:11:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1593:27:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 154, "nodeType": "ExpressionStatement", "src": "1593:27:0"}, {"expression": {"id": 165, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 155, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1634:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 157, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "1643:8:0", "memberName": "decimals", "nodeType": "MemberAccess", "referencedDeclaration": 77, "src": "1634:17:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint8_$dyn_memory_ptr", "typeString": "uint8[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"expression": {"expression": {"id": 161, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1666:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 162, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1675:6:0", "memberName": "tokens", "nodeType": "MemberAccess", "referencedDeclaration": 74, "src": "1666:15:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 163, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1682:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1666:22:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 160, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "1654:11:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_uint8_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (uint8[] memory)"}, "typeName": {"baseType": {"id": 158, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1658:5:0", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 159, "nodeType": "ArrayTypeName", "src": "1658:7:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint8_$dyn_storage_ptr", "typeString": "uint8[]"}}}, "id": 164, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1654:35:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint8_$dyn_memory_ptr", "typeString": "uint8[] memory"}}, "src": "1634:55:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint8_$dyn_memory_ptr", "typeString": "uint8[] memory"}}, "id": 166, "nodeType": "ExpressionStatement", "src": "1634:55:0"}, {"expression": {"id": 177, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 167, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1703:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 169, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "1712:7:0", "memberName": "symbols", "nodeType": "MemberAccess", "referencedDeclaration": 71, "src": "1703:16:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"expression": {"expression": {"id": 173, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1735:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 174, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1744:6:0", "memberName": "tokens", "nodeType": "MemberAccess", "referencedDeclaration": 74, "src": "1735:15:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 175, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1751:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1735:22:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 172, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "1722:12:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_string_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (string memory[] memory)"}, "typeName": {"baseType": {"id": 170, "name": "string", "nodeType": "ElementaryTypeName", "src": "1726:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "id": 171, "nodeType": "ArrayTypeName", "src": "1726:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_storage_$dyn_storage_ptr", "typeString": "string[]"}}}, "id": 176, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1722:36:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "src": "1703:55:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 178, "nodeType": "ExpressionStatement", "src": "1703:55:0"}, {"body": {"id": 222, "nodeType": "Block", "src": "1827:194:0", "statements": [{"assignments": [192], "declarations": [{"constant": false, "id": 192, "mutability": "mutable", "name": "token", "nameLocation": "1853:5:0", "nodeType": "VariableDeclaration", "scope": 222, "src": "1845:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 191, "name": "address", "nodeType": "ElementaryTypeName", "src": "1845:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 197, "initialValue": {"baseExpression": {"expression": {"id": 193, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1861:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 194, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1870:6:0", "memberName": "tokens", "nodeType": "MemberAccess", "referencedDeclaration": 74, "src": "1861:15:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 196, "indexExpression": {"id": 195, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 180, "src": "1877:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1861:18:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "1845:34:0"}, {"expression": {"id": 208, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"id": 198, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1897:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 201, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1906:8:0", "memberName": "decimals", "nodeType": "MemberAccess", "referencedDeclaration": 77, "src": "1897:17:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint8_$dyn_memory_ptr", "typeString": "uint8[] memory"}}, "id": 202, "indexExpression": {"id": 200, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 180, "src": "1915:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1897:20:0", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 204, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 192, "src": "1927:5:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 203, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 62, "src": "1920:6:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$62_$", "typeString": "type(contract IERC20)"}}, "id": 205, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1920:13:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$62", "typeString": "contract IERC20"}}, "id": 206, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1934:8:0", "memberName": "decimals", "nodeType": "MemberAccess", "referencedDeclaration": 61, "src": "1920:22:0", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint8_$", "typeString": "function () view external returns (uint8)"}}, "id": 207, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1920:24:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "src": "1897:47:0", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "id": 209, "nodeType": "ExpressionStatement", "src": "1897:47:0"}, {"expression": {"id": 220, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"id": 210, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1962:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 213, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1971:7:0", "memberName": "symbols", "nodeType": "MemberAccess", "referencedDeclaration": 71, "src": "1962:16:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_string_memory_ptr_$dyn_memory_ptr", "typeString": "string memory[] memory"}}, "id": 214, "indexExpression": {"id": 212, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 180, "src": "1979:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1962:19:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [], "expression": {"argumentTypes": [], "expression": {"arguments": [{"id": 216, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 192, "src": "1991:5:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 215, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 62, "src": "1984:6:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$62_$", "typeString": "type(contract IERC20)"}}, "id": 217, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1984:13:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$62", "typeString": "contract IERC20"}}, "id": 218, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1998:6:0", "memberName": "symbol", "nodeType": "MemberAccess", "referencedDeclaration": 56, "src": "1984:20:0", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_string_memory_ptr_$", "typeString": "function () view external returns (string memory)"}}, "id": 219, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1984:22:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "src": "1962:44:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, "id": 221, "nodeType": "ExpressionStatement", "src": "1962:44:0"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 187, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 183, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 180, "src": "1794:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"expression": {"id": 184, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "1798:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 185, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1807:6:0", "memberName": "tokens", "nodeType": "MemberAccess", "referencedDeclaration": 74, "src": "1798:15:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 186, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1814:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1798:22:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1794:26:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 223, "initializationExpression": {"assignments": [180], "declarations": [{"constant": false, "id": 180, "mutability": "mutable", "name": "j", "nameLocation": "1786:1:0", "nodeType": "VariableDeclaration", "scope": 223, "src": "1778:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 179, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1778:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 182, "initialValue": {"hexValue": "30", "id": 181, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1790:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "1778:13:0"}, "loopExpression": {"expression": {"id": 189, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": true, "src": "1822:3:0", "subExpression": {"id": 188, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 180, "src": "1824:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 190, "nodeType": "ExpressionStatement", "src": "1822:3:0"}, "nodeType": "ForStatement", "src": "1773:248:0"}, {"expression": {"id": 234, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 224, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "2034:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 226, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2043:7:0", "memberName": "weights", "nodeType": "MemberAccess", "referencedDeclaration": 68, "src": "2034:16:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"expression": {"expression": {"id": 230, "name": "options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 141, "src": "2067:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolOptions_$13_memory_ptr", "typeString": "struct PoolOptions memory"}}, "id": 231, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2075:7:0", "memberName": "weights", "nodeType": "MemberAccess", "referencedDeclaration": 10, "src": "2067:15:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_AssetWeight_$6_memory_ptr_$dyn_memory_ptr", "typeString": "struct AssetWeight memory[] memory"}}, "id": 232, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2083:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "2067:22:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 229, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "2053:13:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (uint256[] memory)"}, "typeName": {"baseType": {"id": 227, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2057:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 228, "nodeType": "ArrayTypeName", "src": "2057:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}}, "id": 233, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2053:37:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "src": "2034:56:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 235, "nodeType": "ExpressionStatement", "src": "2034:56:0"}, {"body": {"id": 260, "nodeType": "Block", "src": "2157:80:0", "statements": [{"expression": {"id": 258, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"id": 248, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "2175:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 251, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2184:7:0", "memberName": "weights", "nodeType": "MemberAccess", "referencedDeclaration": 68, "src": "2175:16:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 252, "indexExpression": {"id": 250, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2192:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2175:19:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"baseExpression": {"expression": {"id": 253, "name": "options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 141, "src": "2197:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolOptions_$13_memory_ptr", "typeString": "struct PoolOptions memory"}}, "id": 254, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2205:7:0", "memberName": "weights", "nodeType": "MemberAccess", "referencedDeclaration": 10, "src": "2197:15:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_AssetWeight_$6_memory_ptr_$dyn_memory_ptr", "typeString": "struct AssetWeight memory[] memory"}}, "id": 256, "indexExpression": {"id": 255, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2213:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2197:18:0", "typeDescriptions": {"typeIdentifier": "t_struct$_AssetWeight_$6_memory_ptr", "typeString": "struct AssetWeight memory"}}, "id": 257, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2216:6:0", "memberName": "weight", "nodeType": "MemberAccess", "referencedDeclaration": 5, "src": "2197:25:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2175:47:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 259, "nodeType": "ExpressionStatement", "src": "2175:47:0"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 244, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 240, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2124:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"expression": {"id": 241, "name": "options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 141, "src": "2128:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolOptions_$13_memory_ptr", "typeString": "struct PoolOptions memory"}}, "id": 242, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2136:7:0", "memberName": "weights", "nodeType": "MemberAccess", "referencedDeclaration": 10, "src": "2128:15:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_AssetWeight_$6_memory_ptr_$dyn_memory_ptr", "typeString": "struct AssetWeight memory[] memory"}}, "id": 243, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2144:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "2128:22:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2124:26:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 261, "initializationExpression": {"assignments": [237], "declarations": [{"constant": false, "id": 237, "mutability": "mutable", "name": "j", "nameLocation": "2117:1:0", "nodeType": "VariableDeclaration", "scope": 261, "src": "2109:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 236, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2109:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 239, "initialValue": {"hexValue": "30", "id": 238, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2121:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "2109:13:0"}, "loopExpression": {"expression": {"id": 246, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": true, "src": "2152:3:0", "subExpression": {"id": 245, "name": "j", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2154:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 247, "nodeType": "ExpressionStatement", "src": "2152:3:0"}, "nodeType": "ForStatement", "src": "2104:133:0"}, {"expression": {"id": 267, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 262, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "2250:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 264, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2259:3:0", "memberName": "fee", "nodeType": "MemberAccess", "referencedDeclaration": 82, "src": "2250:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"expression": {"id": 265, "name": "options", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 141, "src": "2265:7:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolOptions_$13_memory_ptr", "typeString": "struct PoolOptions memory"}}, "id": 266, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2273:7:0", "memberName": "swapFee", "nodeType": "MemberAccess", "referencedDeclaration": 12, "src": "2265:15:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2250:30:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 268, "nodeType": "ExpressionStatement", "src": "2250:30:0"}, {"expression": {"id": 273, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 269, "name": "allPoolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 95, "src": "2294:11:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_PoolData_$83_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory[] memory"}}, "id": 271, "indexExpression": {"id": 270, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "2306:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2294:14:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 272, "name": "poolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 123, "src": "2311:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "src": "2294:25:0", "typeDescriptions": {"typeIdentifier": "t_struct$_PoolData_$83_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory"}}, "id": 274, "nodeType": "ExpressionStatement", "src": "2294:25:0"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 111, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 108, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "1284:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 109, "name": "pools", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 88, "src": "1288:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 110, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1294:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "1288:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1284:16:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 276, "initializationExpression": {"assignments": [105], "declarations": [{"constant": false, "id": 105, "mutability": "mutable", "name": "i", "nameLocation": "1277:1:0", "nodeType": "VariableDeclaration", "scope": 276, "src": "1269:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 104, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1269:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 107, "initialValue": {"hexValue": "30", "id": 106, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1281:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "1269:13:0"}, "loopExpression": {"expression": {"id": 113, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": true, "src": "1302:3:0", "subExpression": {"id": 112, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 105, "src": "1304:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 114, "nodeType": "ExpressionStatement", "src": "1302:3:0"}, "nodeType": "ForStatement", "src": "1264:1066:0"}, {"assignments": [278], "declarations": [{"constant": false, "id": 278, "mutability": "mutable", "name": "_abiEncodedData", "nameLocation": "2683:15:0", "nodeType": "VariableDeclaration", "scope": 285, "src": "2670:28:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 277, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "2670:5:0", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 283, "initialValue": {"arguments": [{"id": 281, "name": "allPoolData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 95, "src": "2712:11:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_PoolData_$83_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory[] memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_array$_t_struct$_PoolData_$83_memory_ptr_$dyn_memory_ptr", "typeString": "struct GetBeraPoolDataBatchRequest.PoolData memory[] memory"}], "expression": {"id": 279, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "2701:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 280, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2705:6:0", "memberName": "encode", "nodeType": "MemberAccess", "src": "2701:10:0", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 282, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2701:23:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "nodeType": "VariableDeclarationStatement", "src": "2670:54:0"}, {"AST": {"nativeSrc": "2744:377:0", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2744:377:0", "statements": [{"nativeSrc": "3013:43:0", "nodeType": "YulVariableDeclaration", "src": "3013:43:0", "value": {"arguments": [{"name": "_abiEncodedData", "nativeSrc": "3034:15:0", "nodeType": "YulIdentifier", "src": "3034:15:0"}, {"kind": "number", "nativeSrc": "3051:4:0", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3051:4:0", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "3030:3:0", "nodeType": "YulIdentifier", "src": "3030:3:0"}, "nativeSrc": "3030:26:0", "nodeType": "YulFunctionCall", "src": "3030:26:0"}, "variables": [{"name": "dataStart", "nativeSrc": "3017:9:0", "nodeType": "YulTypedName", "src": "3017:9:0", "type": ""}]}, {"expression": {"arguments": [{"name": "dataStart", "nativeSrc": "3076:9:0", "nodeType": "YulIdentifier", "src": "3076:9:0"}, {"arguments": [{"arguments": [], "functionName": {"name": "msize", "nativeSrc": "3091:5:0", "nodeType": "YulIdentifier", "src": "3091:5:0"}, "nativeSrc": "3091:7:0", "nodeType": "YulFunctionCall", "src": "3091:7:0"}, {"name": "dataStart", "nativeSrc": "3100:9:0", "nodeType": "YulIdentifier", "src": "3100:9:0"}], "functionName": {"name": "sub", "nativeSrc": "3087:3:0", "nodeType": "YulIdentifier", "src": "3087:3:0"}, "nativeSrc": "3087:23:0", "nodeType": "YulFunctionCall", "src": "3087:23:0"}], "functionName": {"name": "return", "nativeSrc": "3069:6:0", "nodeType": "YulIdentifier", "src": "3069:6:0"}, "nativeSrc": "3069:42:0", "nodeType": "YulFunctionCall", "src": "3069:42:0"}, "nativeSrc": "3069:42:0", "nodeType": "YulExpressionStatement", "src": "3069:42:0"}]}, "evmVersion": "paris", "externalReferences": [{"declaration": 278, "isOffset": false, "isSlot": false, "src": "3034:15:0", "valueSize": 1}], "id": 284, "nodeType": "InlineAssembly", "src": "2735:386:0"}]}, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 89, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 85, "mutability": "mutable", "name": "router", "nameLocation": "1150:6:0", "nodeType": "VariableDeclaration", "scope": 286, "src": "1142:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 84, "name": "address", "nodeType": "ElementaryTypeName", "src": "1142:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 88, "mutability": "mutable", "name": "pools", "nameLocation": "1175:5:0", "nodeType": "VariableDeclaration", "scope": 286, "src": "1158:22:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 86, "name": "address", "nodeType": "ElementaryTypeName", "src": "1158:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 87, "nodeType": "ArrayTypeName", "src": "1158:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "1141:40:0"}, "returnParameters": {"id": 90, "nodeType": "ParameterList", "parameters": [], "src": "1182:0:0"}, "scope": 307, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 306, "nodeType": "FunctionDefinition", "src": "3134:193:0", "nodes": [], "body": {"id": 305, "nodeType": "Block", "src": "3203:124:0", "nodes": [], "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 297, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"expression": {"id": 293, "name": "target", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 288, "src": "3217:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 294, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3224:4:0", "memberName": "code", "nodeType": "MemberAccess", "src": "3217:11:0", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 295, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3229:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "3217:18:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 296, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3239:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3217:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 303, "nodeType": "Block", "src": "3284:37:0", "statements": [{"expression": {"hexValue": "66616c7365", "id": 301, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3305:5:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "functionReturnParameters": 292, "id": 302, "nodeType": "Return", "src": "3298:12:0"}]}, "id": 304, "nodeType": "IfStatement", "src": "3213:108:0", "trueBody": {"id": 300, "nodeType": "Block", "src": "3242:36:0", "statements": [{"expression": {"hexValue": "74727565", "id": 298, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3263:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 292, "id": 299, "nodeType": "Return", "src": "3256:11:0"}]}}]}, "implemented": true, "kind": "function", "modifiers": [], "name": "codeSizeIsZero", "nameLocation": "3143:14:0", "parameters": {"id": 289, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 288, "mutability": "mutable", "name": "target", "nameLocation": "3166:6:0", "nodeType": "VariableDeclaration", "scope": 306, "src": "3158:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 287, "name": "address", "nodeType": "ElementaryTypeName", "src": "3158:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3157:16:0"}, "returnParameters": {"id": 292, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 291, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 306, "src": "3197:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 290, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3197:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3196:6:0"}, "scope": 307, "stateMutability": "view", "virtual": false, "visibility": "internal"}], "abstract": false, "baseContracts": [], "canonicalName": "GetBeraPoolDataBatchRequest", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 63, "nodeType": "StructuredDocumentation", "src": "751:130:0", "text": "@dev This contract is not meant to be deployed. Instead, use a static call with the\ndeployment bytecode as payload."}, "fullyImplemented": true, "linearizedBaseContracts": [307], "name": "GetBeraPoolDataBatchRequest", "nameLocation": "891:27:0", "scope": 308, "usedErrors": [], "usedEvents": []}], "license": "MIT"}, "id": 0}