use serde::{Deserialize, Serialize};

use crate::vira::{pool::{<PERSON><PERSON><PERSON>, PoolData, POOL}, util};



#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default, Serialize, Deserialize)]
pub struct BeraPool {
    pub data : PoolData,
}

impl BeraPool {
    pub fn get_sub_pools(&self) -> Vec<POOL> {
        let mut pools: Vec<POOL> = vec![];
        let data = self.data();

        //所有组合
        let all_sets = util::all_sets(&data.tokens);

        for tokens in all_sets {
            let mut d = self.clone();
            let mut token0 = tokens[0].clone();
            let mut token1 = tokens[1].clone();
            //balancer根据weight修正子pool的reserve
            if !token0.weight.is_zero() {
                if token0.weight < token1.weight {
                    token1.reserve = token1.reserve * token0.weight / token1.weight;
                } else if token0.weight > token1.weight {
                    token0.reserve = token0.reserve * token1.weight / token0.weight;
                }
            }
            let data_mut = d.data_mut();
            //data_mut.addr = format!("{}_{}_{}", data.addr, token0.index, token1.index);
            data_mut.tokens = vec![token0, token1];
            //TODO: 需要改成balancer，暂时隐藏
            //pools.push(POOL::BeraPool(d));
        }
        pools
    }    
}

//main pool && virtual pool
impl DexPool for BeraPool {
    fn data(&self) -> &PoolData {
        &self.data
    }

    fn data_mut(&mut self) -> &mut PoolData {
        &mut self.data
    }

}