use alloy::primitives::U256;

// commonly used U256s
pub const _U256_0X100000000: U256 = U256::from_limbs([4294967296, 0, 0, 0]);
pub const _U256_0X10000: U256 = U256::from_limbs([65536, 0, 0, 0]);
pub const _U256_0X100: U256 = U256::from_limbs([256, 0, 0, 0]);

pub const U256_10000: U256 = U256::from_limbs([10000, 0, 0, 0]);
pub const U256_1000: U256 = U256::from_limbs([1000, 0, 0, 0]);
pub const U256_618: U256 = U256::from_limbs([618, 0, 0, 0]);
pub const U256_382: U256 = U256::from_limbs([382, 0, 0, 0]);

// MAX_UINT112 = 5192296858534827628530496329220095
pub const UINT112_MAX: U256 = U256::from_limbs([
    5192296858534827628,
    530496329220095,
    0,
    0,
]);

pub const _U256_255: U256 = U256::from_limbs([255, 0, 0, 0]);
pub const _U256_192: U256 = U256::from_limbs([192, 0, 0, 0]);
pub const _U256_191: U256 = U256::from_limbs([191, 0, 0, 0]);
pub const _U256_128: U256 = U256::from_limbs([128, 0, 0, 0]);
pub const U_100: U256 = U256::from_limbs([100, 0, 0, 0]);
pub const U_60: U256 = U256::from_limbs([60, 0, 0, 0]);
pub const _U256_64: U256 = U256::from_limbs([64, 0, 0, 0]);
pub const _U256_32: U256 = U256::from_limbs([32, 0, 0, 0]);
pub const _U256_16: U256 = U256::from_limbs([16, 0, 0, 0]);
pub const _U256_8: U256 = U256::from_limbs([8, 0, 0, 0]);
pub const _U256_4: U256 = U256::from_limbs([4, 0, 0, 0]);
pub const U_2: U256 = U256::from_limbs([2, 0, 0, 0]);
pub const _U256_1: U256 = U256::from_limbs([1, 0, 0, 0]);
pub const U_MAX_U112: U256 = U256::from_limbs([u64::MAX, (1u64 << 48) - 1, 0, 0]);


// Fee constants
pub const U_666666: U256 = U256::from_limbs([666666, 0, 0, 0]);
pub const U_900004: U256 = U256::from_limbs([900004, 0, 0, 0]);
pub const U_900005: U256 = U256::from_limbs([900005, 0, 0, 0]);
pub const U_100000: U256 = U256::from_limbs([100000, 0, 0, 0]);
pub const U_2000: U256 = U256::from_limbs([2000, 0, 0, 0]);
pub const U_ZERO: U256 = U256::from_limbs([0, 0, 0, 0]);



// Batch processing constants
pub const BATCH_SIZE: usize = 50;

// Uniswap V3 specific
pub const _POPULATE_TICK_DATA_STEP: u64 = 100000;
pub const _Q128: U256 = U256::from_limbs([0, 0, 1, 0]);
pub const _Q224: U256 = U256::from_limbs([0, 0, 0, 4294967296]);

// Others
pub const _U128_0X10000000000000000: u128 = 18446744073709551616;
pub const _U256_0XFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF: U256 = U256::from_limbs([
    18446744073709551615,
    18446744073709551615,
    18446744073709551615,
    0,
]);
pub const _U256_0XFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF: U256 =
    U256::from_limbs([18446744073709551615, 18446744073709551615, 0, 0]);

pub const UPDATE_BALANCE_RETRY : usize = 3;


// 测试常量
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_u256_constants() {
        // 测试基本常量
        assert_eq!(U_100, U256::from(100));
        assert_eq!(U_60, U256::from(60));
        assert_eq!(U_2, U256::from(2));

        // 测试黄金分割相关常量
        assert_eq!(U256_382, U256::from(382));
        assert_eq!(U256_1000, U256::from(1000));
        
        // 测试16进制常量
        assert_eq!(_U256_0X10000, U256::from(65536));
    }
}


// MEV批量检查常量
pub const MEV_BATCH_SIZE: usize = 600;    // 每批处理的MEV数量，对应TypeScript中的step = 25
pub const MEV_BACKUP_INTERVAL: usize = 2000; // 每处理多少个池子备份一次

// 黄金分割求值的最大步骤 Algorithm constants
pub const MAX_GOLDEN_STEPS: u8 = 40;

pub const GET_LOGS_TIMEOUT: std::time::Duration = std::time::Duration::from_secs(10);