
use alloy::signers::local::LocalSigner;
use serde_json::json;

// 使用ethers创建账号，打印每个账号的address和私钥, 组合成json格式
pub fn _batch_new(num: u32) {
    let mut accounts = Vec::new();
    for i in 0..num {
        let wallet = LocalSigner::random();
        let key = hex::encode(wallet.to_bytes());
        let addr = wallet.address();

        println!("({:?}) 钱包公钥: {:?}", i, addr);
        println!("({:?}) 钱包私钥: {}", i, key);
        
        let account_json = json!({
            "id" : i,
            "addr": addr,
            "key": key,
        });
        accounts.push(account_json);
    }

    let accounts_json = serde_json::to_string(&accounts).unwrap();
    println!("Create accounts: {}", accounts_json);
}

#[cfg(test)]
mod test {
    use super::*;
    #[test]
    fn test_batch_new(){
        _batch_new(10);
    }

}