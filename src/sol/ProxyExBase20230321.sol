// SPDX-License-Identifier: UNLICENSED

//import '@uniswap/lib/contracts/libraries/Babylonian.sol';
//import './FullMathV3.sol';

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Address.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

pragma solidity ^0.8.0;
interface IPair {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function swap(uint amount0Out, uint amount1Out, address to, bytes calldata data) external;
}

pragma solidity ^0.8.0;
interface IPairV1 {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1);
    function swap(uint amount0Out, uint amount1Out, address to) external;
}

pragma solidity  ^0.8.0;
interface IRouter {
    function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts);
    function swapExactTokensForTokensSupportingFeeOnTransferTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external;
}


pragma solidity ^0.8.0;

// File: @openzeppelin/contracts/token/ERC20/IERC20.sol
/**
 * @dev Interface of the ERC20 standard as defined in the EIP.
 */
interface IERC20 {
    /**
     * @dev Returns the amount of tokens in existence.
     */
    function totalSupply() external view returns (uint256);

    /**
     * @dev Returns the amount of tokens owned by `account`.
     */
    function balanceOf(address account) external view returns (uint256);

    /**
     * @dev Moves `amount` tokens from the caller's account to `recipient`.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transfer(address recipient, uint256 amount) external returns (bool);

    /**
     * @dev Returns the remaining number of tokens that `spender` will be
     * allowed to spend on behalf of `owner` through {transferFrom}. This is
     * zero by default.
     *
     * This value changes when {approve} or {transferFrom} are called.
     */
    function allowance(address owner, address spender) external view returns (uint256);

    /**
     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * IMPORTANT: Beware that changing an allowance with this method brings the risk
     * that someone may use both the old and the new allowance by unfortunate
     * transaction ordering. One possible solution to mitigate this race
     * condition is to first reduce the spender's allowance to 0 and set the
     * desired value afterwards:
     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729
     *
     * Emits an {Approval} event.
     */
    function approve(address spender, uint256 amount) external returns (bool);

    /**
     * @dev Moves `amount` tokens from `sender` to `recipient` using the
     * allowance mechanism. `amount` is then deducted from the caller's
     * allowance.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);

    /**
     * @dev Emitted when `value` tokens are moved from one account (`from`) to
     * another (`to`).
     *
     * Note that `value` may be zero.
     */

    function withdraw(uint wad) external;
    function deposit() external;

    event Transfer(address indexed from, address indexed to, uint256 value);

    /**
     * @dev Emitted when the allowance of a `spender` for an `owner` is set by
     * a call to {approve}. `value` is the new allowance.
     */
    event Approval(address indexed owner, address indexed spender, uint256 value);
}


// File: @openzeppelin/contracts/access/Roles.sol
pragma solidity ^0.8.0;

library Roles {
    struct Role {
        mapping (address => bool) bearer;
    }

    function add(Role storage role, address account) internal {
        require(!has(role, account), "Roles: account already has role");
        role.bearer[account] = true;
    }

    function remove(Role storage role, address account) internal {
        require(has(role, account), "Roles: account does not have role");
        role.bearer[account] = false;
    }

    function has(Role storage role, address account) internal view returns (bool) {
        require(account != address(0), "Roles: account is the zero address");
        return role.bearer[account];
    }

}

pragma solidity ^0.8.0;
pragma experimental ABIEncoderV2;
contract ProxyEx is Ownable {
    using SafeMath for uint256;
    using Roles for Roles.Role;
    
    mapping (address => PairDataLocal) pairDatas;
    Roles.Role private _operators;
    address public logicContact;
    address public uniswapV2Router;
    
    event Reward(uint256, uint256, uint256);
    event MayFail(uint256); //0:success 1:not enough output 2:whale fail

    struct FeedList {
        address addr;
        uint256 min;
        uint256 max;
        uint256 balance;
        uint256 feedAmount;
    }

    struct Whale {
        address addr;
        address token;
        uint256 amount;
        bool isEth;
        bool isGreater;

        address spender; //check allowance
        address spenderToken;
        uint256 spenderAmount;
    }
    struct SwapData {
        address tokenIn;
        uint256 sumIn;
        uint256[] amountsIn;
        address[][] pairs;
        uint256 minOut;
    }

    struct PairDataLocal {
        address token0;
        address token1;
        uint256 r0;
        uint256 r1;
        //uint256 fee0;
        //uint256 fee1;        
    }

    struct PairData {
        address addr;
        uint256 version;
        uint256 fee0;
        uint256 fee1;
    }

    struct BalanceDesc {
        uint maxIn;

        uint sum;
        uint sumAfter;

        bool redo;
        bool outOfbalance;
    }
    
    constructor() {
        _operators.add(msg.sender);
    }

    //operators
    function addOpts(address[] memory operators) public onlyOwner {
        for (uint256 i = 0; i < operators.length; ++i) {
            if(!isOperator(operators[i])) _operators.add(operators[i]);
        }
    }

    function removeOperators(address operator) public onlyOwner{
        _operators.remove(operator);
    }
    
    function isOperator(address operator) public view returns (bool) {
        return _operators.has(operator);
    }
    
    modifier onlyOperator() {
        require(_operators.has(msg.sender), "Operators: caller is not the Operator");
        _;
    }

    //functions
    function getBalance(address token) public view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    function getBalanceOf(address holder, address token) public view returns (uint256) {
        return IERC20(token).balanceOf(holder);
    }

    function withdrawToEx(address token, address to, uint256 amount) public onlyOwner {
        IERC20(token).transfer(to, amount);
    }

    function rebBalanceEth(address weth1, address weth2, uint256 amount) public onlyOwner {
        //IERC20(eth1)
    }
    
    function withdrawAllEx(address token) public onlyOwner {
        IERC20(token).transfer(msg.sender, IERC20(token).balanceOf(address(this)));
    }

    function withdrawAllToEx(address[] memory tokens, address to) public onlyOwner {
        for(uint256 i; i < tokens.length; i++){
            IERC20 token = IERC20(tokens[i]);
            uint256 balance = token.balanceOf(address(this));
            if(balance > 0)
                token.transfer(to, balance);
        }
    }

    function withdrawAllEthEx() public payable onlyOwner{
        payable(msg.sender).transfer(address(this).balance);
    }

    //contact manager
    function setLogicContact(address _new) public onlyOwner {
        logicContact = _new;
    }
    function setRouter(address _new) public onlyOperator {
        uniswapV2Router = _new;
    }



    // babylonian method (https://en.wikipedia.org/wiki/Methods_of_computing_square_roots#Babylonian_method)
    function sqrt(uint y) internal pure returns (uint z) {
        if (y > 3) {
            z = y;
            uint x = y / 2 + 1;
            while (x < z) {
                z = x;
                x = (y / x + x) / 2;
            }
        } else if (y != 0) {
            z = 1;
        }
    }

    function verifyWhale(Whale memory whale) view public returns (bool)
    {
        if(whale.addr == address(0)) return true;
        if(whale.addr != address(0)){
            uint256 balance = whale.isEth ? whale.addr.balance :  IERC20(whale.token).balanceOf(whale.addr);
            if(whale.isGreater && balance >= whale.amount && balance > 0) return true;
            if(!whale.isGreater && balance <= whale.amount) return true;
        }
        if(whale.spender != address(0)){
            //check allowance
            if(IERC20(whale.spenderToken).allowance(whale.addr, whale.spender) >= whale.spenderAmount) return true;
        }
        return false;
    }

    function sortTokens(address tokenA, address tokenB) internal pure returns (address token0, address token1) {
        require(tokenA != tokenB, 'KKK: IDENTICAL_ADDRESSES');
        (token0, token1) = tokenA < tokenB ? (tokenA, tokenB) : (tokenB, tokenA);
        require(token0 != address(0), 'KKK: ZERO_ADDRESS');
    }

    function getAmountOut(uint amountIn, uint reserveIn, uint reserveOut) internal pure returns (uint amountOut) {
        require(amountIn > 0, 'KKK: INSUFFICIENT_INPUT_AMOUNT');
        require(reserveIn > 0 && reserveOut > 0, 'KKK: INSUFFICIENT_LIQUIDITY');
        uint amountInWithFee = amountIn.mul(997);
        uint numerator = amountInWithFee.mul(reserveOut);
        uint denominator = reserveIn.mul(1000).add(amountInWithFee);
        amountOut = numerator / denominator;
    }

    function getAmountOutWithFee(uint amountIn, uint reserveIn, uint reserveOut, uint fee) internal pure returns (uint amountOut) {
        uint amountInWithFee = amountIn.mul(fee);
        uint numerator = amountInWithFee.mul(reserveOut);
        uint denominator = reserveIn.mul(100000).add(amountInWithFee);
        amountOut = numerator / denominator;
    }

    function _delegate(address implementation) public payable onlyOperator {
        require(implementation != address(0), "error logic address");
        assembly {
          calldatacopy(0, 0, calldatasize())
          let result := delegatecall(gas(), implementation, 0, calldatasize(), 0, 0)
          returndatacopy(0, 0, returndatasize())

          switch result
          case 0 { revert(0, returndatasize()) }
          default { return(0, returndatasize()) }
        }
    }

    receive() external payable {}

    fallback() external payable {
        _delegate(logicContact);
    }

}
