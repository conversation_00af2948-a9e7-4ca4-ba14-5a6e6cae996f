// SPDX-License-Identifier: UNLICENSED

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Address.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

pragma solidity  ^0.8.0;
interface IPair {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function swap(uint amount0Out, uint amount1Out, address to, bytes calldata data) external;
}

pragma solidity  ^0.8.0;
interface IPairV1 {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1);
    function swap(uint amount0Out, uint amount1Out, address to) external;
}

pragma solidity  ^0.8.0;
interface IRouter {
    function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts);
    function swapExactTokensForTokensSupportingFeeOnTransferTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external;
}



pragma solidity  ^0.8.0;

// File: @openzeppelin/contracts/token/ERC20/IERC20.sol
/**
 * @dev Interface of the ERC20 standard as defined in the EIP.
 */
interface IERC20 {
    /**
     * @dev Returns the amount of tokens in existence.
     */
    function totalSupply() external view returns (uint256);

    /**
     * @dev Returns the amount of tokens owned by `account`.
     */
    function balanceOf(address account) external view returns (uint256);

    /**
     * @dev Moves `amount` tokens from the caller's account to `recipient`.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transfer(address recipient, uint256 amount) external returns (bool);

    /**
     * @dev Returns the remaining number of tokens that `spender` will be
     * allowed to spend on behalf of `owner` through {transferFrom}. This is
     * zero by default.
     *
     * This value changes when {approve} or {transferFrom} are called.
     */
    function allowance(address owner, address spender) external view returns (uint256);

    /**
     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * IMPORTANT: Beware that changing an allowance with this method brings the risk
     * that someone may use both the old and the new allowance by unfortunate
     * transaction ordering. One possible solution to mitigate this race
     * condition is to first reduce the spender's allowance to 0 and set the
     * desired value afterwards:
     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729
     *
     * Emits an {Approval} event.
     */
    function approve(address spender, uint256 amount) external returns (bool);

    /**
     * @dev Moves `amount` tokens from `sender` to `recipient` using the
     * allowance mechanism. `amount` is then deducted from the caller's
     * allowance.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);

    /**
     * @dev Emitted when `value` tokens are moved from one account (`from`) to
     * another (`to`).
     *
     * Note that `value` may be zero.
     */

    function withdraw(uint wad) external;

    event Transfer(address indexed from, address indexed to, uint256 value);

    /**
     * @dev Emitted when the allowance of a `spender` for an `owner` is set by
     * a call to {approve}. `value` is the new allowance.
     */
    event Approval(address indexed owner, address indexed spender, uint256 value);
}


// File: @openzeppelin/contracts/access/Roles.sol
pragma solidity  ^0.8.0;

library Roles {
    struct Role {
        mapping (address => bool) bearer;
    }

    function add(Role storage role, address account) internal {
        require(!has(role, account), "Roles: account already has role");
        role.bearer[account] = true;
    }

    function remove(Role storage role, address account) internal {
        require(has(role, account), "Roles: account does not have role");
        role.bearer[account] = false;
    }

    function has(Role storage role, address account) internal view returns (bool) {
        require(account != address(0), "Roles: account is the zero address");
        return role.bearer[account];
    }

}

pragma solidity  ^0.8.0;
pragma experimental ABIEncoderV2;
contract ProxyEx is Ownable {
    using SafeMath for uint256;
    using Roles for Roles.Role;
    
    mapping (address => PairDataLocal) pairDatas;
    //uint256 public currentBalance;
    Roles.Role private _operators;
    address public logicContact;
    address public uniswapV2Router;
    
    event Reward(uint256, uint256, uint256);
    event MayFail(uint256); //0:success 1:not enough output 2:whale fail

    struct FeedList {
        address addr;
        uint256 min;
        uint256 max;
        uint256 balance;
        uint256 feedAmount;
    }

    struct Whale {
        address addr;
        address token;
        uint256 amount;
        bool isEth;
        bool isGreater;

        address spender; //check allowance
        address spenderToken;
        uint256 spenderAmount;
    }
    struct SwapData {
        address tokenIn;
        uint256 sumIn;
        uint256[] amountsIn;
        address[][] pairs;
        uint256 minOut;
    }

    struct PairDataLocal {
        address token0;
        address token1;
        uint256 r0;
        uint256 r1;
        //uint256 fee0;
        //uint256 fee1;        
    }

    struct PairData {
        address addr;
        uint256 version;
        uint256 fee0;
        uint256 fee1;
    }

    struct BalanceDesc {
        uint maxIn;

        uint sum;
        uint sumAfter;

        bool redo;
        bool outOfbalance;
    }
    
    constructor() {
        _operators.add(msg.sender);
    }

    //operators
    function addOpts(address[] memory operators) public onlyOwner {
        for (uint256 i = 0; i < operators.length; ++i) {
            if(!isOperator(operators[i])) _operators.add(operators[i]);
        }
    }

    function removeOperators(address operator) public onlyOwner{
        _operators.remove(operator);
    }
    
    function isOperator(address operator) public view returns (bool) {
        return _operators.has(operator);
    }
    
    modifier onlyOperator() {
        require(_operators.has(msg.sender), "Operators: caller is not the Operator");
        _;
    }

    //functions
    function getBalance(address token) public view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    function getBalanceOf(address holder, address token) public view returns (uint256) {
        return IERC20(token).balanceOf(holder);
    }

    function withdrawToEx(address token, address to, uint256 amount) public onlyOwner {
        IERC20(token).transfer(to, amount);
    }
    
    function withdrawAllEx(address token) public onlyOwner {
        IERC20(token).transfer(msg.sender, IERC20(token).balanceOf(address(this)));
    }

    function withdrawAllToEx(address[] memory tokens, address to) public onlyOwner {
        for(uint256 i; i < tokens.length; i++){
            IERC20 token = IERC20(tokens[i]);
            uint256 balance = token.balanceOf(address(this));
            if(balance > 0)
                token.transfer(to, balance);
        }
    }

    function withdrawAllEthEx() public payable onlyOwner{
        payable(msg.sender).transfer(address(this).balance);
    }

    //contact manager
    function setLogicContact(address _new) public onlyOwner {
        logicContact = _new;
    }

    function getLoginContact() public onlyOwner view returns (address) {
        return logicContact;
    }



    // babylonian method (https://en.wikipedia.org/wiki/Methods_of_computing_square_roots#Babylonian_method)
    function sqrt(uint y) internal pure returns (uint z) {
        if (y > 3) {
            z = y;
            uint x = y / 2 + 1;
            while (x < z) {
                z = x;
                x = (y / x + x) / 2;
            }
        } else if (y != 0) {
            z = 1;
        }
    }

    function verifyWhale(Whale memory whale) view public returns (bool)
    {
        if(whale.addr == address(0)) return true;
        if(whale.addr != address(0)){
            uint256 balance = whale.isEth ? whale.addr.balance :  IERC20(whale.token).balanceOf(whale.addr);
            if(whale.isGreater && balance >= whale.amount && balance > 0) return true;
            if(!whale.isGreater && balance <= whale.amount) return true;
        }
        if(whale.spender != address(0)){
            //check allowance
            if(IERC20(whale.spenderToken).allowance(whale.addr, whale.spender) >= whale.spenderAmount) return true;
        }
        return false;
    }

    function sortTokens(address tokenA, address tokenB) internal pure returns (address token0, address token1) {
        require(tokenA != tokenB, 'KKK: IDENTICAL_ADDRESSES');
        (token0, token1) = tokenA < tokenB ? (tokenA, tokenB) : (tokenB, tokenA);
        require(token0 != address(0), 'KKK: ZERO_ADDRESS');
    }

    function getAmountOut(uint amountIn, uint reserveIn, uint reserveOut) internal pure returns (uint amountOut) {
        require(amountIn > 0, 'KKK: INSUFFICIENT_INPUT_AMOUNT');
        require(reserveIn > 0 && reserveOut > 0, 'KKK: INSUFFICIENT_LIQUIDITY');
        uint amountInWithFee = amountIn.mul(997);
        uint numerator = amountInWithFee.mul(reserveOut);
        uint denominator = reserveIn.mul(1000).add(amountInWithFee);
        amountOut = numerator / denominator;
    }

    function getAmountOutWithFee(uint amountIn, uint reserveIn, uint reserveOut, uint fee) internal pure returns (uint amountOut) {
        uint amountInWithFee = amountIn.mul(fee);
        uint numerator = amountInWithFee.mul(reserveOut);
        uint denominator = reserveIn.mul(100000).add(amountInWithFee);
        amountOut = numerator / denominator;
    }
    //save gas
    function getAmountsOutByPair(address tokenIn, uint256 amountIn, PairData[] memory pairs)
        internal returns (uint256[] memory amounts, address tokenOut){
        uint fee;
        amounts = new uint256[](pairs.length+1);
        amounts[0] = amountIn;
        for (uint i = 0; i < pairs.length; i++) {
            if(i > 0) tokenIn = tokenOut;
            if(amounts[i] == 0){
                amounts[i + 1] = 0;
            } else {
                PairDataLocal memory p = getPair(pairs[i].addr);
                (tokenOut, fee) = tokenIn == p.token0 ? (p.token1, pairs[i].fee0) : (p.token0, pairs[i].fee1);
                (address token0,) = sortTokens(tokenIn, tokenOut);
                (uint256 reserveIn, uint256 reserveOut) = tokenIn == token0 ? (p.r0, p.r1) : (p.r1, p.r0);
                amounts[i + 1] = getAmountOutWithFee(amounts[i], reserveIn, reserveOut, fee);
            }
        }
    }

    function getAmountsOutByPairOnline(address tokenIn, uint256 amountIn, PairData[] memory pairs)
        public view returns (uint256[] memory amounts, address tokenOut)
    {
        uint fee;
        amounts = new uint256[](pairs.length+1);
        amounts[0] = amountIn;
        for (uint i = 0; i < pairs.length; i++) {
            if(i > 0) tokenIn = tokenOut;
            if(amounts[i] == 0){
                amounts[i + 1] = 0;
            } else {
                (tokenOut, fee) = tokenIn == IPair(pairs[i].addr).token0() ? (IPair(pairs[i].addr).token1(), pairs[i].fee0) : (IPair(pairs[i].addr).token0(), pairs[i].fee1);
                (address token0,) = sortTokens(tokenIn, tokenOut);
                (uint256 reserve0, uint256 reserve1) = getReserves(pairs[i].addr);
                (uint256 reserveIn, uint256 reserveOut) = tokenIn == token0 ? (reserve0, reserve1) : (reserve1, reserve0);
                amounts[i + 1] = getAmountOutWithFee(amounts[i], reserveIn, reserveOut, fee);
            }
        }
    }

    function getAmountsExSmart(address _tokenIn, uint256 _sumIn, uint256[] memory _amountsIn, PairData[][] memory _pairs)
        internal returns (uint256[][] memory amountsSmart, uint256 sumOut, bool isMulti, address tokenOut)
    {
        (uint256[] memory singleOuts, address _tokenOut) = getAmountsOutByPair(_tokenIn, _sumIn, _pairs[0]);
        tokenOut = _tokenOut;
        if(_amountsIn.length == 1){
            amountsSmart = new uint256[][](1);
            amountsSmart[0] = singleOuts;
            sumOut = singleOuts[singleOuts.length-1];
            isMulti = false;
        } else {
            uint[][] memory amountsMulti = new uint[][](_pairs.length);
            uint sum3;
            for(uint i; i< _amountsIn.length;i++){
                (uint[] memory amounts,) = getAmountsOutByPair(_tokenIn, _amountsIn[i], _pairs[i]);

                amountsMulti[i] = amounts;
                sum3 += amounts[amounts.length-1];
            }

            if(sum3 > singleOuts[singleOuts.length-1]){
                amountsSmart = new uint256[][](amountsMulti.length);
                amountsSmart = amountsMulti;
                sumOut = sum3;
                isMulti = true;
            } else {
                amountsSmart = new uint256[][](1);
                amountsSmart[0] = singleOuts;
                sumOut = singleOuts[singleOuts.length-1];
                isMulti = false;
            }
        }
    }
    //查询用 //改成bytes[] calldata pumps提升签名速度
    function getAmountsExSmartOnline(address _tokenIn, uint256 _sumIn, uint256[] memory _amountsIn, PairData[][] memory _pairs)
        public view returns (uint256[][] memory amountsSmart, uint256 sumOut, bool isMulti, address tokenOut)
    {
        (uint256[] memory singleOuts, address _tokenOut) = getAmountsOutByPairOnline(_tokenIn, _sumIn, _pairs[0]);
        tokenOut = _tokenOut;
        if(_amountsIn.length == 1){
            amountsSmart = new uint256[][](1);
            amountsSmart[0] = singleOuts;
            sumOut = singleOuts[singleOuts.length-1];
            isMulti = false;
        } else {
            uint[][] memory amountsMulti = new uint[][](_pairs.length);
            uint sum3;
            for(uint i; i< _amountsIn.length;i++){
                (uint[] memory amounts,) = getAmountsOutByPairOnline(_tokenIn, _amountsIn[i], _pairs[i]);

                amountsMulti[i] = amounts;
                sum3 += amounts[amounts.length-1];
            }

            if(sum3 > singleOuts[singleOuts.length-1]){
                amountsSmart = new uint256[][](amountsMulti.length);
                amountsSmart = amountsMulti;
                sumOut = sum3;
                isMulti = true;
            } else {
                amountsSmart = new uint256[][](1);
                amountsSmart[0] = singleOuts;
                sumOut = singleOuts[singleOuts.length-1];
                isMulti = false;
            }
        }
    }

    function decodePairDatas(bytes memory datas) internal pure returns (PairData[] memory d) {
        uint length =  (datas.length / 27);
        d = new PairData[](length);
        for(uint i = 0 ; i < length; i++){
            uint8 version;
            uint24 fee0;
            uint24 fee1;
            address addr;
            assembly {
                version := mload(add(datas, add(1, mul(i,27))))
                fee0    := mload(add(datas, add(4, mul(i,27))))
                fee1    := mload(add(datas, add(7, mul(i,27))))
                addr    := mload(add(datas, add(27, mul(i,27))))
            }
            d[i] = PairData(addr, version, fee0, fee1);
        }
    }

    function _swap(address input, uint256[] memory amounts, PairData[] memory pairs) internal returns (address output)
    {
        if(pairs[0].version != 3) IERC20(input).transfer(pairs[0].addr, amounts[0]);
        uint fee;
        uint amountOut;
        uint amountIn;
        for (uint256 i; i < pairs.length; i++) {
            if(i > 0) input = output;
            (address token0, address token1) = getPairToken(pairs[i].addr);
            (output, fee) = input == token0 ? (token1, pairs[i].fee0) : (token0, pairs[i].fee1);

            if(amounts[i + 1] == 0) continue;
            (uint amount0Out, uint amount1Out) = input == token0 ? (uint(0), amounts[i + 1]) : (amounts[i + 1], uint(0));
            address to =  (i >= pairs.length - 1 || pairs[i+1].version == 3) ? address(this) : pairs[i+1].addr;

            if(i < pairs.length - 1 && pairs[i+1].version == 3){
                if(pairs[i].version == 3) amountIn = amountOut;
                amountOut = IERC20(output).balanceOf(address(this));
            }
            if(pairs[i].version == 1){
                IPairV1(pairs[i].addr).swap(amount0Out, amount1Out, to);
            } else if(pairs[i].version == 2){
                IPair(pairs[i].addr).swap(amount0Out, amount1Out, to, new bytes(0));
            } else if(pairs[i].version == 3){
                //use jswap
                address[] memory path = new address[](2);
                path[0] = input;
                path[1] = output;
                if(IERC20(input).allowance(address(this), uniswapV2Router) < amountIn) IERC20(input).approve(uniswapV2Router, amountIn);
                IRouter(uniswapV2Router).swapExactTokensForTokensSupportingFeeOnTransferTokens(amountIn, amounts[i+1], path, to, block.timestamp + 100);
            } else {
                revert("error version");
            }
            if(i < pairs.length - 1 && pairs[i+1].version == 3) amountOut = IERC20(output).balanceOf(address(this)) - amountOut;
        }
    }

    function _swapMulti(address tokenIn, uint256[][] memory amountsMulti, PairData[][] memory pairs) public onlyOperator {
        for(uint256 i ; i < amountsMulti.length ; i++){
            if(amountsMulti[i][0] > 0){
                _swap(tokenIn, amountsMulti[i], pairs[i]);
            }
        }
    }

    function getPair(address addr) internal returns (PairDataLocal memory data) {
        if(pairDatas[addr].token0 == address(0)){
            //写入缓存
            data.token0 = IPair(addr).token0();
            data.token1 = IPair(addr).token1();
            pairDatas[addr] = data;
        } else {
            data.token0 = pairDatas[addr].token0;
            data.token1 = pairDatas[addr].token1;
        }
        (data.r0, data.r1) = getReserves(addr);
    }

    function getPairToken(address addr) internal returns (address token0, address token1) {
        if(pairDatas[addr].token0 == address(0)){
            token0 = IPair(addr).token0();
            token1 = IPair(addr).token1();
            pairDatas[addr] = PairDataLocal(token0, token1, uint(0), uint(0));
        } else {
            token0 = pairDatas[addr].token0;
            token1 = pairDatas[addr].token1;
        }
    }

    //兼容v1 v2
    function getReserves(address addr) internal view returns (uint112 r0, uint112 r1) {
        (r0, r1) = IPairV1(addr).getReserves();
    }

    function checkPair(address tokenIn, uint256 amountIn, bytes calldata datas) public onlyOperator {
        PairData[] memory pairs = decodePairDatas(datas);
        (uint256[] memory amounts, ) = getAmountsOutByPair(tokenIn, amountIn, pairs);
        _swap(tokenIn, amounts, pairs);
    }

    function checkPairInOut(address tokenIn, uint256 amountIn, bytes calldata datasIn, bytes calldata datasOut) public onlyOperator {
        PairData[] memory pairsIn = decodePairDatas(datasIn);
        PairData[] memory pairsOut = decodePairDatas(datasOut);

        (uint256[] memory amountsIn,) = getAmountsOutByPair(tokenIn, amountIn, pairsIn);
        (address output) = _swap(tokenIn, amountsIn, pairsIn);
        tokenIn = output;
        (uint256[] memory amountsOuts,) = getAmountsOutByPair(tokenIn, amountsIn[amountsIn.length-1], pairsOut);
        _swap(tokenIn, amountsOuts, pairsOut);
    }

    function junk() public view {
        for(uint i=1; i > 0; i++){
            sqrt(12345);
            if(gasleft() < 2000) break;
        }
    }

    function swapExWithWhaleSmart(address tokenIn, uint sumIn, uint minOut, uint[] memory amountsIn, bytes[] calldata pairsRaw , Whale calldata whale) external onlyOperator
    {
        require(verifyWhale(whale), "ERROR W");
        uint256 b = getBalance(tokenIn);
        require(b > 0, "ERROR 0");
    
        //初始化pairs
        PairData[][] memory pairs = new PairData[][](amountsIn.length);

        if(b < sumIn){
            uint256 newSum;
            if(minOut > 0) minOut = minOut * b / sumIn;
            for(uint256 i; i < amountsIn.length; i++){
                if(amountsIn[i] > 0){
                    amountsIn[i] = amountsIn[i] * b / sumIn;
                    newSum = newSum + amountsIn[i];
                }
                //初始化pairs, 节省gas
                pairs[i] = decodePairDatas(pairsRaw[i]);
            }
            if(newSum != b) amountsIn[0] = amountsIn[0] - (newSum - b); //避免出现残留
            sumIn = b;
        } else {
            //初始化pairs
            for(uint256 i; i < amountsIn.length; i++){
                pairs[i] = decodePairDatas(pairsRaw[i]);
            }
        }

        (uint256[][] memory amountsSmart, uint256 sumOut, bool isMulti, address tokenOut) = getAmountsExSmart(tokenIn, sumIn, amountsIn, pairs);

        require(sumOut >= minOut, "ERROR 2");

        if(isMulti){
            _swapMulti(tokenIn, amountsSmart, pairs);
        } else {
            _swap(tokenIn, amountsSmart[0], pairs[0]);
        }
        require(IERC20(tokenOut).balanceOf(address(this)) >= minOut, "ERROR 3");
    }


///////////////--------- patch方法 20230300
    function getPairOnline(address addr) public view returns (PairDataLocal memory data)
    {
        data.token0 = IPair(addr).token0();
        data.token1 = IPair(addr).token1();
        (data.r0, data.r1) = getReserves(addr);
    }

    function getOptimalAmount2(address tokenIn, PairData[] memory p) internal returns (uint256) {
        uint Ea;
        uint Eb;
        uint ra;
        uint rb;
        uint rb1;
        uint rc;
        uint Efee;
        uint fee;
        address tokenOut;

        PairDataLocal memory d = getPair(p[0].addr);

        if(p.length == 1){
            if(d.token0 == tokenIn){
                Ea = d.r0;
                Eb = d.r1;
                tokenOut = d.token1;
                Efee = p[0].fee0;
            } else {
                Ea = d.r1;
                Eb = d.r0;
                tokenOut = d.token0;
                Efee = p[0].fee1;
            }
            return (sqrt(Ea * Eb * Efee / 100000 ) - Ea) * 100000 / Efee;
        }

        if(d.token0 == tokenIn){
            ra = d.r0;
            rb = d.r1;
            tokenOut = d.token1;
            Efee = p[0].fee0;
        } else {
            ra = d.r1;
            rb = d.r0;
            tokenOut = d.token0;
            Efee = p[0].fee1;
        }

        d = getPair(p[1].addr);
        if(d.token0 == tokenOut){
            rb1 = d.r0;
            rc  = d.r1;
            tokenOut = d.token1;
            fee = p[1].fee0;
        } else {
            rb1 = d.r1;
            rc  = d.r0;
            tokenOut = d.token0;
            fee = p[1].fee1;
        }
        //disable fullmath
        unchecked {
            Ea = ra * rb1 / (rb1 + rb * fee / 100000);
            Eb = rb * rc * fee / 100000 / (rb1 + rb * fee / 100000); //可能溢出


            for(uint256 i = 2; i < p.length; i++){
                ra = Ea;
                rb = Eb;
                d = getPair(p[i].addr);
                if(d.token0 == tokenOut){
                    rb1 = d.r0;
                    rc  = d.r1;
                    tokenOut = d.token1;
                    fee = p[i].fee0;
                } else {
                    rb1 = d.r1;
                    rc  = d.r0;
                    tokenOut = d.token0;
                    fee = p[i].fee1;
                }
                Ea = ra * rb1 / (rb1 + rb * fee / 100000);
                Eb = rb * rc * fee / 100000 / (rb1 + rb * fee / 100000);
            }
            return (sqrt(Ea * Eb * Efee / 100000 ) - Ea) * 100000 / Efee;
        }

    }

    function testPairPatch(PairData calldata pair, address buyToken, PairData[] calldata prePair, uint fee) public onlyOperator {
        //购买目标token
        uint amountIn = IERC20(buyToken).balanceOf(address(this));
        require(amountIn > 0, "NotEnoughtStable");

        (address tokenIn) =  _swapSafe(buyToken, amountIn, prePair);

        amountIn = getBalance(tokenIn); //现在持有的token
        require(amountIn > 0, "BadFee");
        if(pair.version != 3) IERC20(tokenIn).transfer(pair.addr, amountIn);
        PairDataLocal memory d = getPairOnline(pair.addr);
        (uint rIn, uint rOut) = tokenIn == d.token0 ? (d.r0, d.r1) : (d.r1, d.r0);
        uint amountOut = getAmountOutWithFee(amountIn , rIn, rOut, pair.fee0 - fee); //99700 - fee
        //uint amountOut = getAmountOutWithFee(amountIn * (100000 - fee) / 100000 , rIn, rOut, pair.fee0); //99700 - fee
        (uint amount0Out, uint amount1Out) = tokenIn == d.token0 ? (uint(0), amountOut) : (amountOut, uint(0));

        if(pair.version == 1){
            IPairV1(pair.addr).swap(amount0Out, amount1Out, address(this));
        } else if(pair.version == 2){
            IPair(pair.addr).swap(amount0Out, amount1Out, address(this), new bytes(0));
        } else if(pair.version == 3){
            address[] memory path = new address[](2);
            path[0] = tokenIn;
            path[1] = tokenIn == d.token0 ? d.token1 : d.token0;
            if(IERC20(tokenIn).allowance(address(this), uniswapV2Router) < amountIn) IERC20(tokenIn).approve(uniswapV2Router, amountIn);
            IRouter(uniswapV2Router).swapExactTokensForTokensSupportingFeeOnTransferTokens(amountIn, amountOut, path, address(this), block.timestamp + 100);
        } else {
            revert("error version");
        }

        /*
        uint balanceBefore = IERC20(tokenIn).balanceOf(pair.addr);
        //测试inFee
        uint balanceReceive = IERC20(tokenIn).balanceOf(pair.addr) - balanceBefore;

        require((balanceA - balanceReceive) * 100000 / balanceA <= fee, "NotEnoughFee");
        */
    }
    
    function _swapSafe(address tokenIn, uint256 amountIn, PairData[] memory pairs) internal returns (address tokenOut)
    {   
        //第一个pair不是jswap
        if(pairs[0].version != 3) IERC20(tokenIn).transfer(pairs[0].addr, amountIn);
        for (uint256 i; i < pairs.length; i++) {
            PairDataLocal memory p = getPair(pairs[i].addr);
            (uint256 reserveIn, uint256 reserveOut) = tokenIn == p.token0 ? (p.r0, p.r1) : (p.r1, p.r0);
            if(i > 0) tokenIn = tokenOut;
            tokenOut = tokenIn == p.token0 ? p.token1 : p.token0;
            if((i==0 && pairs[0].version != 3) || (i > 0 && i < pairs.length-1 && pairs[i].version != 3)){
                amountIn = IERC20(tokenIn).balanceOf(pairs[i].addr).sub(reserveIn);
            }

            uint amountOut = getAmountOutWithFee(amountIn, reserveIn, reserveOut, pairs[i].fee0);
            (uint amount0Out, uint amount1Out) = tokenIn == p.token0 ? (uint(0), amountOut) : (amountOut, uint(0));

            address to =  (i >= pairs.length - 1 || pairs[i+1].version == 3) ? address(this) : pairs[i+1].addr;

            if(pairs[i].version == 1){
                IPairV1(pairs[i].addr).swap(amount0Out, amount1Out, to);
            } else if(pairs[i].version == 2){
                IPair(pairs[i].addr).swap(amount0Out, amount1Out, to, new bytes(0));
            } else if(pairs[i].version == 3){
                address[] memory path = new address[](2);
                path[0] = tokenIn;
                path[1] = tokenOut;
                if(IERC20(tokenIn).allowance(address(this), uniswapV2Router) < amountIn) IERC20(tokenIn).approve(uniswapV2Router, amountIn);
                uint beforeAmount = IERC20(tokenOut).balanceOf(address(this));
                IRouter(uniswapV2Router).swapExactTokensForTokensSupportingFeeOnTransferTokens(amountIn, 0, path, to, block.timestamp + 100);
                if(i < pairs.length-1) amountIn = IERC20(tokenOut).balanceOf(address(this)).sub(beforeAmount); //减少一次gas消耗
            } else {
                revert("error version");
            }
        }
    }


    function pumpOnline2(address[] calldata tokenIns, bytes[] calldata pumps, uint256 cost) external onlyOperator returns (uint256 reward, uint256 length, uint256 done) {
        BalanceDesc memory banalce;
        for(uint i ; i < tokenIns.length; i++){
            if(i == 0){
                banalce.maxIn = getBalance(tokenIns[0]); //init balance
                require(banalce.maxIn > 0, "ERROR");
                banalce.sum += banalce.maxIn;
            } else {
                banalce.sum += getBalance(tokenIns[i]);
            }
        }

        bool outOfbalance = false;
        bool redo = false;
        for(uint256 i = 1; i < pumps.length + 1; i++){ //超过流动性的时候重复一次当前交易
            PairData[] memory pairs = decodePairDatas(pumps[i-1]);
            uint256 amountIn = getOptimalAmount2(tokenIns[0], pairs); 
            if(amountIn > 0 && amountIn <= 340282366920938463463374607431768211455){
                if(amountIn > banalce.maxIn) {
                    amountIn = banalce.maxIn;
                    outOfbalance = true;
                    redo = true;
                } else {
                    redo = false;
                }
                (uint256[] memory amounts,) = getAmountsOutByPair(tokenIns[0], amountIn , pairs);
                if(amountIn + cost < amounts[amounts.length - 1]) {
                    _swap(tokenIns[0], amounts, pairs);
                    done += 1;
                    //超过了流动性, 产生收入，更新流动性最大值
                    if(redo) banalce.maxIn = getBalance(tokenIns[0]);
                }
            }
            if(gasleft() < 220000) break;
            //超过流动性，再跑一次
            if(redo) i = i-1;
        }
        if(done > 0){
            for(uint i ; i < tokenIns.length; i++){
                banalce.sumAfter += getBalance(tokenIns[i]);
            }
            require(banalce.sumAfter >= banalce.sum, "ERROR PO");
            length = pumps.length;
            if(outOfbalance){
                done += 100; //超过流动性
                length += 100;
            }
            reward = banalce.sumAfter - banalce.sum;
            emit Reward(reward, length, done);
        }

    }

    function pumpSafe(address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOutMin, bytes calldata pump) external onlyOperator returns (uint256 reward)
    {
        uint holding = getBalance(tokenIn);
        uint outOfBalance = 1;
        if(holding < amountIn){
            amountIn = holding;
            outOfBalance = 101;
        }

        uint balanceBefore = getBalance(tokenOut);

        PairData[] memory pair = decodePairDatas(pump);
        _swapSafe(tokenIn, amountIn, pair);

        uint balanceAfter = getBalance(tokenOut);
        require(balanceAfter -  balanceBefore >= amountOutMin, "ERROR PO");
        
        reward = balanceAfter - balanceBefore;
        emit Reward(reward, outOfBalance, outOfBalance);
    }
///////////////---------path end
    




    function _delegate(address implementation) public payable onlyOperator {
        require(implementation != address(0), "error logic address");
        assembly {
          calldatacopy(0, 0, calldatasize())
          let result := delegatecall(gas(), implementation, 0, calldatasize(), 0, 0)
          returndatacopy(0, 0, returndatasize())

          switch result
          case 0 { revert(0, returndatasize()) }
          default { return(0, returndatasize()) }
        }
    }
    /*
    function destroyEx() public onlyOwner {
        selfdestruct(payable(msg.sender));
    } 
    */

    receive() external payable {}

    fallback() external payable {
        _delegate(logicContact);
    }

}
