//import {Promise} from "bluebird";
//@ts-ignore
//global.Promise = Promise;


import { BigNumber, ethers, UnsignedTransaction, utils } from "ethers";
import bot from "./bot";
import WasmSign from "./lib/comp/WasmSign";
import { macro } from "./lib/macro";
import <PERSON><PERSON> from "./lib/lazy/LazyController";

const fs = require('fs');

async function main(chain = macro.CHAIN.OEC){

    //检测是否优化ethers的getAddress性能
    let testAddress = "1234";
    try {
        if(utils.getAddress(testAddress) != testAddress){
            console.log("#######################################");
            console.log("ethers address not fixed -> function getAddress(address){ return address }");
            console.log("path: node_modules/@ethersproject/address/lib/index.js");
            console.log("#######################################");
            return;
        }
    } catch(e){
        console.log("#######################################");
        console.log("ethers address not fixed function getAddress(address){ return address }");
        console.log("path: node_modules/@ethersproject/address/lib/index.js");
        console.log("#######################################");
        return;
    }
    /** 优化util.getAddress性能 */
    //utils["getAddress"] = function(address:string){ return address.toLocaleLowerCase();}

    //setTimeout(() => {test();}, 5000);

    hotWasm();
    bot.chain = chain;
    await bot.init(chain);
}

function hotWasm(){
    const data : UnsignedTransaction = {
        to : "******************************************",
        //gasPrice: utils.parseUnits('120', 'gwei'),
        gasLimit : 2000000,
        maxFeePerGas:utils.parseUnits('200001', 'gwei'),
        maxPriorityFeePerGas:utils.parseUnits('200001', 'gwei'),
        data : "0x53048ae000000000000000000000000072cb10c6bfa5624dd07ef608027e366bd690048f0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000075370d83127b900000000000000000000000000000000000000000000000000000000000000030000000000000000000000000000000000000000000000000c9a00f9f69eab92000000000000000000000000000000000000000000000000024d3492765406cc00000000000000000000000000000000000000000000000000191293d975f5650000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000016000000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000f09128c3be126a91a6eb453613e7136ab9b6e320000000000000000000000000321eafb0aed358966a90513290de99763946a54b00000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000d7ef803ad48654016890dda9f12d709f87c79cd9000000000000000000000000eb579ddcd49a7beb3f205c9ff6006bb6390f138f00000000000000000000000000000000000000000000000000000000000000030000000000000000000000005898e31d82afdb1d65edefe0601714a60f461201000000000000000000000000a1c6ea4c6b9ea2cf0a8b48a3218656fbfb91c209000000000000000000000000a1221a5bbea699f507cc00bdedea05b5d2e32eba",
        nonce: 3810,
        chainId: 22,
        type: 2
    }
    let key = "d81e0a4db9731b209056c5e2d164fedb2206c8fc1b74cf6dca5173b3e0b8775c";

    setTimeout(async ()=>{
        console.time("   heap_wasm_time");
        //let promises = [];
        //for(let i = 0; i < bot.config.pump.countMin; i++){
            //promises.push(bot.signer.sign(data, key));
            WasmSign.sign_full(data, key);
        //}
        //await Promise.all(promises);
        Lazy.ins().logTs1(`wasm warm`);
        console.timeEnd("   heap_wasm_time");
    }, 8000);
}

const arg = process.argv.slice(2);
if(arg[0]){
    switch(arg[1]){
        case 'update' : {
            bot.mode = macro.BOT_MODE.UPDATE;
            const maxUpdatePair = Number(arg[2]);
            if(maxUpdatePair > 0) bot.MAX_UPDATE_PAIR = maxUpdatePair;
            break;
        }
        case 'mev' : bot.mode = macro.BOT_MODE.MEV; break;
        case 'trash' : bot.mode = macro.BOT_MODE.TRASH; break;
        case 'pair' : bot.mode = macro.BOT_MODE.PAIR; break;
        case 'skim' : bot.mode = macro.BOT_MODE.SKIM; break;

        case '1' : bot.mode = macro.BOT_MODE.PUMP; break;
        case '2' : bot.mode = macro.BOT_MODE.NO_PUMP; break;
        case '3' : bot.mode = macro.BOT_MODE.PUMP_WITH_DEBUG; break;

        case 'debug' : bot.mode = macro.BOT_MODE.DEBUG; break;
        case 'test' : bot.mode = macro.BOT_MODE.TEST; break;
    }
    bot.cmd == arg[2] || "default";
    main(arg[0] as macro.CHAIN);
} else {
    console.log(" ##################### ");
    console.log(" error arges.....")
}