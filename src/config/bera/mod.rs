pub mod wallets;

use std::collections::HashMap;
use crate::config::{ChainConfig, TokenConfig};
use crate::tools::lowc;
use crate::vira::dex::bera::Bera;
use crate::vira::dex::{<PERSON><PERSON><PERSON><PERSON>, factory::FactoryConfig, DEX};

use super::Operator;

pub fn new() -> ChainConfig {
    ChainConfig::new()
        .server("https://bartio.rpc.berachain.com/")
        .eth("******************************************")
        .stables(
            vec![vec!["******************************************"]]
        )
        .tokens(vec![
            TokenConfig::new().name("wbera").addr("******************************************").is_eth().price(5.0),
        ])
        .routers(vec![
            Bera::new(FactoryConfig::new().name("bex").addr("******************************************"))
        ])
        .operators( Operator {
            bull: HashMap::from([
                (lowc("******************************************"), lowc("04c068b8f82714cd94d726e039551f33723f7122f8682b478cbcdef7da6fde7f")),
                (lowc("******************************************"), lowc("7619343a0a42476e5a8dff44a83b7283c9ca594442a66e317c4a0095fbbed8b3")),
                ]),
            pump: HashMap::from([
                (lowc("******************************************"), lowc("2b5ef1760ab306659b13b76789cbbdf9c3b21ba416e9982dc387f7473174b462")),
                (lowc("******************************************"), lowc("5d0bff3e0b4140ca645231a3ace6c16e7e3ea7e388ec227f1aa6dafe04a47fdd")),
            ]),
        })
        .build()

}


mod tests {
    #[test]
    fn test_config(){
        let config = super::new();
        println!("config: {:?}", config);
    }
}