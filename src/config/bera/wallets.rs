use std::collections::HashMap;
use std::fs::File;
use std::io::Read;
use serde_json;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug, <PERSON>lone, Default, PartialEq, Eq)]
pub struct Wallet {
    id: u32,
    addr: String,
    key : String,
    #[serde(default)]
    balance: u128,
}

#[derive(Serialize, Deserialize, Debug, <PERSON>lone, Default, PartialEq, Eq)]
pub struct Wallets {
    wallets: HashMap<String, Wallet>,
}

impl Wallets {
    pub fn _load() -> Wallets {
        let mut wallets = HashMap::new();
        
        // Load JSON from src/sybil/bera/wallet.json
        let mut file = File::open("src/sybil/bera/wallets.json").unwrap();
        let mut contents = String::new();
        file.read_to_string(&mut contents).unwrap();
        let json : Vec<Wallet> = serde_json::from_str(&contents).unwrap();
        //println!("json: {:?}", json);
        
        for wallet in json {
            println!("wallet: {:?}", wallet);
            wallets.insert(wallet.addr.clone(), wallet);
        }
        //wallets = json;

        println!("wallets: {:?}", wallets);
        Wallets {
            wallets,
        }
    }
    /*
    pub fn get(&self, address: &str) -> Option<&Wallet> {
        self.wallets.get(address)
    }

    pub fn get_all(&self) -> &HashMap<String, Wallet> {
        &self.wallets
    }
     */
}



