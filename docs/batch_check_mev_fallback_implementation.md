# batch_check_mev 降级机制实现文档

## 概述

本文档描述了在 `src/vira/contract/mod.rs` 中为 `batch_check_mev` 函数实现的降级机制。该机制确保当批量 MEV 检测失败时，系统能够自动降级为单个请求处理，提高系统的容错性和可靠性。

## 实现详情

### 函数签名
```rust
pub async fn batch_check_mev(&self, inputs : Vec<Vec<ViraData::PoolReq>>) -> Result<Vec<ViraLogic::CheckMevsResultDesc>, DEXError>
```

### 核心逻辑

1. **首次批量处理尝试**
   - 使用 `contract.batchCheckMev()` 尝试批量处理所有 MEV 请求
   - 如果成功，直接返回结果

2. **降级机制触发**
   - 当批量处理失败时，记录错误信息并启动降级机制
   - 等待 2 秒后开始单个请求处理

3. **单个请求处理**
   - 将原始的 `Vec<Vec<ViraData::PoolReq>>` 拆分为单个 `Vec<ViraData::PoolReq>` 请求
   - 为每个单个请求重新获取 stable 配置（分母为 1）
   - 逐一调用 `contract.batchCheckMev(vec![single_input])`

4. **错误处理和结果收集**
   - 对于成功的单个请求，提取结果并添加到结果数组
   - 对于失败的单个请求，创建空结果以保持数组长度一致
   - 统计成功和失败的请求数量

5. **进度显示**
   - 每处理 10 个请求显示一次进度
   - 最终显示总体处理结果

## 关键特性

### 1. 容错性
- 批量处理失败时自动降级，不会导致整个操作失败
- 单个请求失败时创建空结果，保持结果数组的完整性

### 2. 一致性
- 保持函数签名不变，调用方无需修改代码
- 返回结果的格式和数量与输入保持一致

### 3. 可观测性
- 详细的日志记录，包括错误信息、进度显示和最终统计
- 区分批量失败和单个请求失败的不同情况

### 4. 性能考虑
- 只在批量处理确实失败时才使用降级机制
- 避免为无利可图的 MEV 路径进行不必要的处理

## 错误处理策略

### 批量处理失败
```rust
Err(e) => {
    println!("❌ 批量MEV检查失败，启动降级机制: {:?}", e);
    // 启动降级处理...
}
```

### 单个请求失败
```rust
Err(single_e) => {
    // 创建空结果以保持数组长度一致
    all_results.push(ViraLogic::CheckMevsResultDesc {
        gas: U256::ZERO,
        fee0: vec![],
        fee1: vec![],
    });
}
```

## 与现有代码的兼容性

该实现与现有的 `src/vira/status/sync.rs` 中的降级逻辑保持一致：

1. **相同的错误处理模式**：创建空结果而不是传播错误
2. **相同的等待策略**：失败后等待 2 秒再重试
3. **相同的日志格式**：使用一致的日志输出格式
4. **相同的结果结构**：使用相同的 `ViraLogic::CheckMevsResultDesc` 结构

## 使用示例

```rust
// 调用方代码无需修改
let mev_requests = vec![
    vec![pool_req_1],
    vec![pool_req_2],
    // ...
];

match contract.batch_check_mev(mev_requests).await {
    Ok(results) => {
        // 处理结果，无论是批量成功还是降级处理的结果
        for result in results {
            // 处理每个 MEV 检查结果
        }
    }
    Err(e) => {
        // 只有在所有尝试都失败时才会到达这里
        eprintln!("MEV 检查完全失败: {:?}", e);
    }
}
```

## 测试

在 `mod tests` 中添加了 `test_batch_check_mev_fallback` 测试函数，用于验证降级机制的正确性。该测试创建模拟数据并调用 `batch_check_mev` 函数，验证在不同场景下的行为。

## 总结

该降级机制实现了以下目标：

1. ✅ 在批量处理失败时自动降级为单个请求处理
2. ✅ 保持函数签名不变，确保调用方兼容性
3. ✅ 提供详细的日志记录和进度显示
4. ✅ 实现完整的错误处理和结果收集
5. ✅ 与现有代码模式保持一致
6. ✅ 只在必要时使用降级机制，避免性能影响

这个实现大大提高了系统的可靠性，确保即使在网络不稳定或合约负载较高的情况下，MEV 检测功能仍能正常工作。
