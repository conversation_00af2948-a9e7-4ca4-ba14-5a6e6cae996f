# Gemini 项目指南

本文档为理解和与本项目交互提供指南。

## 项目概述

这是一个混合项目，同时包含一个 Rust 应用程序和使用 Foundry 管理的 Solidity 智能合约。

- **Rust Crate**：主要应用程序逻辑使用 Rust 编写，位于 `src` 目录中。
- **Solidity 合约**：智能合约位于 `forge/contracts` 目录中，并使用 Foundry 框架进行管理。

## 技术栈

- **客户端**：Rust
- **智能合约/服务端**：Solidity
- **智能合约框架**：Foundry

## 关键文件和目录

- `src/`：包含 Rust 源代码。
- `src/main.rs`：Rust 应用程序的主入口点。
- `Cargo.toml`：Rust 依赖管理和项目配置。
- `forge/`：Foundry 项目的根目录。
- `forge/contracts/`：Solidity 智能合约源文件。
- `forge/test/`：智能合约的 Solidity 测试。
- `foundry.toml`：Foundry 配置文件。

## 开发工作流

### 构建项目

要构建项目的不同部分，请使用以下命令：

- **Rust**：

  ```bash
  cargo build
  ```
- **Solidity (Foundry)**：

  ```bash
  forge build --sizes
  ```

### 运行测试

要为项目的每个部分运行测试：

- **Rust**：

  ```bash
  cargo test
  ```
- **Solidity (Foundry)**：

  ```bash
  forge test -vvv
  ```

### 代码检查 (Linting)

要检查代码样式和错误：

- **Rust**：

  ```bash
  cargo check
  ```

### 基本原则

- **尽量使用中文回答问题**
- **全力以赴，给我最好的代码!**
- **代码需要简洁可读性高， 更少的函数嵌套，允许部分重复代码，不需要极致的DRY原则**
- **需要多处使用的常量放在const.rs中，只有当前mod使用的常量放在mod中**

### Optimization Guidelines

- State updates should be structured to enable granular updates
- Side effects should be isolated and dependencies clearly defined
