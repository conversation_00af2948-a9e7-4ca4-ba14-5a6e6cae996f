# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

- **Build**: `cargo build`
- **Run Tests**: `cargo test` (use `-- --nocapture` for verbose output)
- **Run Single Test**: `cargo test <test_name>`
- **Lint**: `cargo clippy`
- Rust Check: cargo check
- Foundry Build: forge build
- Foundry Check: forge check

## Architecture

1. **Core Modules**:
   - `vira/`: DeFi and blockchain interactions (e.g., `dex`, `pool`, `token`).
   - `config/`: Configuration management.
   - `errors.rs`: Centralized error handling.
2. **Async Runtime**: Uses `tokio` for async operations.
3. **Dependencies**:
   - `alloy`: Ethereum interactions.
   - `serde`: Serialization.
   - `rayon`: Parallel processing.

## Notes

- Focus on `vira/` for DeFi logic.
- Use `cargo test -- --nocapture` for detailed test output.
- 尽量使用中文回答
