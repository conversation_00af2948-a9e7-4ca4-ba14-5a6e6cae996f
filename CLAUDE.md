# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

1. **Build**: `npm run build`
2. **Lint**: `npm run lint`
3. **Run Tests**: `npm test`
4. **Run Single Test**: `npm test -- <test-file-path>`

## High-Level Architecture

The codebase is structured around a bot system for interacting with blockchain routers and factories. Key components include:

- **Router Configuration**: Custom factory addresses can be specified in router configurations for non-standard router-factory relationships.
- **Provider Management**: Supports both HTTP and WebSocket providers with nonce management.
- **Oracle Integration**: Handles data fetching and processing for blockchain interactions.

## Cursor Rules

- **Project Structure**: Custom factory addresses can be specified in router configurations for non-standard router-factory relationships.
