// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/Test.sol";

import {GetUniswapV2PoolAddressBatchRequest} from "../contracts/UniswapV2/GetUniswapV2PoolAddressBatchRequest.sol";
import {GetUniswapV2PoolDataBatchRequest} from "../contracts/UniswapV2/GetUniswapV2PoolDataBatchRequest.sol";
import {GetUniswapV2PoolReservesBatchRequest} from "../contracts/UniswapV2/GetUniswapV2PoolReservesBatchRequest.sol";

contract TestUniswapV2 is Test {
    //GetUniswapV2PoolAddressBatchRequest public pairsBatchRequest;
    GetUniswapV2PoolDataBatchRequest public poolsDataBatchRequest;

    address public mockRouter;
    address public mockFactory;

    function setUp() public {
    }

    /*
    function test_GetUniswapV2PoolsBatchRequest() public {
        pairsBatchRequest = new GetUniswapV2PoolAddressBatchRequest(
            30,
            20,
            0x98bf93ebf5c380C0e6Ae8e192A7e2AE08edAcc02
        );
    }
    */

    function test_GetUniswapV2PoolsBatchRequest() public {
        address[] memory pools = new address[](1);
        //pools[0] = 0x18ab2e3D24A4d895eB5188F96ed3aF25aFDF795d; //err pool
        pools[0] = 0xE56043671df55dE5CDf8459710433C10324DE0aE; //good pool
        new GetUniswapV2PoolDataBatchRequest(pools);
        // Print pool information
        for (uint i = 0; i < pools.length; i++) {
            emit log_named_address(
                string(abi.encodePacked("Pool ", vm.toString(i), " Address")),
                pools[i]
            );
        }
    }

    function test_GetUniswapV2PoolReservesBatchRequest() public {
        address[] memory pools = new address[](1);
        //pools[0] = 0x18ab2e3D24A4d895eB5188F96ed3aF25aFDF795d; //err pool
        pools[0] = 0xE56043671df55dE5CDf8459710433C10324DE0aE; //good pool
        new GetUniswapV2PoolReservesBatchRequest(pools);
    }
}
