// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/Test.sol";
import "../contracts/ViraLogic.sol";
import "../contracts/Vira.sol";
import "../contracts/IVira.sol";

contract BatchCheckMevTest is Test {
    ViraLogic public vira;
    address constant WPLS = 0xA1077a294dDE1B09bB078844df40758a5D0f9a27;
    address constant DAI_WPLS = 0xaE8429918FdBF9a5867e3243697637Dc56aa76A1;
    address constant BEAR_DAI = 0x9B2EcC7ffDDF74a67600ee7E5a273561c626C99d;
    address constant WPLS_BEAR = 0x31eF9a41500E6BD18524404aC9c5B88D04AA924E;

    // WPLS > FEE > PLSD > WPLS
    address constant PLSD_FEE = 0x0529cd0BB5a01461a657FC499275e925aE0F25Ce;
    address constant PLSD_WPLS = 0xFe76a07537884C5DE46683503257e6af5798Cf7E;
    // WPLS > FEE > INC > WPLS
    address constant FEE_WPLS = 0xA20397F5d6e27ca8e48Cc3d58137cD791d483077;
    address constant INC_FEE = 0xDfd729F355cCaa74aD3EF128352145b33ccccedb;
    address constant INC_WPLS = 0xf808Bb6265e9Ca27002c0A04562Bf50d4FE37EAA;

    //WPLS > FIN > WPLS
    address constant FIN_WPLS_V2 = 0x615CfD552E98eB97e5557B03aa41D0E85e98167B;

    // WPLS > FIN > AFFE > WPLS
    address constant FIN_WPLS = 0x3474c4E0447E72BEf593f7A409E5Ae67A6cec9aD; //fin-wpls
    address constant FIN_AFFE = 0x18C2972dB0bD3958bCEC1311b3b548c2974E5dE6; //fin-affe
    address constant AFFE_WPLS = 0x155172653E94a7e5F0e04126803dcB6896796FBb; //affe-wpls

    function setUp() public {
        // 部署合约
        vira = new ViraLogic();
        emit log_named_address("Vira Address", address(vira));
        emit log_named_address("Vira Owner Address", vira.owner());
        emit log_named_address("Vira Logic Address", vira.logicContact());

        // Deal WPLS tokens to the contract for testing
        deal(WPLS, address(vira), 10000 * 10**18);
        
        // Verify the balance
        uint256 balance = IERC20(WPLS).balanceOf(address(vira));
        emit log_named_uint("WPLS Balance", balance);
    }

    function test_BatchCheckMev() public {
        // 设置调用者地址为operator
        vm.startPrank(vira.owner());

        // 构造测试数据
        // WPLS > FIN > WPLS 路径
        ViraData.PoolReq[][] memory ds = new ViraData.PoolReq[][](1);
        ds[0] = new ViraData.PoolReq[](2);
        
        // WPLS-FIN pool
        ds[0][0] = ViraData.PoolReq({
            addr: FIN_WPLS_V2,
            version: 2,    // UniswapV2 pool
            fee: 0,        // fee是需要检测的值
            fp: 9970,      // router fee
            inIndex: 1,    // WPLS index
            outIndex: 0    // FIN index
        });

        // FIN-WPLS pool (完成循环)
        ds[0][1] = ViraData.PoolReq({
            addr: FIN_WPLS,
            version: 2,    // UniswapV2 pool
            fee: 0,        // fee是需要检测的值
            fp: 9970,      // router fee
            inIndex: 0,    // FIN index
            outIndex: 1    // WPLS index
        });

        // 调用合约函数
        ViraLogic.CheckMevsResultDesc[] memory results = vira.batchCheckMev(ds, new address[](0), new uint[](0));

        //打印出results的原始结果
        emit log_named_uint("results length", results.length);
        emit log_named_uint("results[0].fee0 length", results[0].fee0.length);
        emit log_named_uint("results[0].fee1 length", results[0].fee1.length);
        emit log_named_uint("results[0].gas", results[0].gas);

        // 验证结果
        require(results.length > 0, "Should return at least one result");
        
        // 打印结果
        for (uint i = 0; i < results.length; i++) {
            emit log_named_uint("Gas Used", results[i].gas);
            
            // 打印正向交易的 fees (WPLS > FIN > WPLS)
            if (results[i].fee0.length > 0) {
                emit log_string("\nForward Path (WPLS > FIN > WPLS):");
                for (uint j = 0; j < results[i].fee0.length; j++) {
                    string memory poolDesc;
                    if (j == 0) poolDesc = "WPLS-FIN";
                    else if (j == 1) poolDesc = "FIN-WPLS";
                    
                    emit log_named_uint(
                        string(abi.encodePacked(poolDesc, " Fee")), 
                        results[i].fee0[j]
                    );
                }
            } else {
                emit log_string("Forward Path: No valid fees");
            }
            
            // 打印反向交易的 fees (WPLS > FIN > WPLS)
            if (results[i].fee1.length > 0) {
                emit log_string("\nReverse Path (WPLS > FIN > WPLS):");
                for (uint j = 0; j < results[i].fee1.length; j++) {
                    string memory poolDesc;
                    if (j == 0) poolDesc = "WPLS-FIN";
                    else if (j == 1) poolDesc = "FIN-WPLS";
                    
                    emit log_named_uint(
                        string(abi.encodePacked(poolDesc, " Fee")), 
                        results[i].fee1[j]
                    );
                }
            } else {
                emit log_string("Reverse Path: No valid fees");
            }
        }

        // 验证 gas 值
        for (uint i = 0; i < results.length; i++) {
            assertTrue(
                results[i].gas > 0,
                "Gas used should be greater than 0"
            );
        }

        vm.stopPrank();
    }

}