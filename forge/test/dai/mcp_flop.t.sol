// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";

// 定义 Flop 合约接口（关键函数）
interface IFlop {
    function kicks() external view returns (uint256);
    function bids(uint256) external view returns (uint256, uint256, address, uint48, uint48, address);
    function dent(uint256 id, uint256 lot, uint256 bid) external;
    function beg() external view returns (uint256);
    function ttl() external view returns (uint48);
    function tau() external view returns (uint48);
}

interface IVow {
    // Vow 合约真实函数签名
    function Sin() external view returns (uint256);       // 总未处理坏账（RAD）
    function flog(uint256 era) external;                  // 触发拍卖
    function wait() external view returns (uint256);      // 等待期
    function ash() external view returns (uint256);       // 已处理的坏账
    function live() external view returns (uint256);      // 合约激活状态
    function heal(uint256 rad) external;                  // 处理盈余（仅用于对比）
    function flop() external returns (uint id);            // 触发拍卖
}

interface IVat {
    function frob(bytes32 ilk, address u, address v, address w, int dink, int dart) external;
    function hope(address usr) external;
    function move(address src, address dst, uint256 rad) external;
    function dai(address usr) external view returns (uint256);
    function gem(bytes32 ilk, address usr) external view returns (uint256);
    function Line() external view returns (uint256); // 全局债务上限
    function debt() external view returns (uint256);
}

// 定义 DAI 接口（用于授权和转账）
interface IDai {
    function approve(address spender, uint256 amount) external returns (bool);
    function balanceOf(address owner) external view returns (uint256);
}

contract FlopTest is Test {
    // 合约地址
    address constant FLOP_ADDR = 0xA41B6EF151E06da0e34B009B86E828308986736D;
    address constant VOW_ADDR = 0xA950524441892A31ebddF91d3cEEFa04Bf454466;
    address constant DAI_ADDR = 0x6B175474E89094C44Da98b954EedeAC495271d0F;
    address public constant VAT_ADDR = 0x35D1b3F3D7966A1DFe207aa4514C12a259A0492B;

    // 合约实例
    IFlop public flop = IFlop(FLOP_ADDR);
    IVow public vow = IVow(VOW_ADDR);
    IDai public dai = IDai(DAI_ADDR);
    IVat public vat = IVat(VAT_ADDR);
    address public user = address(0x123); // 测试用户地址

    function setUp() public {
        // 切换到主网 Fork 模式
        //vm.createSelectFork("mainnet"); 

        // 给测试用户充值 DAI 并授权 Flop 合约使用
        deal(address(dai), user, 100_000 * 1e18); 
        vm.prank(user);
        dai.approve(address(flop), type(uint256).max);
    }

    // 测试 1: 查询活跃拍卖
    function testFindActiveAuction() public {
        uint256 activeAuctionId = flop.kicks();
        console.log("Active Auction ID:", activeAuctionId);

        if (activeAuctionId > 0) {
            (, uint256 bid, , uint48 start, uint48 end, ) = flop.bids(activeAuctionId);
            console.log("Current Bid (DAI):", bid / 1e18);
            console.log("Auction Duration:", end - start, "seconds");
        }
    }

    // 测试 2: 计算可接受价格（荷兰式拍卖逻辑）
    function testCalculatePrice() public {
        uint256 auctionId = flop.kicks();
        require(auctionId > 0, "No active auction");

        (, uint256 bid, , uint48 start, uint48 end, ) = flop.bids(auctionId);
        uint48 ttl_ = flop.ttl();
        uint48 tau_ = flop.tau();
        uint256 beg_ = flop.beg();

        // 简化价格计算（实际需参考 Flop 合约逻辑）
        uint256 elapsed = block.timestamp - start;
        uint256 remaining = end - block.timestamp;
        uint256 currentPrice = bid * (remaining * 1e18) / (ttl_ * 1e18);
        currentPrice = currentPrice * beg_ / 1e18; // 应用最小加价比例

        console.log("Estimated Current Price (MKR per DAI):", currentPrice / 1e18);
    }

    // 测试 3: 提交竞拍（需在 Fork 模式下运行）
    function testBidOnAuction() public {
        uint256 auctionId = flop.kicks();
        require(auctionId > 0, "No active auction");

        // 获取当前拍卖参数
        (uint256 lot, uint256 bid, , , , ) = flop.bids(auctionId);

        // 计算可接受的 DAI 出价（示例：出价 10% 低于当前 bid）
        uint256 myBid = bid * 90 / 100;

        // 提交竞拍
        vm.prank(user);
        flop.dent(auctionId, lot, myBid);

        // 验证拍卖状态更新
        (, uint256 newBid, address newGuy, , , ) = flop.bids(auctionId);
        assertEq(newGuy, user, "Bidder should be updated");
        assertEq(newBid, myBid, "Bid amount should be updated");
    }

    // 获取可触发拍卖的坏账信息（基于最新区块时间）
    function testTriggerFlop() public {
        // 1. 验证合约状态
        require(vow.live() == 1, "Vow is not active");

        // 2. 获取链上实时数据
        uint256 totalSin = vow.Sin();        // 未处理坏账总量（RAD）
        uint256 waitPeriod = vow.wait();     // 等待期（秒）
        uint256 currentTime = block.timestamp;

        console.log("Total Unprocessed Sin:", totalSin / 1e45, "RAD");
        console.log("Wait Period:", waitPeriod);
        console.log("Current Time:", currentTime);

        if (totalSin == 0) {
            console.log("No bad debt to process");
            return;
        }

        // 3. 核心逻辑：模拟时间推移并触发 flog
        // 注意：Vow 合约中 flog(era) 的 era 必须满足 block.timestamp >= era + wait
        // 假设最新坏账的 era 是当前时间 - waitPeriod + 1（未超期）
        uint256 testEra = currentTime - waitPeriod + 1;

        // 检查是否可触发
        bool canTrigger = (currentTime >= testEra + waitPeriod);
        console.log("Can trigger auction?", canTrigger);

        if (!canTrigger) {
            console.log("Wait period not passed");
            vm.expectRevert(bytes("Vow/not-passed")); // 匹配 Vow 合约的 revert 信息
            vow.flog(testEra);
            return;
        }

        // 4. 触发 flog 并验证 Flop 拍卖
        uint256 preAuctionId = flop.kicks();
        vow.flog(testEra); // 必须传入合法的 era

        uint256 postAuctionId = flop.kicks();
        console.log("New Auction ID:", postAuctionId);

        // 断言：拍卖 ID 递增且合约状态更新
        assertTrue(postAuctionId > preAuctionId, "Auction ID should increment");
        assertGt(vow.ash(), 0, "Ash (processed sin) should increase");
    }

    function testTriggerFlopAndHeal() public {
        // 1. 验证合约状态
        require(vow.live() == 1, "Vow is not active");

        // 2. 获取链上实时数据
        uint256 totalSin = vow.Sin();        // 未处理坏账总量（RAD）
        uint256 waitPeriod = vow.wait();     // 等待期（秒）
        uint256 currentTime = block.timestamp;

        console.log("Total Unprocessed Sin:", totalSin / 1e45, "RAD");
        console.log("Wait Period:", waitPeriod);
        console.log("Current Time:", currentTime);


        //当前vow持有的dai数量
        uint256 daiBalance = vat.dai(address(vow));
        console.log("vat DAI Balance:", daiBalance / 1e45, "DAI");
        uint256 sin = totalSin > daiBalance ? daiBalance : totalSin;
        vow.heal(sin);

        uint256 auctionId = vow.flop();
        console.log("Auction ID:", auctionId);


        /*
        // 3. 触发拍卖
        uint256 preAuctionId = flop.kicks();
        vow.flog(currentTime);
        
        // 4. 验证拍卖是否启动
        uint256 postAuctionId = flop.kicks();
        console.log("New Auction ID:", postAuctionId);

        // 断言：拍卖 ID 递增且合约状态更新
        assertTrue(postAuctionId > preAuctionId, "Auction ID should increment");

        // 5. 验证坏账处理  
        uint256 healedSin = vow.ash();
        console.log("Healed Sin:", healedSin / 1e45, "RAD");
        assertGt(healedSin, 0, "Healed sin should be greater than 0");
        */
    }

}