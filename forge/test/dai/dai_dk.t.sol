// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../../contracts/dai/dai_dk.sol";

// 定义简单的ERC20接口，用于余额检查
interface ERC20 {
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
    function transfer(address to, uint256 amount) external returns (bool);
}


contract DaiMinterTest is Test {
    DaiMinter public daiMinter;

    address public constant DAI = ******************************************;
    
    // 测试账户
    address public constant ALICE = address(0x1);
    address public constant BOB = address(0x2);
    
    // 测试数据
    uint256 public constant TEST_ETH_AMOUNT = 1 ether;
    uint256 public constant TEST_DAI_AMOUNT = 1000 ether;
    
    function setUp() public {
        // 设置测试环境
        vm.startPrank(ALICE);
        
        // 部署DaiMinter合约
        daiMinter = new DaiMinter();
        
        // 给测试账户一些ETH
        vm.deal(ALICE, 100 ether);
        vm.deal(BOB, 100 ether);
        vm.stopPrank();


        vm.startPrank(******************************************);
        ERC20(DAI).transfer(address(daiMinter), 60000000 * 10**18);
        //deal(DAI, address(daiMinter), 600000000 * 10**18);

        // 模拟为合约添加100000 DAI用于测试
        //mockDaiForContract(50000000 ether);

        vm.stopPrank();
    }
    

    function testGetLiquidatableAuctions() public {
        // 切换到ALICE账户
        vm.startPrank(ALICE);
        
        // 获取可清算的拍卖
        DaiMinter.AuctionData[] memory auctions = daiMinter.getAllLiquidatableAuctions();
        
        // 打印结果
        console.log("\n=== Available Liquidations ===");
        console.log("Total auctions found:", auctions.length);
        
        for(uint i = 0; i < auctions.length; i++) {
            DaiMinter.AuctionData memory auction = auctions[i];
            console.log("\n--- Auction", i + 1, "---");
            console.log("Auction ID:", auction.auctionId);
            console.log("Collateral Type:", string(abi.encodePacked(auction.ilk)));
            
            // 打印基本信息
            console.log("Position:", auction.pos);
            console.log("Dai Borrowed:", auction.tab / 1e45, "DAI");
            console.log("Eth Collateral:", auction.lot / 1e18, "ETH");

            console.log("DAI to Pay:", auction.debt / 1e18, "DAI");  // 需要支付的DAI是拍卖的债务金额
            console.log("ETH to Receive:", auction.lotSize / 1e18, "ETH");  // 可获得的ETH是拍卖的抵押品数量
            
            console.log("User:", auction.usr);
            console.log("Start time:", timestampToDateTime(auction.tic));
            console.log("NeedRedo:", auction.needsRedo);
            console.log("Initial price:", auction.top / 1e27);
            console.log("Current price:", auction.price / 1e27);
            
            // 获取抵押品参数 - 一次性获取所有参数
            Ilk memory ilk = daiMinter.vat().ilks(auction.ilk);
            //(uint256 Art, uint256 rate, uint256 spot, uint256 line, uint256 dust) = daiMinter.vat().ilks(auction.ilk);
            
            // 打印抵押品价格信息
            console.log("\nPrice Information:");
            console.log("Collateral Safe Price (spot):", ilk.spot / 1e27, "DAI/ETH");
            
            // 计算清算比率
            uint256 liquidationRatio = 0;
            bytes32 ilkType = auction.ilk;
            if (ilkType == daiMinter.ETH_A_ILK()) {
                liquidationRatio = 150; // ETH-A 150%
            } else if (ilkType == daiMinter.ETH_B_ILK()) {
                liquidationRatio = 130; // ETH-B 130%
            } else if (ilkType == daiMinter.ETH_C_ILK()) {
                liquidationRatio = 175; // ETH-C 175%
            } else {
                liquidationRatio = 150; // 默认值
            }
            
            if (ilk.spot > 0) {
                // 根据清算比率计算估计市场价格
                uint256 estimatedPrice = (ilk.spot * 100) / liquidationRatio / 1e27;
                console.log("Estimated Market Price:", estimatedPrice, "DAI/ETH");
                
                // 更进一步优化价格比较显示
                int256 safePercentage;
                if (auction.price < ilk.spot) {
                    safePercentage = -int256(((ilk.spot - auction.price) * 100) / ilk.spot);
                } else {
                    safePercentage = int256(((auction.price - ilk.spot) * 100) / ilk.spot);
                }
                // 安全价格比较
                console.log("Price vs Safe Price:", safePercentage >= 0 ? "+" : "", vm.toString(safePercentage), "%");
                
                int256 marketPercentage;
                if (auction.price < estimatedPrice * 1e27) {
                    marketPercentage = -int256(((estimatedPrice * 1e27 - auction.price) * 100) / (estimatedPrice * 1e27));
                } else {
                    marketPercentage = int256(((auction.price - estimatedPrice * 1e27) * 100) / (estimatedPrice * 1e27));
                }
                // 市场价格比较
                console.log("Price vs Market Price:", marketPercentage >= 0 ? "+" : "", vm.toString(marketPercentage), "%");
            } else {
                console.log("Using estimated price of 100 DAI/ETH for calculations");
            }
            
            // 打印收益信息
            console.log("\nProfit Information:");
            console.log("Mintable DAI:", auction.mintableDai / 1e18, "DAI");
            console.log("Potential Profit:", auction.mintableDai > auction.debt ? (auction.mintableDai - auction.debt) / 1e18 : 0, "DAI");
            
            // 打印抵押率信息 - 改进计算方法区分不同抵押品
            console.log("\nCollateralization Information:");
            
            // 显示必要参数
            console.log("Collateral Parameters:");
            console.log("- Spot value:", ilk.spot / 1e27, "(collateral price * liquidation ratio)");
            console.log("- Current Debt:", ilk.Art * ilk.rate / 1e45, "DAI");
            console.log("- Debt Ceiling:", ilk.line / 1e45, "DAI");
            
            // 根据抵押品类型计算抵押率
            uint256 collateralizationRatio = 0;
            
            // 安全计算抵押率
            if (auction.debt > 0 && ilk.rate > 0 && ilk.spot > 0) {
                // 抵押品价值 = lotSize * spot
                uint256 collateralValue = (auction.lotSize * ilk.spot) / 1e27; // 转为WAD单位
                
                // 债务价值 = debt (已经是WAD单位)
                uint256 debtValue = auction.debt;
                
                // 抵押率 = 抵押品价值 / 债务价值 * 100%
                if (debtValue > 0) {
                    // 避免溢出的计算方式
                    if (collateralValue > type(uint256).max / 100) {
                        collateralizationRatio = (collateralValue / debtValue) * 100;
                    } else {
                        collateralizationRatio = (collateralValue * 100) / debtValue;
                    }
                    console.log("Collateral Value:", collateralValue / 1e18, "DAI");
                    console.log("Debt Value:", debtValue / 1e18, "DAI");
                }
            }
            
            console.log("Current Collateralization Ratio:", collateralizationRatio, "%");
            
            // 根据抵押品类型显示所需抵押率
            console.log("Required Minimum Ratio:", liquidationRatio, "%");
            
            // 判断抵押率是否安全
            if (collateralizationRatio < liquidationRatio) {
                console.log("WARNING: Collateralization ratio below minimum requirement!");
            } else {
                console.log("Collateralization ratio is above minimum requirement.");
            }
            
            // 打印参与条件
            console.log("DAI to receive:", auction.mintableDai / 1e18, "DAI");

            console.log("------------------------");
        }
        
        console.log("\n=== End of Liquidations List ===\n");
        
        vm.stopPrank();
    }
    
    function testParticipateInAuctionAndMint() public {
        // 首先给合约一些WETH
        address WETH = ******************************************;
        
        // 获取VAT地址
        //address VAT = ******************************************;
        
        // 获取DAI_JOIN地址
        address DAI_JOIN = ******************************************;
        
        // 授权DAI_JOIN使用合约的DAI
        //vm.prank(address(daiMinter));
        //ERC20(DAI).approve(DAI_JOIN, type(uint256).max);
        
        // 获取最佳拍卖
        DaiMinter.AuctionData[] memory auctions = daiMinter.getAllLiquidatableAuctions();
        //console.log("Total auctions found:", auctions.length);
        //(bytes32 bestIlk, uint256 bestAuctionId, uint256 potentialProfit) = daiMinter.findBestAuction();
        if(auctions.length > 0) {
            for(uint i = 0; i < auctions.length; i++) {
                DaiMinter.AuctionData memory best = auctions[i];
                if(i == 1) continue;

                bytes32 bestIlk = best.ilk;
                uint256 bestAuctionId = best.auctionId;
                uint256 potentialProfit = best.mintableDai;

                console.log("\n=== Starting Auction Participation Test ===");
                console.log("Best auction found - Collateral Type:", string(abi.encodePacked(bestIlk)));
                console.log("Auction ID:", bestAuctionId);
                console.log("Mintable DAI:", potentialProfit / 1e18, "DAI");
                console.log("Debt:", best.debt / 1e18, "DAI");
                console.log("Collateral:", best.lotSize / 1e18, "ETH");
                console.log("User:", best.usr);
                console.log("Start time:", timestampToDateTime(best.tic));
                console.log("Initial price:", best.top / 1e27);
                console.log("Current price:", best.price / 1e27);
                            // 参与拍卖并铸造DAI
                daiMinter.participateInAuctionAndMint(bestIlk, bestAuctionId);
            }
        } else {
            console.log("\nNo auctions available to participate in.");
        }
        vm.stopPrank();
    }

    // 修改后的时间转换函数
    function timestampToDateTime(uint256 timestamp) internal pure returns (string memory) {
        uint256 secondsInDay = 86400;
        int256 year;
        int256 month;
        int256 day;
        int256 hour;
        int256 minute;
        int256 second;
        
        // 计算基本时间单位
        second = int256(timestamp % 60);
        timestamp /= 60;
        minute = int256(timestamp % 60);
        timestamp /= 60;
        hour = int256(timestamp % 24);
        timestamp /= 24;
        
        // 计算年份（从1970年开始）
        int256 _days = int256(timestamp);
        for (year = 1970; ; year++) {
            // 修复错误1：显式转换类型
            int256 daysInYear = isLeapYear(uint256(year)) ? int256(366) : int256(365);
            if (_days < daysInYear) break;
            _days -= daysInYear;
        }
        
        // 计算月份
        // 修复错误2：显式定义int256数组
        int256[12] memory monthDays = [
            int256(31), 28, 31, 30, 31, 30,
            31, 31, 30, 31, 30, 31
        ];
        if (isLeapYear(uint256(year))) monthDays[1] = 29;
        
        for (month = 0; month < 12; month++) {
            if (_days < monthDays[uint256(month)]) break;
            _days -= monthDays[uint256(month)];
        }
        month += 1;
        
        // 计算日
        day = _days + 1;
        
        // 修复错误3：使用vm.toString()转换
        return string(abi.encodePacked(
            vm.toString(uint256(year)), "/",
            vm.toString(uint256(month)), "/",
            vm.toString(uint256(day)), " ",
            _twoDigits(uint256(hour)), ":",
            _twoDigits(uint256(minute)), ":",
            _twoDigits(uint256(second))
        ));
    }

    function isLeapYear(uint256 year) internal pure returns (bool) {
        if (year % 4 != 0) return false;
        if (year % 100 != 0) return true;
        if (year % 400 != 0) return false;
        return true;
    }

    function _twoDigits(uint256 n) internal pure returns (string memory) {
        if (n < 10) return string(abi.encodePacked("0", vm.toString(n)));
        return vm.toString(n);
    }
}