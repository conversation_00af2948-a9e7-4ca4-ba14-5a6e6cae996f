// SPDX-License-Identifier: MIT
pragma solidity ^0.8.13;

import "forge-std/Test.sol";
import "../../contracts/dai/dai.sol";

contract DaiMakerTest is Test {
    DaiMaker public daiMaker;
    
    // MakerDAO 主网合约地址
    address public constant WETH = ******************************************;
    address public constant DAI = ******************************************;
    
    address public user = address(1);
    uint public userEthBalance = 1000 ether;
    
    function setUp() public {
        // 部署合约
        daiMaker = new DaiMaker();
        
        // 设置用户余额
        vm.deal(user, userEthBalance);
        
        // 模拟ETH价格
        // 在实际测试中，你需要正确模拟MakerDAO的预言机价格
    }
    
    function testOpenCdp() public {
        // 切换到用户
        vm.startPrank(user);
        
        // 创建金库
        uint cdpId = daiMaker.openCdp();
        
        // 验证金库被创建
        assertEq(daiMaker.cdps(user), cdpId);
        
        vm.stopPrank();
    }
    
    function testDepositAndBorrow() public {
        // 切换到用户
        vm.startPrank(user);
        
        // 创建金库
        uint cdpId = daiMaker.openCdp();
        console.log("cdpId", cdpId);
        // 存入ETH并铸造DAI
        uint depositAmount = 100 ether;
        uint borrowAmount = 1 * 1e18; // 1 DAI
        
        // 模拟主网分叉环境（需要Foundry的fork功能）
        // 使用 setUp()中定义的分叉
        
        // 这部分在真实的主网分叉环境中会起作用
        // 由于我们无法在此环境中fork主网，这仅作为演示
        daiMaker.depositAndBorrow{value: depositAmount}(cdpId, borrowAmount);
        
        // 检查用户DAI余额
        DaiLike dai = DaiLike(DAI);
        assertEq(dai.balanceOf(user), borrowAmount);
        
        // 检查ETH余额变化
        assertEq(user.balance, userEthBalance - depositAmount);
        
        vm.stopPrank();
    }
    
    function testRepayAndWithdraw() public {
        // 此测试需要主网分叉环境
        // 首先执行存款和借款操作
        vm.startPrank(user);
        
        uint cdpId = daiMaker.openCdp();
        uint depositAmount = 1 ether;
        uint borrowAmount = 500 * 1e18; // 500 DAI
        
        daiMaker.depositAndBorrow{value: depositAmount}(cdpId, borrowAmount);
        
        // 模拟用户已经有DAI准备还款
        // 在真实测试中，用户已经从上一步获得了DAI
        DaiLike dai = DaiLike(DAI);
        deal(address(dai), user, borrowAmount);
        
        // 批准合约使用DAI
        dai.approve(address(daiMaker), borrowAmount);
        
        // 还款并提取ETH
        daiMaker.repayAndWithdraw(cdpId, borrowAmount, depositAmount);
        
        // 验证结果
        assertEq(dai.balanceOf(user), 0);
        assertEq(user.balance, userEthBalance);
        
        vm.stopPrank();
    }
    
    function testGetCdpInfo() public {
        // 此测试同样需要主网分叉环境
        vm.startPrank(user);
        
        uint cdpId = daiMaker.openCdp();
        uint depositAmount = 1 ether;
        uint borrowAmount = 250 * 1e18; // 250 DAI
        
        daiMaker.depositAndBorrow{value: depositAmount}(cdpId, borrowAmount);
        
        // 获取金库信息
        (uint collateral, uint debt, uint ratio) = daiMaker.getCdpInfo(cdpId);
        
        // 验证结果
        assertEq(collateral, depositAmount);
        assertEq(debt, borrowAmount);
        
        // 按照我们的简化计算，1 ETH = $1000，则抵押率应为 400%
        // 1 ETH * $1000 / 250 DAI = 4，即 400%
        assertEq(ratio, 400);
        
        vm.stopPrank();
    }
}