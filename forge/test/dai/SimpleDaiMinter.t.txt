// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "../contracts/dai/dai_dk.sol";

interface IERC20 {
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

contract SimpleDaiMinterTest is Test {
    DaiMinter public daiMinter;
    IERC20 public dai;
    
    // 测试账户
    address public user = address(1);
    uint256 public userEthBalance = 10 ether;
    
    // MakerDAO DAI地址
    address public constant DAI_ADDR = ******************************************;
    
    function setUp() public {
        // 部署合约
        daiMinter = new DaiMinter();
        
        // 初始化DAI接口
        dai = IERC20(DAI_ADDR);
        
        // 为测试用户分配ETH
        vm.deal(user, userEthBalance);
        
        // 注意: 这个测试需要以太坊主网分叉
        // forge test --match-path test/DaiMinter.t.sol -vv --fork-url $ETH_RPC_URL
    }
    
    function testGetMaxSafeDai() public {
        // 查询1 ETH可以安全铸造的DAI数量
        uint256 maxSafeDai = daiMinter.getMaxSafeDai(1 ether);
        
        // 输出结果（具体值取决于当前市场条件）
        console.log("Max safe DAI for 1 ETH:", maxSafeDai);
        
        // 确保返回了有效值
        assertTrue(maxSafeDai > 0, "Max safe DAI should be greater than 0");
    }
    
    function testMintDai() public {
        // 切换到测试用户
        vm.startPrank(user);
        
        // 存入的ETH数量
        uint256 depositAmount = 1 ether;
        
        // 铸造的DAI数量（使用安全值的一小部分）
        uint256 daiAmount = 0.01 ether; // 只铸造0.01 DAI，确保安全
        
        // 记录初始DAI余额
        uint256 initialDaiBalance = dai.balanceOf(user);
        
        // 执行存款和铸造
        daiMinter.mintDai{value: depositAmount}(daiAmount);
        
        // 验证ETH余额减少
        assertEq(user.balance, userEthBalance - depositAmount, "ETH balance should decrease");
        
        // 验证DAI余额增加
        // 注意：在实际测试中，可能需要考虑舍入误差
        assertApproxEqAbs(
            dai.balanceOf(user),
            initialDaiBalance + daiAmount,
            1, // 允许1 wei的误差
            "DAI balance should increase by the requested amount"
        );
        
        // 结束用户模拟
        vm.stopPrank();
    }
    
    function testMintMaxSafeDai() public {
        // 切换到测试用户
        vm.startPrank(user);
        
        // 存入的ETH数量
        uint256 depositAmount = 1 ether;
        
        // 获取最大安全铸造量
        uint256 maxSafeDai = daiMinter.getMaxSafeDai(depositAmount);
        
        // 记录初始DAI余额
        uint256 initialDaiBalance = dai.balanceOf(user);
        
        // 尝试铸造最大安全数量的DAI
        daiMinter.mintDai{value: depositAmount}(maxSafeDai);
        
        // 验证ETH余额减少
        assertEq(user.balance, userEthBalance - depositAmount, "ETH balance should decrease");
        
        // 验证DAI余额增加
        // 注意：实际铸造的数量可能略小于请求的数量
        uint256 minExpectedDai = maxSafeDai * 99 / 100; // 允许1%的误差
        assertTrue(
            dai.balanceOf(user) >= initialDaiBalance + minExpectedDai,
            "DAI balance should increase by at least 99% of the requested amount"
        );
        
        // 输出结果
        console.log("Requested DAI:", maxSafeDai);
        console.log("Actual minted DAI:", dai.balanceOf(user) - initialDaiBalance);
        
        // 结束用户模拟
        vm.stopPrank();
    }
    
    function testFailExceedingMaxDai() public {
        // 切换到测试用户
        vm.startPrank(user);
        
        // 存入的ETH数量
        uint256 depositAmount = 1 ether;
        
        // 获取最大安全铸造量并超出
        uint256 maxSafeDai = daiMinter.getMaxSafeDai(depositAmount);
        uint256 excessiveDaiAmount = maxSafeDai * 2; // 请求两倍的安全铸造量
        
        // 这个调用应该会失败
        daiMinter.mintDai{value: depositAmount}(excessiveDaiAmount);
        
        // 结束用户模拟
        vm.stopPrank();
    }
    
    // 仅在主网分叉环境下测试
    function testMainnetForkInteraction() public {
        // 跳过非主网分叉环境
        if (block.chainid != 1) {
            console.log("Skipping this test on non-mainnet fork");
            return;
        }
        
        vm.startPrank(user);
        
        // 存入一小笔ETH
        uint256 smallDeposit = 0.1 ether;
        
        // 保守铸造金额
        uint256 conservativeDaiAmount = 0.001 ether;
        
        // 执行存款和铸造
        try daiMinter.mintDai{value: smallDeposit}(conservativeDaiAmount) {
            // 交易成功，验证DAI余额增加
            assertTrue(
                dai.balanceOf(user) >= conservativeDaiAmount,
                "Should have minted some DAI"
            );
            console.log("Successfully minted DAI on mainnet fork");
        } catch Error(string memory reason) {
            // 交易失败，输出原因
            console.log("Transaction failed on mainnet fork:", reason);
            // 失败不一定意味着测试失败，因为主网状态可能与测试预期不同
        }
        
        vm.stopPrank();
    }
} 