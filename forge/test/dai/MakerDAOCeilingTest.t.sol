// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "forge-std/console.sol";

interface VatLike {
    function frob(bytes32 ilk, address u, address v, address w, int dink, int dart) external;
    function hope(address usr) external;
    function ilks(bytes32 ilk) external view returns (uint256 Art, uint256 rate, uint256 spot, uint256 line, uint256 dust);
    function dai(address usr) external view returns (uint256);
    function Line() external view returns (uint256);
    function debt() external view returns (uint256);
}

interface GemJoinLike {
    function join(address urn, uint wad) external payable;
}

interface WethLike {
    function approve(address usr, uint wad) external returns (bool);
    function deposit() external payable;
    function transfer(address dst, uint wad) external returns (bool);
    function balanceOf(address usr) external view returns (uint256);
}

/**
 * @title MakerDAOCeilingTest
 * @notice 专门用于诊断和测试MakerDAO Vat合约中的ceiling-exceeded错误
 */
contract MakerDAOCeilingTest is Test {
    // MakerDAO核心合约地址
    address public constant WETH = ******************************************;
    address public constant VAT = ******************************************;
    address public constant ETH_A_JOIN = ******************************************;
    
    // ETH-A抵押品类型
    bytes32 public constant ETH_A_ILK = 0x4554482d41000000000000000000000000000000000000000000000000000000;
    
    // 接口实例
    VatLike public vat;
    WethLike public weth;
    GemJoinLike public ethJoin;
    
    // 测试用户
    address public tester;
    
    function setUp() public {
        // 初始化接口
        vat = VatLike(VAT);
        weth = WethLike(WETH);
        ethJoin = GemJoinLike(ETH_A_JOIN);
        
        // 创建测试账户
        tester = address(this);
        
        // 授权
        vat.hope(address(this));
        
        // 注意: 本测试需要以太坊主网分叉环境
    }
    
    /**
     * @notice 诊断当前Vat合约的债务上限状态
     */
    function testDiagnoseCeilingExceeded() public {
        // 获取全局债务上限
        uint256 globalDebtCeiling = vat.Line();
        
        // 获取当前总债务
        uint256 currentDebt = vat.debt();
        
        // 获取ETH-A的参数
        (uint256 art, uint256 rate, uint256 spot, uint256 line, uint256 dust) = vat.ilks(ETH_A_ILK);
        
        // 计算当前ETH-A的总债务
        uint256 currentIlkDebt = art * rate;
        
        // 输出结果
        console.log("--- Global Debt Parameters ---");
        console.log("Total Debt Ceiling (Line):", globalDebtCeiling);
        console.log("Current Total Debt (debt):", currentDebt);
        console.log("Remaining Available Debt:", globalDebtCeiling > currentDebt ? globalDebtCeiling - currentDebt : 0);
        console.log("Global Debt Utilization Rate:", (currentDebt * 100) / globalDebtCeiling, "percent");
        
        console.log("--- ETH-A Parameters ---");
        console.log("Current Total Normalized Debt (Art):", art);
        console.log("Accumulated Rate (rate):", rate);
        console.log("Safe Price (spot):", spot);
        console.log("Debt Ceiling (line):", line);
        console.log("Minimum Debt (dust):", dust);
        console.log("Current Actual Debt:", currentIlkDebt);
        console.log("Remaining Available Debt:", line > currentIlkDebt ? line - currentIlkDebt : 0);
        console.log("Debt Utilization Rate:", (currentIlkDebt * 100) / line, "percent");
        
        // 检查是否超过债务上限
        bool isGlobalCeilingExceeded = currentDebt >= globalDebtCeiling;
        bool isIlkCeilingExceeded = currentIlkDebt >= line;
        
        console.log("--- Diagnostic Results ---");
        if (isGlobalCeilingExceeded) {
            console.log("Global Debt Ceiling Exceeded! This is causing the ceiling-exceeded error");
        } else {
            console.log("Global Debt Ceiling Not Exceeded");
        }
        
        if (isIlkCeilingExceeded) {
            console.log("ETH-A Debt Ceiling Exceeded! This is causing the ceiling-exceeded error");
        } else {
            console.log("ETH-A Debt Ceiling Not Exceeded");
        }
    }
    
    /**
     * @notice 测试不同的铸造数量，找到最大可铸造的DAI
     */
    function testFindMaxMintableAmount() public {
        // 测试参数
        uint256 ethAmount = 1 ether;
        
        // 获取ETH-A的参数
        (uint256 art, uint256 rate, uint256 spot, uint256 line, uint256 dust) = vat.ilks(ETH_A_ILK);
        
        // 计算当前ETH-A的总债务
        uint256 currentIlkDebt = art * rate;
        
        // 计算剩余可用债务
        uint256 remainingDebt = 0;
        if (line > currentIlkDebt) {
            remainingDebt = line - currentIlkDebt;
        }
        
        // 计算基于ETH数量和抵押率可铸造的最大DAI
        uint256 maxDaiBasedOnCollateral = (ethAmount * spot) / rate;
        
        // 为安全起见，使用2/3的数量
        uint256 maxSafeDai = maxDaiBasedOnCollateral * 2 / 3;
        
        // 计算考虑债务上限后实际可铸造的最大DAI
        uint256 maxActualDai = maxSafeDai;
        if (maxActualDai > remainingDebt / 1e27) {
            maxActualDai = remainingDebt / 1e27;
        }
        
        console.log("Max Mintable DAI Based on ETH Amount (Ignoring Debt Ceiling):", maxSafeDai);
        console.log("Max Actual Mintable DAI Considering Debt Ceiling:", maxActualDai);
        
        // 如果债务太小，可能无法满足最小债务要求
        if (maxActualDai * 1e27 < dust) {
            console.log("Warning: Mintable Amount Below Minimum Debt Requirement!", dust);
            console.log("Need to Mint at Least:", dust / 1e27, "DAI");
        }
    }
    
    /**
     * @notice 尝试以不同的数量铸造DAI，查看哪些会成功，哪些会失败
     */
    function testTryDifferentMintAmounts() public {
        
        // 测试数量
        uint256 ethAmount = 1000 ether;
        uint256[] memory amountsToTry = new uint256[](5);
        
        // 获取ETH-A参数
        (, uint256 rate, uint256 spot, ,) = vat.ilks(ETH_A_ILK);
        
        // 计算理论上最大可铸造的DAI (使用2/3抵押率)
        uint256 maxSafeDai = ethAmount * spot / rate * 2 / 3;
        
        // 设置不同百分比的铸造量
        amountsToTry[0] = maxSafeDai / 1000;  // 0.1% of max
        amountsToTry[1] = maxSafeDai / 100;   // 1% of max
        amountsToTry[2] = maxSafeDai / 10;    // 10% of max  
        amountsToTry[3] = maxSafeDai / 2;     // 50% of max
        amountsToTry[4] = maxSafeDai;         // 100% of max
        
        // 为测试准备ETH
        deal(address(this), ethAmount);
        
        // 将ETH转换为WETH
        weth.deposit{value: ethAmount}();
        
        // 授权Join适配器使用WETH
        weth.approve(ETH_A_JOIN, ethAmount);
        
        // 将WETH存入系统
        ethJoin.join(address(this), ethAmount);
        
        // 测试不同的铸造量
        console.log("--- Testing Different Mint Amounts ---");
        for (uint i = 0; i < amountsToTry.length; i++) {
            uint256 amount = amountsToTry[i];
            //console.log("Trying to mint:", amount, "DAI (", (amount * 100) / maxSafeDai, "percent of max)");
            console.log("Trying to mint:", amount);

            try vat.frob(
                ETH_A_ILK,
                address(this),
                address(this),
                address(this),
                int(ethAmount),
                int(amount)
            ) {
                console.log("Success: Mint succeeded");
                
                // 清理，恢复状态以便下一次测试
                vat.frob(
                    ETH_A_ILK,
                    address(this),
                    address(this),
                    address(this),
                    -int(ethAmount),
                    -int(amount)
                );
            } catch Error(string memory reason) {
                console.log("Failed:", reason);
            }
        }
    }

    function mintDai(uint256 daiAmount) external payable {
        // 增加抵押品数量可以提高可铸造的DAI上限
        require(msg.value > 0, "Must send ETH");
        
        // 计算需要的ETH数量
        (, uint rate, uint spot, , ) = vat.ilks(ETH_A_ILK);
        uint256 requiredEth = (daiAmount * rate) / spot;
        
        // 确保发送足够的ETH
        require(msg.value >= requiredEth, "Insufficient ETH collateral");
        
        // ... 其余代码保持不变
    }
} 