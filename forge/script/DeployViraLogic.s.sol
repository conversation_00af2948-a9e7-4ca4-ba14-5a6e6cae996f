// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/Script.sol";
import "../contracts/ViraLogic.sol";

contract DeployViraLogic is Script {
    function setUp() public {}

    function run() public {
        // 获取私钥
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        
        // 开始广播交易
        vm.startBroadcast(deployerPrivateKey);

        // 部署合约
        ViraLogic viraLogic = new ViraLogic();
        
        console.log("ViraLogic deployed to:", address(viraLogic));
        
        vm.stopBroadcast();
    }
} 