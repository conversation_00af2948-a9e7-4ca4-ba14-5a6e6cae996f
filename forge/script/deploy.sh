#!/usr/bin/env bash

# 加载环境变量
source .env

# 设置 RPC URL
RPC_URL="https://rpc.pulsechain.com"

# 1. 查询当前 gas 价格
echo "Fetching current gas price..."
GAS_PRICE=$(cast gas-price --rpc-url $RPC_URL)
echo "Current gas price: $GAS_PRICE wei"

# 2. 预估部署需要的 gas limit
echo "Estimating gas limit for deployment..."
GAS_LIMIT=$(forge script script/DeployViraLogic.s.sol:DeployViraLogic \
    --rpc-url $RPC_URL \
    --private-key $PRIVATE_KEY \
    --gas-estimate)
echo "Estimated gas limit: $GAS_LIMIT"

# 3. 计算总 gas 成本
TOTAL_GAS_COST=$((GAS_PRICE * GAS_LIMIT))
TOTAL_GAS_COST_ETH=$(cast --from-wei $TOTAL_GAS_COST)
echo "Total estimated gas cost: $TOTAL_GAS_COST_ETH PLS"

# 确认是否继续
read -p "Do you want to proceed with deployment? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]
then
    exit 1
fi

# 4. 执行部署
echo "Deploying ViraLogic contract..."
forge script script/DeployViraLogic.s.sol:DeployViraLogic \
    --rpc-url $RPC_URL \
    --private-key $PRIVATE_KEY \
    --broadcast \
    --verify \
    -vvvv

echo "Deployment completed!" 