// SPDX-License-Identifier: MIT
pragma solidity ^0.8.13;

import "forge-std/console.sol";

// MakerDAO 接口定义
interface GemJoinLike {
    function join(address usr, uint wad) external;
    function exit(address usr, uint wad) external;
}

interface DaiJoinLike {
    function join(address usr, uint wad) external;
    function exit(address usr, uint wad) external;
}

interface VatLike {
    function hope(address usr) external;
    function nope(address usr) external;
    function ilks(bytes32 ilk) external view returns (uint256 Art, uint256 rate, uint256 spot, uint256 line, uint256 dust);
    function urns(bytes32 ilk, address usr) external view returns (uint256 ink, uint256 art);
    function gem(bytes32 ilk, address usr) external view returns (uint256);
    function dai(address usr) external view returns (uint256);
    function frob(bytes32 ilk, address u, address v, address w, int dink, int dart) external;
}

interface ManagerLike {
    function cdpCan(address, uint, address) external view returns (uint);
    function ilks(uint) external view returns (bytes32);
    function owns(uint) external view returns (address);
    function urns(uint) external view returns (address);
    function open(bytes32, address) external returns (uint);
    function give(uint, address) external;
    function cdpAllow(uint, address, uint) external;
    function frob(uint, int, int) external;
    function flux(uint, address, uint) external;
    function move(uint, address, uint) external;
}

interface DaiLike {
    function approve(address usr, uint wad) external returns (bool);
    function balanceOf(address usr) external view returns (uint);
    function transfer(address dst, uint wad) external returns (bool);
    function transferFrom(address src, address dst, uint wad) external returns (bool);
}

interface WethLike {
    function approve(address usr, uint wad) external returns (bool);
    function deposit() external payable;
    function withdraw(uint wad) external;
}

contract DaiMaker {
    // MakerDAO 主网合约地址
    address public constant WETH = ******************************************;
    address public constant DAI = ******************************************;
    address public constant CDP_MANAGER = ******************************************;
    address public constant ETH_JOIN = ******************************************;
    address public constant DAI_JOIN = ******************************************;
    address public constant VAT = ******************************************;
    
    // ETH-A ilk identifier
    //bytes32 public constant ETH_ILK = 0x4554482d41000000000000000000000000000000000000000000000000000000;
    bytes32 public constant ETH_ILK = "ETH-A"; // 抵押品种类
    //bytes32 public constant ETH_ILK = 0x574254432d410000000000000000000000000000000000000000000000000000;

    // 各合约接口
    WethLike public weth = WethLike(WETH);
    DaiLike public dai = DaiLike(DAI);
    ManagerLike public manager = ManagerLike(CDP_MANAGER);
    VatLike public vat = VatLike(VAT);
    GemJoinLike public ethJoin = GemJoinLike(ETH_JOIN);
    DaiJoinLike public daiJoin = DaiJoinLike(DAI_JOIN);
    
    // 记录用户的金库ID
    mapping(address => uint) public cdps;
    
    constructor() {
        // 给合约授权
        vat.hope(DAI_JOIN);
        vat.hope(ETH_JOIN);
    }
    
    /**
     * @notice 创建一个新的ETH金库
     * @dev 为调用者创建一个新的ETH金库
     * @return cdpId 新创建的金库ID
     */
    function openCdp() public returns (uint) {
        uint cdpId = manager.open(ETH_ILK, address(this));
        cdps[msg.sender] = cdpId;

        
        // 将所有权转移给用户前先授权合约可以操作金库
        // 确保给用户的金库在 manager 层面授权
        manager.cdpAllow(cdpId, address(this), 1);
        manager.give(cdpId, msg.sender);
        
        return cdpId;
    }
    
    /**
     * @notice 存入ETH并铸造DAI
     * @dev 将ETH转换为WETH，存入金库并铸造DAI
     * @param cdpId 金库ID
     * @param daiAmount 要铸造的DAI数量，以wad（18位小数）表示
     */
    function depositAndBorrow(uint cdpId, uint daiAmount) public payable {
        require(manager.owns(cdpId) == msg.sender, "not owner");
        
        // 转换ETH为WETH
        weth.deposit{value: msg.value}();
        
        // 批准ETH_JOIN使用WETH
        weth.approve(ETH_JOIN, msg.value);

        console.log("msg.value:", msg.value);
        
        // 获取金库的urn地址
        address urn = manager.urns(cdpId);
        console.log("urn", urn);

        // 将WETH存入MakerDAO系统
        ethJoin.join(urn, msg.value);

        // 获取金库信息和系统参数
        (uint ink, uint art) = vat.urns(ETH_ILK, urn);
        (, uint rate, uint spot, uint line, ) = vat.ilks(ETH_ILK);
        
        // 打印 rate 和 spot 值
        console.log("ink:", ink);
        console.log("art:", ink);
        console.log("rate:", rate);
        console.log("spot:", spot);
        console.log("line:", line); // 债务上限
        
        require(ink > 0, 'ink e');

        // 使用更安全的计算方式避免溢出
        // 先除以一部分大数再乘以小数，避免中间值溢出
        uint256 maxDai = ink * spot / rate;  // 单位是 RAY
        uint256 safeMaxDai = maxDai * 2 / 3; // 保持150%抵押率

        console.log("maxDai:", maxDai);
        console.log("safeMaxDai:", safeMaxDai);
        console.log("daiAmount:", daiAmount);

        // 直接调用VAT的frob函数
        vat.frob(
            ETH_ILK,
            urn,
            urn,
            address(this),
            int(msg.value),
            int(daiAmount)
        );
            
        // 将DAI从系统取出到用户地址
        manager.move(cdpId, address(this), daiAmount * 1e27);
        daiJoin.exit(msg.sender, daiAmount);
    }
    
    /**
     * @notice 还款并提取ETH
     * @dev 还DAI债务并提取ETH
     * @param cdpId 金库ID
     * @param daiAmount 要还款的DAI数量
     * @param ethAmount 要提取的ETH数量
     */
    function repayAndWithdraw(uint cdpId, uint daiAmount, uint ethAmount) public {
        require(manager.owns(cdpId) == msg.sender, "not owner");
        
        // 将DAI从用户转移到合约
        dai.transferFrom(msg.sender, address(this), daiAmount);
        
        // 批准DAI_JOIN使用DAI
        dai.approve(DAI_JOIN, daiAmount);
        
        // 将DAI存入系统
        daiJoin.join(manager.urns(cdpId), daiAmount);
        
        // 偿还债务并提取ETH
        manager.frob(cdpId, -int(ethAmount), -int(daiAmount * 1e9)); // 乘以1e9转为RAY
        
        // 从系统中提取ETH到合约
        manager.flux(cdpId, address(this), ethAmount);
        ethJoin.exit(address(this), ethAmount);
        
        // 将WETH转换为ETH并发送给用户
        weth.withdraw(ethAmount);
        payable(msg.sender).transfer(ethAmount);
    }
    
    /**
     * @notice 获取金库信息
     * @dev 获取金库的抵押物数量、债务数量和抵押率
     * @param cdpId 金库ID
     * @return collateral 抵押物数量 (ETH)
     * @return debt 债务数量 (DAI)
     * @return ratio 抵押率 (百分比)
     */
    function getCdpInfo(uint cdpId) public view returns (uint collateral, uint debt, uint ratio) {
        (uint ink, uint art) = vat.urns(ETH_ILK, manager.urns(cdpId));
        (, uint rate,,, ) = vat.ilks(ETH_ILK);
        
        collateral = ink; // 单位是wad (18位)
        debt = art * rate / 1e27; // 债务乘以累积利率，转换为wad (18位)
        
        if (debt > 0) {
            // 假设1 ETH = $2000，不依赖于链上预言机，简化计算
            ratio = collateral * 2000 * 100 / debt; // 抵押率百分比
        } else {
            ratio = type(uint).max; // 没有债务时抵押率无限大
        }
    }
    
    // 接收ETH
    receive() external payable {}
}
