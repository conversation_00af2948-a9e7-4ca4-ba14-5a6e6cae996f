// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/console.sol";


interface VatLike {
    function frob(bytes32 ilk, address u, address v, address w, int dink, int dart) external;
    function hope(address usr) external;
    function move(address src, address dst, uint256 rad) external;
    function ilks(bytes32 ilk) external view returns (Ilk memory);
    function dai(address usr) external view returns (uint256);
    function gem(bytes32 ilk, address usr) external view returns (uint256);
    function Line() external view returns (uint256); // 全局债务上限
    function urns(bytes32 ilk, address usr) external view returns (Urn memory);
    function debt() external view returns (uint256);
}

interface DogLike {
    function ilks(bytes32 ilk) external view returns (
        address clip,    // 清算合约
        uint256 chop,    // 清算惩罚
        uint256 hole,    // 清算洞
        uint256 dirt     // 清算污垢
    );
}

interface ClipperLike {
    function take(uint256 id, uint256 amt, uint256 max, address who, bytes calldata data) external;
    function sales(uint256 id) external view returns (
        uint256 pos,      // 拍卖位置
        uint256 tab,      // 债务金额
        uint256 lot,      // 抵押品数量
        address usr,      // 被清算用户
        uint256 tic,      // 拍卖开始时间
        uint256 top       // 初始价格
    );
    function count() external view returns (uint256);  // 获取活跃拍卖数量
    function list() external view returns (uint256[] memory);  // 获取活跃拍卖列表
    function getStatus(uint256 id) external view returns (
        bool needsRedo,   // 是否需要重置
        uint256 price,    // 当前价格
        uint256 lot,      // 抵押品数量
        uint256 tab       // 债务金额
    );
}

interface GemJoinLike {
    function join(address urn, uint wad) external payable;
}

interface DaiJoinLike {
    function join(address usr, uint wad) external;
    function exit(address usr, uint wad) external;
}

interface WethLike {
    function approve(address usr, uint wad) external returns (bool);
    function deposit() external payable;
    function withdraw(uint wad) external;
    function transfer(address dst, uint wad) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
}

interface ERC20Like {
    function approve(address usr, uint wad) external returns (bool);
    function balanceOf(address usr) external view returns (uint256);
    function transfer(address dst, uint wad) external returns (bool);
}

// --- Data ---
struct Ilk {
    uint256 Art;   // Total Normalised Debt     [wad]
    uint256 rate;  // Accumulated Rates         [ray]
    uint256 spot;  // Price with Safety Margin  [ray]
    uint256 line;  // Debt Ceiling              [rad]
    uint256 dust;  // Urn Debt Floor            [rad]
}
struct Urn {
    uint256 ink;   // Locked Collateral  [wad]
    uint256 art;   // Normalised Debt    [wad]
}

contract DaiMinter {
    // 支持多种抵押品类型
    bytes32 public constant ETH_A_ILK = 0x4554482d41000000000000000000000000000000000000000000000000000000;
    bytes32 public constant ETH_B_ILK = 0x4554482d42000000000000000000000000000000000000000000000000000000;
    bytes32 public constant ETH_C_ILK = 0x4554482d43000000000000000000000000000000000000000000000000000000;
    
    //bytes32 public constant WBTC_ILK = 0x574254432d430000000000000000000000000000000000000000000000000000;
    
    // 存储所有支持的抵押品类型
    bytes32[] public supportedIlks;
    
    // 每种抵押品类型对应的Join地址
    mapping(bytes32 => address) public gemJoins;
    
    // 每种抵押品类型对应的Clipper地址
    mapping(bytes32 => address) public clippers;
    
    // MakerDAO核心合约地址
    address public constant WETH = ******************************************;
    address public constant DAI_JOIN = ******************************************;
    address public constant VAT = ******************************************;
    address public constant DAI = ******************************************;
    address public constant DOG = ******************************************;
    address public constant VOW = ******************************************;

    // 接口实例化
    WethLike public weth = WethLike(WETH);
    VatLike public vat = VatLike(VAT);
    DaiJoinLike public daiJoin = DaiJoinLike(DAI_JOIN);
    DogLike public dog = DogLike(DOG);

    constructor() {
        // 初始化必要授权
        vat.hope(DAI_JOIN);
        
        // 添加对Clipper授权账户的hope
        vat.hope(VOW);
        
        // 添加对自身的hope，允许与自己的金库交互
        vat.hope(address(this));

        // 确保DAI已被授权给DAI_JOIN
        try ERC20Like(DAI).approve(DAI_JOIN, type(uint256).max) {
            // DAI approved for DAI_JOIN
        } catch {
            // Failed to approve DAI for DAI_JOIN
        }
        
        // 添加支持的抵押品类型
        supportedIlks.push(ETH_A_ILK);
        supportedIlks.push(ETH_B_ILK);
        supportedIlks.push(ETH_C_ILK);
        
        // 配置每种抵押品类型的Join地址
        gemJoins[ETH_A_ILK] = ******************************************; // ETH-A Join
        gemJoins[ETH_B_ILK] = ******************************************; // ETH-B Join
        gemJoins[ETH_C_ILK] = ******************************************; // ETH-C Join
        
        // 授权所有Join合约
        for (uint i = 0; i < supportedIlks.length; i++) {
            bytes32 ilk = supportedIlks[i];
            address joinAddr = gemJoins[ilk];
            if (joinAddr != address(0)) {
                vat.hope(joinAddr);
            }
        }
        
        // 获取每种抵押品类型的Clipper地址
        for (uint i = 0; i < supportedIlks.length; i++) {
            bytes32 ilk = supportedIlks[i];
            (address clip,,,) = dog.ilks(ilk);
            require(clip != address(0), "Invalid clipper address");
            clippers[ilk] = clip;
            
            // 添加对每个clipper的hope授权
            vat.hope(clip);
        }
    }

    // 定义结构体存储拍卖详情
    struct AuctionData {
        uint256 auctionId;     // 拍卖的唯一标识符
        bytes32 ilk;           // 抵押品类型的标识符（ETH-A, ETH-B等）
        uint256 pos;           // 拍卖在系统中的位置索引
        uint256 tab;           // 原始债务金额（RAD单位，10^45）
        uint256 lot;           // 原始抵押品总量（WAD单位，10^18）
        address usr;           // 被清算的用户地址
        uint256 tic;           // 拍卖开始时间戳
        uint256 top;           // 拍卖初始价格（RAY单位，10^27）
        bool needsRedo;        // 是否需要重置拍卖的标志
        uint256 price;         // 当前拍卖价格（RAY单位，10^27）
        uint256 lotSize;       // 可获得的实际抵押品数量（WAD单位，10^18）
        uint256 debt;          // 需要支付的DAI数量（WAD单位，10^18）
        uint256 mintableDai;   // 使用此抵押品可铸造的最大DAI数量（WAD单位，10^18）
    }

    // --- Math ---
    function _sub(uint x, uint y) internal view returns (uint z) {
        if(x > y) {
            z = x - y;
        } else {
            console.log("x:", x);
            console.log("y:", y);
            z = 0;
        }
    }

    /**
     * @notice 获取单个拍卖的详细信息
     * @param ilk 抵押品类型
     * @param auctionId 拍卖ID
     */
    function _getAuctionData(bytes32 ilk, uint256 auctionId) public view returns (AuctionData memory data) {
        ClipperLike clipper = ClipperLike(clippers[ilk]);
        
        data.auctionId = auctionId;
        data.ilk = ilk;
        
        // 获取拍卖信息
        (
            data.pos,
            data.tab,
            data.lot,
            data.usr,
            data.tic,
            data.top
        ) = clipper.sales(auctionId);
        
        require(data.lot > 0, "Invalid auction");
        
        // 获取拍卖状态
        (data.needsRedo, data.price, data.lotSize, data.debt) = clipper.getStatus(auctionId);
        require(!data.needsRedo, "Auction needs redo");
        
        // 精度转换: MakerDAO系统中的债务(data.debt)通常以RAD(10^45)为单位
        // 而ETH/DAI通常以WAD(10^18)为单位，因此需要转换
        // 将RAD(10^45)转换为WAD(10^18)，除以10^27
        data.debt = data.debt / 1e27;
        
        // 计算可铸造的DAI数量
        //if (data.lotSize > 0) {}

        return data;
    }

    /**
     * @notice 获取所有可清算的拍卖信息，支持多种抵押品类型
     * @return 包含拍卖详情的数组
     */
    function getAllLiquidatableAuctions() public view returns (AuctionData[] memory) {
        // 创建临时存储数组，最大可能的拍卖数量是所有ilk的拍卖总和
        AuctionData[] memory tempAuctions = new AuctionData[](1000); // 设置一个合理的上限
        uint256 count = 0;
        
        // 遍历所有支持的抵押品类型
        for (uint i = 0; i < supportedIlks.length; i++) {
            bytes32 ilk = supportedIlks[i];
            ClipperLike clipper = ClipperLike(clippers[ilk]);
            
            // 获取该抵押品类型的所有活跃拍卖
            uint256[] memory activeAuctions = clipper.list();
            
            // 遍历所有活跃拍卖
            for (uint j = 0; j < activeAuctions.length; j++) {
                uint256 id = activeAuctions[j];
                
                // 获取拍卖状态
                (bool needsRedo, uint256 price, uint256 lot, uint256 tab) = clipper.getStatus(id);
                
                // 检查拍卖是否有效且不需要重置
                if (!needsRedo && lot > 0 && tab > 0) {
                    // 获取拍卖详细信息
                    try this._getAuctionData(ilk, id) returns (AuctionData memory data) {
                        tempAuctions[count] = data;
                        count++;
                    } catch {
                        continue;
                    }
                }
            }
        }
        
        // 创建结果数组，大小为实际找到的拍卖数量
        AuctionData[] memory result = new AuctionData[](count);
        for (uint i = 0; i < count; i++) {
            result[i] = tempAuctions[i];
        }
        
        return result;
    }

    /**
     * @notice 参与拍卖并铸造DAI
     * @param ilk 抵押品类型
     * @param auctionId 拍卖ID
     * 在MakerDAO中，ETH-A拍卖需要使用DAI支付来获取ETH抵押品
     */
    function participateInAuctionAndMint(bytes32 ilk, uint256 auctionId) external payable {
        // 获取拍卖数据
        AuctionData memory data = _getAuctionData(ilk, auctionId);
        
        // 确保有效的拍卖ID
        require(data.auctionId > 0, "Invalid auction ID");
        
        // 打印预执行信息
        _logPreExecutionDetails(data);

        // 获取合约当前DAI余额
        uint256 daiBalance = ERC20Like(DAI).balanceOf(address(this));
        
        // 首先将所有DAI存入Vat系统
        if (daiBalance > 0) {
            // 授权DAI_JOIN使用DAI
            ERC20Like(DAI).approve(DAI_JOIN, daiBalance);
            // 将所有DAI存入Vat系统
            daiJoin.join(address(this), daiBalance);
        }
        
        address clipperAddr = clippers[ilk];
        require(clipperAddr != address(0), "Invalid clipper address");
        
        // 获取拍卖前的gem余额
        uint256 daiBefore = vat.dai(address(this));
        uint256 gemBefore = vat.gem(ilk, address(this));
        console.log("Gem Before:", gemBefore / 1e18, "ETH");
        console.log("Dai Before:", daiBefore / 1e18, "DAI");
        

    // 打印take前的ilk参数
    //Ilk memory ilkBefore = vat.ilks(ilk);
    //console.log("BEFORE TAKE - ilk.Art:", ilkBefore.Art);
    //console.log("BEFORE TAKE - ilk.rate:", ilkBefore.rate / 1e27);
    //console.log("BEFORE TAKE - Art*rate:", ilkBefore.Art * ilkBefore.rate / 1e45, "DAI");
    //console.log("BEFORE TAKE - line:", ilkBefore.line / 1e45, "DAI");
    //console.log("BEFORE TAKE - maxByLine:", (ilkBefore.line - ilkBefore.Art * ilkBefore.rate) / 1e45, "DAI");
    

        // 使用Vat内部DAI余额参与拍卖
        bool auctionSuccess = false;
        string memory failureReason = "";
        
        try ClipperLike(clipperAddr).take(
            auctionId,
            data.lotSize,
            data.price + 1,
            address(this),
            ""
        ) {
            auctionSuccess = true;
        } catch Error(string memory reason) {
            failureReason = reason;
            auctionSuccess = false;
        } catch (bytes memory) {
            failureReason = "ll er"; // 低级错误
            auctionSuccess = false;
        }
        
        if (!auctionSuccess) {
            revert(string(abi.encodePacked("failed: ", failureReason)));
        }


            // 打印take后的ilk参数
    //Ilk memory ilkAfter = vat.ilks(ilk);
    //console.log("AFTER TAKE - ilk.Art:", ilkAfter.Art);
    //console.log("AFTER TAKE - ilk.rate:", ilkAfter.rate / 1e27);
    //console.log("AFTER TAKE - Art*rate:", ilkAfter.Art * ilkAfter.rate / 1e45, "DAI");
    //console.log("AFTER TAKE - line:", ilkAfter.line / 1e45, "DAI");
    //console.log("AFTER TAKE - maxByLine:", (ilkAfter.line - ilkAfter.Art * ilkAfter.rate) / 1e45, "DAI");
    
        
        // 获取拍卖后的gem余额
        uint256 gemBalance = vat.gem(ilk, address(this));
        console.log("Gem After:", gemBalance / 1e18, "ETH");
        console.log("Dai After:", vat.dai(address(this)) / 1e18, "DAI");
        
        // 更新lotSize为实际获得的gem数量
        data.lotSize = gemBalance - gemBefore;
        
        // 将获得的抵押品抵押给MakerDAO，并铸造DAI
        uint256 mintedDai = _mintDai(ilk);

        // 获取mintdai后的gem余额
        gemBalance = vat.gem(ilk, address(this));
        console.log("Gem After Mint:", gemBalance / 1e18, "ETH");
        console.log("Dai After Mint:", vat.dai(address(this)) / 1e18, "DAI");
        
        // 计算总收益 = 铸造的DAI - 支付的DAI
        int256 totalProfit = 0;
        if (mintedDai > data.debt) {
            totalProfit = int256(mintedDai - data.debt);
        } else {
            totalProfit = -int256(data.debt - mintedDai);
        }
        
        _logExecutionResults(data);
    }

    /**
     * @dev 铸造DAI的逻辑，基于当前可用的gem余额
     * @param ilk 抵押品类型
     * @return mintedDai 成功铸造的DAI数量
     */
    function _mintDai(bytes32 ilk) private returns (uint256 mintedDai) {
        // 获取当前金库中的gem余额(尚未锁定的部分)
        uint256 availableGem = vat.gem(ilk, address(this));
        
        // 在调用_calculateMaxMintableDai之前
        /*
        (uint256 Art, uint256 rate, uint256 spot, uint256 line, uint256 dust) = vat.ilks(ilk);
        console.log("Art:", Art);
        console.log("rate:", rate);
        console.log("spot:", spot);
        console.log("line:", line);
        console.log("dust:", dust);
        console.log("global dai balance:", vat.dai(address(this)) / 1e27);
        console.log("getGlobalDebtCeiling:", getGlobalDebtCeiling());
        */

        // 计算可铸造的最大DAI数量
        uint256 maxDaiAmount = _calculateMaxMintableDai(ilk, availableGem);

        console.log("maxDaiAmount:", maxDaiAmount);

        if (maxDaiAmount == 0) {
            return 0;
        }
        
        // 设置铸造数量
        mintedDai = maxDaiAmount;
        
        // 如果计算的mintedDai为0，则不进行任何操作
        if (mintedDai == 0) {
            return 0;
        }
        
        // 这里的dink是增量，即我们要锁定的新抵押品数量
        int256 dink = int256(availableGem);
        // dart是债务的增量
        int256 dart = int256(mintedDai);
        
        // 直接使用vat.frob从内部余额铸造DAI
        try vat.frob(
            ilk,                // 抵押品类型
            address(this),      // urn地址(自己的金库)
            address(this),      // gem来源
            address(this),      // dai目标
            dink,               // 锁定新的gem数量(增量)
            dart                // 铸造新的DAI数量(增量)
        ) {
            // 成功铸造DAI
        } catch Error(string memory reason) {
            revert(string(abi.encodePacked("Failed to mint DAI: ", reason)));
        } catch (bytes memory) {
            revert("Failed to mint DAI with low level error");
        }
        
        return mintedDai;
    }
    
    /***
     * @dev 计算特定抵押品类型的最大可铸造DAI数量，基于金库状态而非特定数量的gem
     * @param ilk 抵押品类型
     * @return maxDai 可铸造的最大DAI数量
     */
    function _calculateMaxMintableDai(bytes32 i, uint256 availableGem) private view returns (uint256 maxDai) {
        // 获取抵押品参数以计算最大可铸造DAI
        //(uint256 Art, uint256 rate, uint256 spot, uint256 line, uint256 dust) = vat.ilks(i);
        //(uint256 ink, uint256 art) = vat.urns(i, address(this));

        Ilk memory ilk = vat.ilks(i);
        // 获取当前已锁定的抵押品
        Urn memory urn = vat.urns(i, address(this));


        // 计算抵押率约束
        // (urn.ink * ilk.spot - urn.art * ilk.rate) / ilk.rate;
        uint256 maxByCollateral = _sub((urn.ink + availableGem) * ilk.spot, urn.art * ilk.rate) / ilk.rate;
        console.log("maxByCollateral:", maxByCollateral / 1e18);
        
        //计算总债务
        // vat.Line() - vat.debt();
        uint256 maxByDebt = _sub(vat.Line(), vat.debt()) / 1e27;
        console.log("maxByDebt:", maxByDebt / 1e18);
        
        // 计算系统债务约束
        // (ilk.line - ilk.Art * ilk.rate) / ilk.rate;
        uint256 maxByLine = _sub(ilk.line, ilk.Art * ilk.rate) / ilk.rate;
        console.log("maxByLine:", maxByLine / 1e18);

        // 取较小值
        maxDai = min3(maxByDebt, maxByLine, maxByCollateral);

        // 确保铸造的DAI不低于最小dust值
        if (maxDai * ilk.rate < ilk.dust) {
            // 如果最大可铸造值低于dust，则设为0避免无效操作
            return 0;
        }

        return maxDai;


        /*
        // 考虑当前锁定和将要锁定的抵押品总和
        uint256 totalCollateral = urn.ink + availableGem;
        // 基于总抵押品计算最大安全债务
        uint256 maxDaiFromCollateral = totalCollateral * ilk.spot / 1e27;

        // 考虑当前债务
        uint256 currentDebt = art * rate / 1e27;
        // 最大可新增债务
        uint256 maxNewDebt = maxDaiFromCollateral > currentDebt ? maxDaiFromCollateral - currentDebt : 0;

        
        // 计算抵押品类型的剩余债务上限
        uint256 remainingDebtCeiling = 0;
        if (line > Art * rate) {
            remainingDebtCeiling = (line - Art * rate) / 1e27; // 转换为WAD单位
        }
        
        // 获取全局债务信息
        uint256 debt = vat.debt();

        // 直接使用RAD单位计算
        uint256 globalCeilingRad = 0;
        try vat.Line() returns (uint256 _line) {
            globalCeilingRad = _line; // 保持RAD单位
        } catch {
            globalCeilingRad = 5000000000 * 1e45; // 50亿DAI，RAD单位
        }
        
        uint256 remainingGlobalCeilingRad = 0;
        if (globalCeilingRad > debt) {
            remainingGlobalCeilingRad = globalCeilingRad - debt;
        }
        
        // 最后再转为WAD单位
        uint256 remainingGlobalCeiling = remainingGlobalCeilingRad / 1e27;

        // 取三个限制因素的最小值
        maxDai = min3(maxNewDebt, remainingDebtCeiling, remainingGlobalCeiling);

        console.log("maxNewDebt:", maxNewDebt);
        console.log("maxDaiFromCollateral:", maxDaiFromCollateral);
        console.log("remainingDebtCeiling:", remainingDebtCeiling);
        
        return maxDai;
        */
    }
    
    function _logPreExecutionDetails(AuctionData memory data) private view {
        // 实现这个函数，但保留空实现
    }
    
    function _logExecutionResults(AuctionData memory data) private view {
        // 实现这个函数，但保留空实现
    }

    /**
     * @dev 拍卖详情日志函数
     */
    function _logAuctionDetails(AuctionData memory data) private view {
        // 实现这个函数，但保留空实现
    }

    /**
     * @notice 接收ETH
     */
    receive() external payable {}

    /**
     * @notice 计算指定抵押品类型的清算比率
     * @param ilk 抵押品类型
     * @return 清算比率（百分比，例如150表示150%）
     */
    function _getLiquidationRatio(bytes32 ilk) internal pure returns (uint256) {
        if (ilk == ETH_A_ILK) {
            return 150; // ETH-A 150%
        } else if (ilk == ETH_B_ILK) {
            return 130; // ETH-B 130%
        } else if (ilk == ETH_C_ILK) {
            return 175; // ETH-C 175%
        } else {
            return 150; // 默认值
        }
    }

    /**
     * @notice 获取全局债务上限
     * @return 全局债务上限，WAD单位(10^18)
     */
    function getGlobalDebtCeiling() public view returns (uint256) {
        // 从VAT合约中读取Line参数(全局债务上限)
        try vat.Line() returns (uint256 globalLine) {
            // Line是以RAD为单位(10^45)，转换为WAD单位(10^18)
            return globalLine / 1e27;
        } catch {
            // 如果调用失败，使用保守估计值
            uint256 conservativeEstimate = 5000000000 * 1e18; // 50亿DAI
            return conservativeEstimate;
        }
    }
    
    /**
     * @notice 返回三个值中的最小值
     * @param a 第一个值
     * @param b 第二个值
     * @param c 第三个值
     * @return 三个值中的最小值
     */
    function min3(uint256 a, uint256 b, uint256 c) internal pure returns (uint256) {
        return a < b ? (a < c ? a : c) : (b < c ? b : c);
    }

    /**
     * @notice 计算预估市场价格
     * @param spot 安全价格 (RAY精度 10^27)
     * @param ilk 抵押品类型
     * @return 估计的市场价格 (WAD精度 10^18)
     */
    function _calculateEstimatedMarketPrice(uint256 spot, bytes32 ilk) internal pure returns (uint256) {
        // 获取抵押品类型的清算比率
        uint256 liquidationRatio = _getLiquidationRatio(ilk);
        
        // spot = 市场价格 * 清算比率 / 100，单位是RAY(10^27)
        // 因此: 市场价格 = spot * 100 / 清算比率 / 10^27
        if (spot > 0) {
            return (spot * 100) / liquidationRatio / 1e27;
        } else {
            // 如果spot为0，返回默认估计值100 DAI/ETH
            return 100;
        }
    }

    /**
     * @notice 计算价格比率（当前价格/预估市场价格），用于确定折扣幅度
     * @param currentPrice 当前拍卖价格 (RAY精度 10^27)
     * @param spot 抵押品安全价格 (RAY精度 10^27)
     * @param ilk 抵押品类型
     * @return 价格比率 (精度 10^18)，较小值表示折扣更大
     */
    function _calculatePriceRatio(uint256 currentPrice, uint256 spot, bytes32 ilk) internal pure returns (uint256) {
        // 估计市场价格，单位是DAI/ETH (WAD精度)
        uint256 estimatedMarketPrice = _calculateEstimatedMarketPrice(spot, ilk);
        
        // 将当前价格从RAY转换为WAD精度
        uint256 currentPriceWad = currentPrice / 1e9;
        
        // 计算比率: 当前价格 / 预估市场价格
        // 较小值表示折扣更大
        if (estimatedMarketPrice > 0) {
            // 避免溢出
            if (currentPriceWad > type(uint256).max / 1e18) {
                return (currentPriceWad / estimatedMarketPrice);
            } else {
                return (currentPriceWad * 1e18) / (estimatedMarketPrice * 1e18);
            }
        } else {
            return type(uint256).max; // 无法计算比率，返回最大值
        }
    }

    /**
     * @notice 寻找最佳拍卖机会
     * @return bestIlk 最佳抵押品类型
     * @return bestAuctionId 最佳拍卖ID
     * @return potentialProfit 潜在收益
     */
    function findBestAuction() public view returns (
        bytes32 bestIlk,
        uint256 bestAuctionId,
        uint256 potentialProfit
    ) {
        // 获取所有可清算的拍卖
        AuctionData[] memory auctions = getAllLiquidatableAuctions();
        
        // 初始化变量
        bestIlk = bytes32(0);
        bestAuctionId = 0;
        potentialProfit = 0;
        uint256 bestPriceRatio = type(uint256).max;
        
        // 遍历所有拍卖
        for (uint i = 0; i < auctions.length; i++) {
            AuctionData memory auction = auctions[i];
            
            // 获取VAT中的抵押品参数
            Ilk memory ilk = vat.ilks(auction.ilk);
            //(uint256 Art, uint256 rate, uint256 spot, uint256 line, uint256 dust) = vat.ilks(auction.ilk);
            
            // 计算价格比率（当前价格/预估市场价格）
            uint256 priceRatio = _calculatePriceRatio(auction.price, ilk.spot, auction.ilk);
            
            // 计算潜在收益
            uint256 currentProfit = 0;
            if (auction.mintableDai > auction.debt) {
                currentProfit = auction.mintableDai - auction.debt;
            }
            
            // 选择价格比率最低的拍卖（折扣最大）
            if (priceRatio < bestPriceRatio && auction.mintableDai > 0) {
                bestPriceRatio = priceRatio;
                bestIlk = auction.ilk;
                bestAuctionId = auction.auctionId;
                potentialProfit = currentProfit;
            }
        }
        
        return (bestIlk, bestAuctionId, potentialProfit);
    }
}