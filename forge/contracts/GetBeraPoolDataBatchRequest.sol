//SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

struct AssetWeight {
    address asset;
    uint256 weight;
}

struct PoolOptions {
    AssetWeight[] weights;
    uint256 swapFee;
}

interface IBeraRouter {
    function getLiquidity(address pool) external view returns (address[] memory asset, uint256[] memory amounts);
    function getPoolName(address pool) external view returns (string memory);
    function getPoolOptions(address pool) external view returns (PoolOptions memory);
    function getTotalShares(address pool) external view returns (address[] memory assets, uint256[] memory amounts);
}

interface IERC20 {
    function symbol() external view returns (string memory);
    function decimals() external view returns (uint8);
}

/**
 @dev This contract is not meant to be deployed. Instead, use a static call with the
      deployment bytecode as payload.
 */
contract GetBeraPoolDataBatchRequest {
    struct PoolData {
        address addr;
        uint256[] weights;
        string[] symbols;
        address[] tokens;
        uint8[] decimals;
        uint256[] reserves;
        uint256 fee;
    }

    constructor(address router, address[] memory pools) {

        PoolData[] memory allPoolData = new PoolData[](pools.length);

        for (uint256 i = 0; i < pools.length; ++i) {
            address poolAddress = pools[i];

            PoolData memory poolData;
            (poolData.tokens, poolData.reserves) = IBeraRouter(router).getLiquidity(poolAddress);
            PoolOptions memory options = IBeraRouter(router).getPoolOptions(poolAddress);

            poolData.addr = poolAddress;
            poolData.decimals = new uint8[](poolData.tokens.length);
            poolData.symbols = new string[](poolData.tokens.length);

            for (uint256 j = 0 ; j < poolData.tokens.length; ++j) {
                address token = poolData.tokens[j];
                poolData.decimals[j] = IERC20(token).decimals();
                poolData.symbols[j] = IERC20(token).symbol();
            }
            poolData.weights = new uint256[](options.weights.length);
            for (uint256 j = 0; j < options.weights.length; ++j) {
                poolData.weights[j] = options.weights[j].weight;
            }
            poolData.fee = options.swapFee;
            allPoolData[i] = poolData;
        }

        
        // ensure abi encoding, not needed here but increase reusability for different return types
        // note: abi.encode add a first 32 bytes word with the address of the original data
        
        //uint256 dataStart = address(this).balance; // Use balance as a dummy operation to get the current memory size
        bytes memory _abiEncodedData = abi.encode(allPoolData);

        assembly {
            // Return from the start of the data (discarding the original data address)
            // up to the end of the memory used
            
            //return(add(_abiEncodedData, 0x20), sub(dataStart, add(_abiEncodedData, 0x20)))
            
            let dataStart := add(_abiEncodedData, 0x20)
            return(dataStart, sub(msize(), dataStart))
        }

    }

    function codeSizeIsZero(address target) internal view returns (bool) {
        if (target.code.length == 0) {
            return true;
        } else {
            return false;
        }
    }
}
