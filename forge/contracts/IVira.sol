// SPDX-License-Identifier: UNLICENSED
import "./ViraData.sol";

pragma solidity ^0.8.0;

// File: @openzeppelin/contracts/token/ERC20/IERC20.sol
interface IERC20 {
    function totalSupply() external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
    function transfer(address recipient, uint256 amount) external returns (bool);
    function allowance(address owner, address spender) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);
    function decimals() external view returns (uint256);
    function withdraw(uint256 wad) external;
    function deposit() external payable;

    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
}

pragma solidity ^0.8.0;

interface IPoolV2 {
    function token0() external view returns (address);
    function token1() external view returns (address);
    //使用V1兼容v1 v2
    //function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function swap(uint256 amount0Out, uint256 amount1Out, address to, bytes calldata data) external;
    
    //function skim(address to) external;
    //for stable
    //function stable() external view returns (bool);
    //function getAmountOut(uint256 amountIn, address tokenIn) external view returns (uint256);
}

pragma solidity ^0.8.0;

interface IPoolV1 {
    //function token0() external view returns (address);
    //function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1);
    function swap(uint256 amount0Out, uint256 amount1Out, address to) external;
    //function skim(address to) external;
}

pragma solidity ^0.8.0;
interface IBeraRouter {
    enum SwapKind {
        GIVEN_IN,
        GIVEN_OUT
    }
    function getLiquidity(address pool) external view returns (address[] memory asset, uint256[] memory amounts);
    function getPreviewSwapExact(SwapKind kind, address pool, address baseAsset, uint256 baseAssetAmount, address quoteAsset) external view returns (address asset, uint256 amount);
    function swap(
        SwapKind kind,
        address poolId,
        address assetIn,
        uint256 amountIn,
        address assetOut,
        uint256 amountOut,
        uint256 deadline
    ) external payable returns (address[] memory assets, uint256[] memory amounts);
}

pragma solidity ^0.8.0;
interface IRouterV2 {
    function factory() external view returns (address);
    function swapExactTokensForTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external returns (uint256[] memory amounts);
    function swapExactTokensForTokensSupportingFeeOnTransferTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external;
}



pragma solidity ^0.8.0;
interface IAdapter {
    function getPair(ViraData.PoolReq memory _p) external view returns (ViraData.PoolData memory p);
    function swap(ViraData.PoolData memory p, uint amountIn, address to) external returns (uint);
    function getReserves(ViraData.PoolData memory _p) external returns (uint rIn, uint rOut);
    function getAmountOut(ViraData.PoolData memory _p, uint amountIn, bool onlyRouterFee) external view returns (uint);
}

pragma solidity ^0.8.0;

interface IFactory {
    function allPairsLength() external view returns (uint256);
    function totalPairs() external view returns (uint256);
    function allPairs(uint256 index) external view returns (address);
}

// File: @openzeppelin/contracts/access/Roles.sol
pragma solidity ^0.8.0;
library Roles {
    struct Role {
        mapping(address => bool) bearer;
    }

    function add(Role storage role, address account) internal {
        require(!has(role, account), "Roles: account already has role");
        role.bearer[account] = true;
    }

    function remove(Role storage role, address account) internal {
        require(has(role, account), "Roles: account does not have role");
        role.bearer[account] = false;
    }

    function has(Role storage role, address account) internal view returns (bool) {
        require(account != address(0), "Roles: account is the zero address");
        return role.bearer[account];
    }
}