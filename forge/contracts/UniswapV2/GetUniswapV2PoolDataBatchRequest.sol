//SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

interface IPair {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    //function swap(uint256 amount0Out, uint256 amount1Out, address to, bytes calldata data) external;
    //function skim(address to) external;
    //for stable
    function stable() external view returns (bool);
    //function getAmountOut(uint256 amountIn, address tokenIn) external view returns (uint256);
}
/*
interface IPairV1 {
    function token0() external view returns (address);
    function token1() external view returns (address);
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1);
    //function swap(uint256 amount0Out, uint256 amount1Out, address to) external;
    //function skim(address to) external;
}
*/

interface IERC20 {
    function decimals() external view returns (uint8);
    function symbol() external view returns (string memory);
}


/**
 * @dev This contract is not meant to be deployed. Instead, use a static call with the
 *       deployment bytecode as payload.
 */
contract GetUniswapV2PoolDataBatchRequest {
    constructor(address[] memory addrs) {
        GetPoolInfoResultDesc[] memory ps = new GetPoolInfoResultDesc[](addrs.length);

        (bool success, bytes memory data) = addrs[0].staticcall(abi.encodeWithSignature("stable()"));
        bool isStable = success && abi.decode(data, (bool));

        for (uint256 i; i < addrs.length; i++) {
            address addr = addrs[i];
            (bool _err, address _token0, address _token1, string memory _s0, string memory _s1, uint8 _d0, uint8 _d1) = getPairBaseInfo(addr);

            GetPoolInfoResultDesc memory p = GetPoolInfoResultDesc({
                token0: _token0,
                token1: _token1,
                s0: _s0,
                s1: _s1,
                r0: 0,
                r1: 0,
                d0: _d0,
                d1: _d1,
                lastUpdate: 0,
                stable: isStable,
                err: _err
            });

            (success, data) = addr.staticcall(abi.encodeWithSignature("getReserves()"));
            //require(success, "getReserves failed");
            if(success){
                if (data.length == 64) {
                    // Uniswap V1: returns 2 parameters
                    (p.r0, p.r1) = abi.decode(data, (uint112, uint112));
                } else if (data.length == 96) {
                    // Uniswap V2: returns 3 parameters
                    (p.r0, p.r1, p.lastUpdate) = abi.decode(data, (uint112, uint112, uint32));
                } else {
                    p.err = true;
                    //revert("Unexpected data length");
                }
            }
            //if(_err)
            ps[i] = p;
        }

        // ensure abi encoding, not needed here but increase reusability for different return types
        // note: abi.encode add a first 32 bytes word with the address of the original data
        bytes memory _abiEncodedData = abi.encode(ps);

        assembly {
            // Return from the start of the data (discarding the original data address)
            // up to the end of the memory used
            let dataStart := add(_abiEncodedData, 0x20)
            return(dataStart, sub(msize(), dataStart))
        }
    }

    struct GetPoolInfoResultDesc {
        //uint256 id;
        //address addr;
        address token0;
        address token1;
        string s0;
        string s1;
        uint112 r0;
        uint112 r1;
        uint8 d0;
        uint8 d1;
        uint32 lastUpdate;
        bool stable;
        bool err;
    }

    //批量更新pair的reserves

    function getPairBaseInfo(address addr)
        internal
        view
        returns (bool err, address token0, address token1, string memory s0, string memory s1, uint8 d0, uint8 d1)
    {
        try IPair(addr).token0() returns (address _token0){
            token0 = _token0;
        } catch {
            err = true;
        }

        try IPair(addr).token1() returns (address _token1){
            token1 = _token1;
        } catch {
            err = true;
        }
        /*
        try IERC20(token0).symbol() returns (string memory _s0){
            s0 = _s0;
        } catch {
            err = true;
        }
        
        try IERC20(token1).symbol() returns (string memory _s1){
            s1 = _s1;
        } catch {
            err = true;
        }
        */
        
        (bool success, bytes memory data) = token0.staticcall(abi.encodeWithSignature("symbol()"));
        if (success) {
            //s0 = abi.decode(abi.encodePacked(data), (string));
            
            s0 = bytesToString(data);
        } else {
            err = true;
        }
        
        (success, data) = token1.staticcall(abi.encodeWithSignature("symbol()"));
        if (success) {
            //s1 = abi.decode(abi.encodePacked(data), (string));
            s1 = bytesToString(data);
        } else {
            err = true;
        }

        try IERC20(token0).decimals() returns (uint8 _d0){
            d0 = _d0;
        } catch {
            err = true;
        }

        try IERC20(token1).decimals() returns (uint8 _d1){
            d1 = _d1;
        } catch {
            err = true;
        }
    }

    function bytesToString(bytes memory _bytes) internal pure returns (string memory) {
        if(_bytes.length < 96){
            //不能正常使用abi.decode，使用字节处理, 去除0的字节
            uint8 i = 0;
            while(i < 32 && _bytes[i] != 0) {
                i++;
            }
            bytes memory bytesArray = new bytes(i);
            for (i = 0; i < 32 && _bytes[i] != 0; i++) {
                bytesArray[i] = _bytes[i];
            }
            return string(bytesArray);
        } else {
            return abi.decode(_bytes, (string));
        }
    }
/*
    function bytesToString(bytes memory _bytes) internal pure returns (string memory) {
        uint8 i = 0;
        while(i < 32 && _bytes[i] != 0) {
            i++;
        }
        bytes memory bytesArray = new bytes(i);
        for (i = 0; i < 32 && _bytes[i] != 0; i++) {
            bytesArray[i] = _bytes[i];
        }
        return string(bytesArray);
    }
*/


/*
    struct PoolData {
        address tokenA;
        uint8 tokenADecimals;
        address tokenB;
        uint8 tokenBDecimals;
        uint112 reserve0;
        uint112 reserve1;
    }
    function codeSizeIsZero(address target) internal view returns (bool) {
        if (target.code.length == 0) {
            return true;
        } else {
            return false;
        }
    }
*/

}
