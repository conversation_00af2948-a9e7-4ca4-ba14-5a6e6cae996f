//SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;


contract GetUniswapV2PoolReservesBatchRequest {
    struct Reserve {
        uint112 reserve0;
        uint112 reserve1;
        uint32 blockTimestampLast;
    }

    constructor(address[] memory addrs) {
        Reserve[] memory reserves = new Reserve[](addrs.length);
        for (uint256 i; i < addrs.length; i++) {
            address addr = addrs[i];
            (bool success, bytes memory data) = addr.staticcall(abi.encodeWithSignature("getReserves()"));
            if(success){
                Reserve memory r;
                if (data.length == 64) {
                    // Uniswap V1: returns 2 parameters
                    (r.reserve0, r.reserve1) = abi.decode(data, (uint112, uint112));
                } else if (data.length == 96) {
                    // Uniswap V2: returns 3 parameters
                    (r.reserve0, r.reserve1, r.blockTimestampLast) = abi.decode(data, (uint112, uint112, uint32));
                } else {

                }
                reserves[i] = r;
            }
        }

        // ensure abi encoding, not needed here but increase reusability for different return types
        // note: abi.encode add a first 32 bytes word with the address of the original data
        bytes memory _abiEncodedData = abi.encode(reserves);

        assembly {
            // Return from the start of the data (discarding the original data address)
            // up to the end of the memory used
            let dataStart := add(_abiEncodedData, 0x20)
            return(dataStart, sub(msize(), dataStart))
        }
    }
}