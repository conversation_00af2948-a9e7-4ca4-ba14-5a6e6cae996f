// SPDX-License-Identifier: UNLICENSED
import "@openzeppelin/contracts/access/Ownable.sol";

import "./ViraData.sol";
import "./IVira.sol";


pragma solidity ^0.8.0;
//pragma experimental ABIEncoderV2;

contract Vira is Ownable {
    using Roles for Roles.Role;

    Roles.Role private _operators;
    address public logicContact;
    mapping(uint256 => address) adapters;

    uint112 constant MAX_UINT112 = 5192296858534827628530496329220095;

    constructor() Ownable(msg.sender) {
        _operators.add(msg.sender);
        _operators.add(address(this));
    }

    //operators
    modifier onlyOperator() {
        require(_operators.has(msg.sender), "Operators: caller is not the Operator");
        _;
    }

    function addOpts(address[] memory operators) external onlyOwner {
        for (uint256 i = 0; i < operators.length; ++i) {
            if (!isOperator(operators[i])) _operators.add(operators[i]);
        }
    }

    function removeOperators(address operator) external onlyOwner {
        _operators.remove(operator);
    }

    function isOperator(address operator) public view returns (bool) {
        return _operators.has(operator);
    }

    function getBalance(address token) public view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    function getBalanceOf(address holder, address token) public view returns (uint256) {
        return IERC20(token).balanceOf(holder);
    }

    function withdrawToEx(address token, address to, uint256 amount) public onlyOwner {
        IERC20(token).transfer(to, amount);
    }

    function withdrawAllEx(address token) external onlyOwner {
        IERC20(token).transfer(msg.sender, IERC20(token).balanceOf(address(this)));
    }

    function withdrawAllToEx(address[] memory tokens, address to) external onlyOwner {
        for (uint256 i; i < tokens.length; i++) {
            IERC20 token = IERC20(tokens[i]);
            uint256 balance = token.balanceOf(address(this));
            if (balance > 0) {
                token.transfer(to, balance);
            }
        }
    }

    function withdrawAllEthEx() external payable onlyOwner {
        payable(msg.sender).transfer(address(this).balance);
    }

    function setLogicContact(address _new) external onlyOwner {
        logicContact = _new;
    }

    /** adapter */
    function setAdapter(uint index, address addr) external onlyOwner {
        adapters[index] = addr;
    }

    function getAdapter(uint index) public view returns (address) {
        return adapters[index];
    }


    /**
     * feed begin **************
     */
    function checkFeed(ViraData.FeedList[] memory feedList) public view returns (ViraData.FeedList[] memory, uint256) {
        uint256 total = 0;
        for (uint256 i; i < feedList.length; i++) {
            uint256 balance = feedList[i].addr.balance;
            feedList[i].balance = balance;
            if (balance < feedList[i].min) {
                uint256 amount = feedList[i].max - balance;
                total = amount + total;
                feedList[i].feedAmount = amount;
            }
        }
        return (feedList, total);
    }

    function feed(ViraData.FeedList[] calldata feedList, address eth, uint256 total) external payable onlyOperator {
        uint256 balance = getBalance(eth);
        require(balance > total, "not enough balance");
        IERC20(eth).withdraw(total);
        for (uint256 i; i < feedList.length; i++) {
            //(bool sent, bytes memory data)
            (bool sent,) = feedList[i].addr.call{value: feedList[i].feedAmount}("");
            require(sent, "Failed to send Ether");
        }
    }
    /**
     * feed end **************
     */

    function _delegate(address implementation) public payable onlyOperator {
        require(implementation != address(0), "error logic address");
        assembly {
            calldatacopy(0, 0, calldatasize())
            let result := delegatecall(gas(), implementation, 0, calldatasize(), 0, 0)
            returndatacopy(0, 0, returndatasize())

            switch result
            case 0 { revert(0, returndatasize()) }
            default { return(0, returndatasize()) }
        }
    }

    receive() external payable {}

    fallback() external payable {
        _delegate(logicContact);
    }
}
