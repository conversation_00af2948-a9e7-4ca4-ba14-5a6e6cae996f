// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

contract ViraData {
    

    enum PumpResult {
        None,
        Success, //1.成功
        SuccessOverlow, //2.成功但是超出流动性
        FindMax0, //3:findmax没有利润
        Balance0, //4:没有钱
        SwapErr, //5:swap出错
        FindMaxErr, //6:计算出有利润，但是实际没有
        RewardTooLow //7:有利润但是不能覆盖gas损失
    }

    struct PumpReq {
        uint8 convertEth; //自动变换eth

        //计算方式
        //0: dydx preview(only v2)
        //1: golden max preview(黄金分割法, gas消耗非常大, 适用于线下求值)
        //2: amountIn preview
        //3: amountIn no preview (存在preview消耗大的pool，preview消耗的gas跟swap一样)
        uint8 calc;

        uint24 gasLimit; //gas消耗
        uint88 cost;     //消耗的价值
        
        uint112 amountIn;
        PoolReq[] pairs;
    }

    struct PoolReq {
        address addr;
        uint256 version; //V1 V2 V3 V4...
        uint256 fee; //单方向swap fee
        uint256 fp; //router fee
        uint inIndex;
        uint outIndex;
    }

    struct PoolData {
        address addr;
        uint256 version; //V1 V2 V3 V4...
        uint256 fee; //单方向swap fee
        uint256 fp; //router fee
        uint inIndex;
        uint outIndex;

        //define by adapter
        bool swapByRouter;
        bool preview; //是否执行getAmountOut后再swap
        address[] t;
        uint256[] r;
    }

    struct FeedList {
        address addr;
        uint256 min;
        uint256 max;
        uint256 balance;
        uint256 feedAmount;
    }
    
}