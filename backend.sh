#!/bin/bash
#echo "[CMD]: ./node_modules/.bin/pm2 start ./node_modules/ts-node/dist/bin.js --node-args='--max-old-space-size=3072' --name $1 --restart-delay 15000 -- src/main.ts $1 $2"
#./node_modules/.bin/pm2 start ./node_modules/ts-node/dist/bin.js --node-args="--max-old-space-size=3072" --name $1 --restart-delay 15000 -- src/main.ts $1 $2

echo "[CMD]: pm2 start src/main.ts --name $1 --restart-delay 15000 -- $1 $2"
if [ $# -ge 2 ]
then
    pm2 start src/main.ts --name $1$2 --restart-delay 15000 -- $1 $2 $3 && pm2 logs $1$2
else 
    pm2 start src/main.ts --name $1 --restart-delay 15000 -- $1 $2 && pm2 logs $1
fi