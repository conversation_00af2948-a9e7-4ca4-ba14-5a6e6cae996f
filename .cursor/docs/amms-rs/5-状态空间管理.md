# 状态空间管理

本文档详细介绍了AMMs-rs库中的状态空间管理系统，该系统负责维护AMM状态、处理区块链重组和提供高效的数据访问。

## 概述

状态空间管理系统是AMMs-rs库的核心组件之一，它提供了一个统一的框架来管理各种AMM的状态。该系统主要解决以下问题：

1. 跟踪和同步AMM状态
2. 处理区块链重组
3. 优化数据访问和缓存
4. 提供过滤机制

## 核心组件

### StateSpaceManager

`StateSpaceManager`是状态空间管理系统的主要入口点：

```rust
#[derive(Clone)]
pub struct StateSpaceManager<N, P> {
    pub state: Arc<RwLock<StateSpace>>,
    pub latest_block: Arc<AtomicU64>,
    pub block_filter: Filter,
    pub provider: P,
    phantom: PhantomData<N>,
}
```

主要职责：
- 管理AMM状态
- 提供区块订阅机制
- 处理日志事件
- 维护最新区块信息

#### 订阅功能

`StateSpaceManager`提供了一个`subscribe`方法，用于订阅区块链事件：

```rust
pub async fn subscribe(
    &self,
) -> Result<
    Pin<Box<dyn Stream<Item = Result<Vec<Address>, StateSpaceError>> + Send>>,
    StateSpaceError,
>
where
    P: Provider<N> + Clone + 'static,
    N: Network<BlockResponse = Block>,
{
    let provider = self.provider.clone();
    let latest_block = self.latest_block.clone();
    let state = self.state.clone();
    let mut block_filter = self.block_filter.clone();

    let block_stream = provider.subscribe_blocks().await?.into_stream();

    Ok(Box::pin(stream! {
        tokio::pin!(block_stream);

        while let Some(block) = block_stream.next().await {
            let block_number = block.number();
            block_filter = block_filter.select(block_number);

            let logs = provider.get_logs(&block_filter).await?;

            let affected_amms = state.write().await.sync(&logs)?;
            latest_block.store(block_number, Ordering::Relaxed);

            yield Ok(affected_amms);
        }
    }))
}
```

这个方法创建一个异步流，该流会：
1. 订阅新区块
2. 获取相关日志
3. 同步AMM状态
4. 返回受影响的AMM地址

### StateSpace

`StateSpace`结构体负责存储和管理AMM状态：

```rust
pub struct StateSpace {
    pub state: HashMap<Address, AMM>,
    pub latest_block: Arc<AtomicU64>,
    cache: StateChangeCache<CACHE_SIZE>,
}
```

主要方法：
- `get`: 获取指定地址的AMM
- `get_mut`: 获取可变的AMM引用
- `sync`: 同步AMM状态

#### 同步方法

`sync`方法是`StateSpace`的核心功能，它处理日志事件并更新AMM状态：

```rust
pub fn sync(&mut self, logs: &[Log]) -> Result<Vec<Address>, StateSpaceError> {
    let mut affected_amms = HashSet::new();

    for log in logs {
        let log_address = log.address;
        if let Some(amm) = self.state.get_mut(&log_address) {
            amm.sync(log)?;
            affected_amms.insert(log_address);
        }
    }

    Ok(affected_amms.into_iter().collect())
}
```

这个方法：
1. 遍历所有日志
2. 找到匹配的AMM
3. 更新AMM状态
4. 返回受影响的AMM地址

### StateChangeCache

`StateChangeCache`提供了一个缓存机制，用于处理区块链重组：

```rust
pub struct StateChangeCache<const N: usize> {
    changes: VecDeque<StateChange>,
}

pub struct StateChange {
    pub block_number: u64,
    pub state: HashMap<Address, AMM>,
}
```

这个缓存存储了最近N个区块的状态变化，允许在发生重组时恢复到先前的状态。

### 构建器模式

状态空间管理系统使用构建器模式来配置和创建`StateSpaceManager`：

```rust
#[derive(Debug, Default)]
pub struct StateSpaceBuilder<N, P> {
    pub provider: P,
    pub latest_block: u64,
    pub factories: Vec<Factory>,
    pub amms: Vec<AMM>,
    pub filters: Vec<PoolFilter>,
    phantom: PhantomData<N>,
}
```

这个构建器提供了流畅的API来配置状态空间：

```rust
pub fn new(provider: P) -> StateSpaceBuilder<N, P> {
    Self {
        provider,
        latest_block: 0,
        factories: vec![],
        amms: vec![],
        filters: vec![],
        phantom: PhantomData,
    }
}

pub fn block(self, latest_block: u64) -> StateSpaceBuilder<N, P> {
    StateSpaceBuilder {
        latest_block,
        ..self
    }
}

pub fn with_factories(self, factories: Vec<Factory>) -> StateSpaceBuilder<N, P> {
    StateSpaceBuilder { factories, ..self }
}

pub fn with_amms(self, amms: Vec<AMM>) -> StateSpaceBuilder<N, P> {
    StateSpaceBuilder { amms, ..self }
}

pub fn with_filters(self, filters: Vec<PoolFilter>) -> StateSpaceBuilder<N, P> {
    StateSpaceBuilder { filters, ..self }
}
```

#### 同步方法

构建器的`sync`方法用于初始化和同步状态空间：

```rust
pub async fn sync(self) -> Result<StateSpaceManager<N, P>, AMMError> {
    let chain_tip = BlockId::from(self.provider.get_block_number().await?);
    let factories = self.factories.clone();
    let mut futures = FuturesUnordered::new();

    // 创建事件过滤器
    let mut filter_set = HashSet::new();
    for factory in &self.factories {
        for event in factory.pool_events() {
            filter_set.insert(event);
        }
    }

    for amm in self.amms.iter() {
        for event in amm.sync_events() {
            filter_set.insert(event);
        }
    }

    let block_filter = Filter::new().event_signature(FilterSet::from(
        filter_set.into_iter().collect::<Vec<FixedBytes<32>>>(),
    ));

    // 按变体分组AMM
    let mut amm_variants = HashMap::new();
    for amm in self.amms.into_iter() {
        amm_variants
            .entry(amm.variant())
            .or_insert_with(Vec::new)
            .push(amm);
    }

    // 为每个工厂创建异步任务
    for factory in factories {
        let provider = self.provider.clone();
        let filters = self.filters.clone();

        let extension = amm_variants.remove(&factory.variant());
        futures.push(tokio::spawn(async move {
            // 发现AMM
            let mut discovered_amms = factory.discover(chain_tip, provider.clone()).await?;

            if let Some(amms) = extension {
                discovered_amms.extend(amms);
            }

            // 应用发现过滤器
            for filter in filters.iter() {
                if filter.stage() == filters::FilterStage::Discovery {
                    let pre_filter_len = discovered_amms.len();
                    discovered_amms = filter.filter(discovered_amms).await?;

                    info!(
                        target: "state_space::sync",
                        factory = %factory.address(),
                        pre_filter_len,
                        post_filter_len = discovered_amms.len(),
                        filter = ?filter,
                        "Discovery filter"
                    );
                }
            }

            // 同步AMM
            discovered_amms = factory.sync(discovered_amms, chain_tip, provider).await?;

            // 应用同步过滤器
            for filter in filters.iter() {
                if filter.stage() == filters::FilterStage::Sync {
                    let pre_filter_len = discovered_amms.len();
                    discovered_amms = filter.filter(discovered_amms).await?;

                    info!(
                        target: "state_space::sync",
                        factory = %factory.address(),
                        pre_filter_len,
                        post_filter_len = discovered_amms.len(),
                        filter = ?filter,
                        "Sync filter"
                    );
                }
            }

            Ok::<Vec<AMM>, AMMError>(discovered_amms)
        }));
    }

    // 等待所有任务完成
    let mut amms = Vec::new();
    while let Some(result) = futures.next().await {
        match result {
            Ok(Ok(mut discovered_amms)) => amms.append(&mut discovered_amms),
            Ok(Err(e)) => return Err(e),
            Err(e) => return Err(AMMError::Eyre(e.into())),
        }
    }

    // 创建状态空间
    let mut state = HashMap::new();
    for amm in amms {
        state.insert(amm.address(), amm);
    }

    let state_space = StateSpace {
        state,
        latest_block: Arc::new(AtomicU64::new(chain_tip.as_u64().unwrap_or_default())),
        cache: StateChangeCache::default(),
    };

    // 创建管理器
    Ok(StateSpaceManager {
        state: Arc::new(RwLock::new(state_space)),
        latest_block: Arc::new(AtomicU64::new(chain_tip.as_u64().unwrap_or_default())),
        block_filter,
        provider: self.provider,
        phantom: PhantomData,
    })
}
```

这个方法执行以下步骤：
1. 获取链尖区块
2. 创建事件过滤器
3. 按变体分组AMM
4. 为每个工厂创建异步任务
5. 发现和同步AMM
6. 应用过滤器
7. 创建状态空间和管理器

## 过滤器系统

状态空间管理系统包含一个强大的过滤器系统，用于筛选AMM：

```rust
pub trait AMMFilter: std::fmt::Debug + Send + Sync {
    fn filter(&self, amms: Vec<AMM>) -> impl Future<Output = Result<Vec<AMM>, AMMError>> + Send;
    fn stage(&self) -> FilterStage;
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum FilterStage {
    Discovery,
    Sync,
}
```

过滤器可以在两个阶段应用：
1. 发现阶段：在发现AMM后应用
2. 同步阶段：在同步AMM后应用

### 常见过滤器

库提供了几种常见的过滤器：

#### 白名单过滤器

```rust
#[derive(Debug)]
pub struct WhitelistFilter {
    pub addresses: HashSet<Address>,
    pub stage: FilterStage,
}
```

只保留白名单中的AMM。

#### 黑名单过滤器

```rust
#[derive(Debug)]
pub struct BlacklistFilter {
    pub addresses: HashSet<Address>,
    pub stage: FilterStage,
}
```

移除黑名单中的AMM。

#### 代币过滤器

```rust
#[derive(Debug)]
pub struct TokenFilter {
    pub tokens: HashSet<Address>,
    pub stage: FilterStage,
}
```

只保留包含指定代币的AMM。

## 错误处理

状态空间管理系统定义了自己的错误类型：

```rust
#[derive(Error, Debug)]
pub enum StateSpaceError {
    #[error("AMM error: {0}")]
    AMM(#[from] AMMError),
    #[error("Provider error: {0}")]
    Provider(#[from] ProviderError),
    #[error("Eyre error: {0}")]
    Eyre(#[from] eyre::Error),
}
```

这个错误类型包装了各种底层错误，提供了统一的错误处理机制。

## 缓存机制

`StateChangeCache`提供了一个简单但有效的缓存机制：

```rust
impl<const N: usize> StateChangeCache<N> {
    pub fn push(&mut self, block_number: u64, state: HashMap<Address, AMM>) {
        if self.changes.len() >= N {
            self.changes.pop_front();
        }
        self.changes.push_back(StateChange { block_number, state });
    }

    pub fn get(&self, block_number: u64) -> Option<&HashMap<Address, AMM>> {
        self.changes
            .iter()
            .find(|change| change.block_number == block_number)
            .map(|change| &change.state)
    }

    pub fn latest(&self) -> Option<&HashMap<Address, AMM>> {
        self.changes.back().map(|change| &change.state)
    }

    pub fn revert_to(&mut self, block_number: u64) -> Option<HashMap<Address, AMM>> {
        let position = self
            .changes
            .iter()
            .position(|change| change.block_number == block_number)?;

        // 移除后面的所有变化
        let len = self.changes.len();
        for _ in position + 1..len {
            self.changes.pop_back();
        }

        self.latest().cloned()
    }
}
```

这个缓存允许：
1. 存储最近N个区块的状态变化
2. 获取特定区块的状态
3. 获取最新状态
4. 在发生重组时回滚到先前的状态

## 使用示例

以下是使用状态空间管理系统的典型示例：

```rust
// 创建提供者
let provider = Provider::new(rpc_url);

// 创建工厂
let factory = UniswapV2Factory::new(
    Address::from_str("******************************************").unwrap(),
    3000,
    10000000,
);

// 创建过滤器
let token_filter = TokenFilter {
    tokens: HashSet::from([
        Address::from_str("******************************************").unwrap(), // WETH
        Address::from_str("******************************************").unwrap(), // USDC
    ]),
    stage: FilterStage::Discovery,
};

// 创建状态空间
let state_space = StateSpaceBuilder::new(provider)
    .block(15000000)
    .with_factories(vec![Factory::UniswapV2(factory)])
    .with_filters(vec![PoolFilter::Token(token_filter)])
    .sync()
    .await?;

// 订阅状态变化
let mut stream = state_space.subscribe().await?;

while let Some(result) = stream.next().await {
    match result {
        Ok(affected_amms) => {
            println!("Affected AMMs: {:?}", affected_amms);
        }
        Err(e) => {
            eprintln!("Error: {:?}", e);
        }
    }
}
```

## 总结

AMMs-rs库的状态空间管理系统提供了一个强大而灵活的框架，用于管理AMM状态、处理区块链重组和优化数据访问。主要特点包括：

1. 统一的AMM状态管理
2. 异步事件处理
3. 缓存机制
4. 可扩展的过滤器系统
5. 构建器模式配置

这些功能使得库能够高效地处理大量AMM，同时保持状态的一致性和准确性。 