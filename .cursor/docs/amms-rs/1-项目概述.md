# AMMs-rs: 自动做市商(AMM)Rust库

## 项目概述

AMMs-rs是一个用Rust编写的高性能库，用于与不同的自动做市商(Automated Market Maker, AMM)协议进行交互。该库提供了一个统一的接口，使应用程序能够与各种DeFi协议交互，同时处理每个协议的特定复杂性。

主要特点：
- 支持多种AMM协议（UniswapV2, UniswapV3, ERC4626金库, Balancer等）
- 统一的API接口，方便不同协议间的交互
- 高性能状态空间管理系统，用于处理区块链重组和状态同步
- 提供价格计算、交换模拟等功能
- 优化的批量请求处理

## 核心组件

AMMs-rs库主要由两个核心模块组成：

1. **amms模块**: 包含AMM接口和各种协议实现
   - AutomatedMarketMaker trait定义了所有AMM实现必须遵循的接口
   - 各种协议的具体实现（UniswapV2, UniswapV3, ERC4626, Balancer）
   - 工厂模式用于创建和发现AMM实例

2. **state_space模块**: 提供状态管理组件
   - 处理区块链重组
   - 管理AMM状态并保持同步
   - 提供缓存机制以提高性能
   - 实现过滤器系统用于AMM发现和同步

## 支持的协议

目前，AMMs-rs支持以下协议：
- **UniswapV2**: 恒定乘积公式AMM
- **UniswapV3**: 集中流动性AMM
- **ERC4626**: 标准化代币化金库
- **Balancer**: 可配置权重的多代币池

## 使用场景

AMMs-rs库适用于以下场景：
- DeFi应用程序需要与多种AMM协议交互
- 价格预言机实现
- 交易机器人和套利策略
- 链上数据分析工具
- 智能合约开发和测试

## 开发指南

有关如何贡献或使用AMMs-rs库的详细信息，请参见：
- [开发环境设置](2-开发环境设置.md)
- [库架构详解](3-核心库架构.md)
- [AMM实现](4-AMM实现.md)
- [状态空间管理](5-状态空间管理.md) 