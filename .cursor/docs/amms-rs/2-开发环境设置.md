# 开发环境设置

本文档提供了设置AMMs-rs库开发环境所需的详细步骤，包括所有必要的依赖项、配置步骤和其他工具。

## 开发环境概述

AMMs-rs库使用Rust构建，智能合约使用Solidity通过Foundry框架编写。开发设置需要同时配置Rust和以太坊开发工具。

## 先决条件

- **Rust**: 1.84版本或更高（项目在CI中使用nightly-2024-11-01）
- **Foundry**: 用于智能合约开发和测试
- **protobuf-compiler**: 某些依赖项需要

## 安装步骤

### 1. 安装Rust

```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
# 选择1进行默认安装
# 安装完成后，添加到PATH
source "$HOME/.cargo/env"
```

### 2. 安装Foundry

```bash
curl -L https://foundry.paradigm.xyz | bash
foundryup
```

### 3. 安装protobuf-compiler

- 在Ubuntu/Debian上:
  ```bash
  sudo apt-get install -y protobuf-compiler
  ```
- 在macOS上:
  ```bash
  brew install protobuf
  ```

### 4. 克隆仓库（包括子模块）

```bash
git clone --recurse-submodules https://github.com/darkforestry/amms-rs.git
cd amms-rs
```

### 5. 环境配置

在项目根目录创建 `.env`文件，包含以下变量：

```
ETHEREUM_PROVIDER=your_ethereum_rpc_url
```

## 项目结构

了解项目结构对有效开发至关重要。AMMs-rs代码库组织如下：

```
amms-rs/
├── .github/           # GitHub工作流配置
├── src/
│   ├── amms/          # AMM接口和实现
│   │   ├── uniswap_v2/
│   │   ├── uniswap_v3/
│   │   ├── erc_4626/
│   │   ├── balancer/
│   │   ├── abi/       # 合约ABI定义
│   │   ├── amm.rs     # 核心AMM接口
│   │   └── ...
│   ├── state_space/   # 状态空间管理组件
│   └── lib.rs         # 库入口点
├── contracts/         # Solidity智能合约
├── benches/           # 性能基准测试
└── examples/          # 使用示例
```

## 开发工作流程

AMMs-rs的典型开发工作流包括以下步骤：

### 1. 设置分支

为您的更改创建新分支：

```bash
git checkout -b feature/your-feature-name
```

### 2. 实现和测试更改

1. 在相关文件中实现您的更改
2. 构建项目：
   ```bash
   cargo build
   ```
3. 运行测试：
   ```bash
   cargo test
   ```

### 3. 智能合约开发

如果您在处理批量请求合约或其他Solidity组件：

1. 进入contracts目录：
   ```bash
   cd contracts
   ```
2. 编译合约：
   ```bash
   forge build
   ```
3. 测试合约：
   ```bash
   forge test
   ```

### 4. 代码格式化

提交更改前，确保代码格式正确：

```bash
cargo fmt --all
cargo clippy -- -D warnings
```

这一点特别重要，因为CI流水线会检查正确的格式。

## 依赖项管理

AMMs-rs的主要依赖项在 `Cargo.toml`中定义，包括：

- alloy: 以太坊交互
- tracing: 日志记录
- tokio: 异步编程支持
- futures: 异步流处理
- rug: 高精度数学计算

确保您了解这些核心依赖项，因为它们在整个代码库中广泛使用。
