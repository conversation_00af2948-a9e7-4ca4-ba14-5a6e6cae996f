# 核心库架构

本文档提供了AMMs-rs库核心架构组件的深入概述，这些组件使库能够与不同的自动做市商(AMM)协议交互。我们将详细分析库的结构、关键接口和影响其功能的设计决策。

## 高级组织结构

AMMs-rs库主要分为两个处理不同关注点的模块：

```rust
pub mod amms;
pub mod state_space;
```

`amms`模块包含核心AMM接口和实现，而 `state_space`模块提供用于管理AMM状态和处理区块链重组的组件。

## AMM接口

AMM接口定义了所有自动做市商实现必须遵循的标准API。核心接口是 `AutomatedMarketMaker` trait，定义在 `src/amms/amm.rs`中：

```rust
#[allow(async_fn_in_trait)]
pub trait AutomatedMarketMaker {
    /// 返回AMM的地址
    fn address(&self) -> Address;

    /// 返回指示AMM应该同步的事件签名
    fn sync_events(&self) -> Vec<B256>;

    /// 同步AMM状态
    fn sync(&mut self, log: &Log) -> Result<(), AMMError>;

    /// 返回AMM中使用的代币地址列表
    fn tokens(&self) -> Vec<Address>;

    /// 计算base_token相对于quote_token的价格
    fn calculate_price(&self, base_token: Address, quote_token: Address) -> Result<f64, AMMError>;

    /// 模拟交换，返回给定amount_in的base_token能换取的quote_token数量
    fn simulate_swap(
        &self,
        base_token: Address,
        quote_token: Address,
        amount_in: U256,
    ) -> Result<U256, AMMError>;

    /// 模拟交换，修改AMM状态，返回给定amount_in的base_token能换取的quote_token数量
    fn simulate_swap_mut(
        &mut self,
        base_token: Address,
        quote_token: Address,
        amount_in: U256,
    ) -> Result<U256, AMMError>;

    /// 初始化空池并同步到指定区块
    async fn init<N, P>(self, block_number: BlockId, provider: P) -> Result<Self, AMMError>
    where
        Self: Sized,
        N: Network,
        P: Provider<N> + Clone;
}
```

这个trait定义了与AMM交互的核心功能，包括：

- 获取AMM地址和代币
- 同步AMM状态
- 计算价格
- 模拟交换
- 初始化AMM

## AMM枚举类型

为了提供统一的接口，库使用了一个枚举类型来包装所有支持的AMM实现：

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AMM {
    UniswapV2Pool(UniswapV2Pool),
    UniswapV3Pool(UniswapV3Pool),
    ERC4626Vault(ERC4626Vault),
    BalancerPool(BalancerPool),
}
```

这个枚举通过宏自动实现了 `AutomatedMarketMaker` trait，将方法调用委托给底层具体实现。

## 工厂模式

AMMs-rs使用工厂模式来创建和发现AMM实例。每种AMM类型都有一个对应的工厂：

```rust
#[derive(Debug, Clone)]
pub enum Factory {
    UniswapV2(UniswapV2Factory),
    UniswapV3(UniswapV3Factory),
    ERC4626(ERC4626Factory),
    Balancer(BalancerFactory),
}
```

工厂负责：

- 创建新的AMM实例
- 发现链上的AMM池
- 处理池创建事件

## AMM实现

### UniswapV2实现

UniswapV2实现基于恒定乘积公式(x * y = k)，主要特点包括：

```rust
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct UniswapV2Pool {
    pub address: Address,
    pub token_a: Token,
    pub token_b: Token,
    pub reserve_0: u128,
    pub reserve_1: u128,
    pub fee: usize,
}
```

关键功能：

- `get_amount_out`: 计算交换后获得的代币数量
- `calculate_price_64_x_64`: 计算价格，使用64.64定点表示
- `swap_calldata`: 生成交换调用数据

### UniswapV3实现

UniswapV3实现支持集中流动性和多费率层级：

```rust
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct UniswapV3Pool {
    pub address: Address,
    pub token_a: Token,
    pub token_b: Token,
    pub fee: usize,
    pub liquidity: u128,
    pub sqrt_price_x96: U256,
    pub tick: i32,
    pub tick_bitmap: HashMap<i16, U256>,
    pub ticks: HashMap<i32, UniswapV3Tick>,
    pub positions: HashMap<B256, UniswapV3Position>,
}
```

关键功能：

- 复杂的价格计算逻辑，基于sqrt(price)表示
- 滴答(tick)管理用于集中流动性
- 位置跟踪

### ERC4626实现

ERC4626实现支持代币化金库标准：

```rust
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ERC4626Vault {
    pub address: Address,
    pub asset: Token,
    pub vault: Token,
    pub total_assets: U256,
    pub total_supply: U256,
}
```

关键功能：

- 资产和份额之间的转换
- 价格计算基于金库份额价值

### Balancer实现

Balancer实现支持多代币池和可配置权重：

```rust
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct BalancerPool {
    pub address: Address,
    pub tokens: Vec<Token>,
    pub balances: Vec<U256>,
    pub weights: Vec<U256>,
    pub swap_fee: U256,
}
```

关键功能：

- 多代币交换逻辑
- 基于权重的价格计算

## 状态空间管理

状态空间管理系统负责维护AMM状态并处理区块链重组：

```rust
#[derive(Clone)]
pub struct StateSpaceManager<N, P> {
    pub state: Arc<RwLock<StateSpace>>,
    pub latest_block: Arc<AtomicU64>,
    pub block_filter: Filter,
    pub provider: P,
    phantom: PhantomData<N>,
}
```

主要组件：

- `StateSpace`: 管理AMM状态的核心结构
- `StateChangeCache`: 缓存状态变化以处理重组
- 过滤器系统：用于AMM发现和同步

### 状态空间构建器

库提供了一个构建器模式来配置状态空间：

```rust
#[derive(Debug, Default)]
pub struct StateSpaceBuilder<N, P> {
    pub provider: P,
    pub latest_block: u64,
    pub factories: Vec<Factory>,
    pub amms: Vec<AMM>,
    pub filters: Vec<PoolFilter>,
    phantom: PhantomData<N>,
}
```

这允许用户：

- 指定区块提供者
- 添加工厂和AMM实例
- 配置过滤器
- 同步状态

## 错误处理

库使用自定义错误类型进行错误处理：

```rust
#[derive(Error, Debug)]
pub enum AMMError {
    #[error("Provider error: {0}")]
    Provider(#[from] ProviderError),
    #[error("Alloy error: {0}")]
    Alloy(#[from] alloy::Error),
    #[error("Sol error: {0}")]
    Sol(#[from] SolError),
    #[error("Eyre error: {0}")]
    Eyre(#[from] eyre::Error),
    #[error("Token not found: {0:?}")]
    TokenNotFound(Address),
    #[error("Invalid swap: {0}")]
    InvalidSwap(String),
    #[error("Math error: {0}")]
    Math(String),
    #[error("UniswapV2 error: {0}")]
    UniswapV2(#[from] UniswapV2Error),
    #[error("UniswapV3 error: {0}")]
    UniswapV3(#[from] UniswapV3Error),
    #[error("ERC4626 error: {0}")]
    ERC4626(#[from] ERC4626Error),
    #[error("Balancer error: {0}")]
    Balancer(#[from] BalancerError),
}
```

## 批量请求优化

为了提高性能，库使用批量请求合约来减少RPC调用：

- `GetUniswapV2PairsBatchRequest`: 批量获取UniswapV2对
- `GetUniswapV2PoolDataBatchRequest`: 批量获取UniswapV2池数据
- `GetUniswapV3PoolDataBatchRequest`: 批量获取UniswapV3池数据
- `GetBalancerPoolDataBatchRequest`: 批量获取Balancer池数据

这些合约允许在单个RPC调用中获取多个池的数据，显著提高了性能。

## 总结

AMMs-rs库的核心架构提供了：

- 统一的AMM接口
- 多种协议实现
- 高效的状态管理
- 优化的数据获取
- 可扩展的设计

这种架构使应用程序能够与不同的AMM协议交互，同时处理每个协议的特定复杂性。
