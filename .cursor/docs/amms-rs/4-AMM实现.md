# AMM实现

本文档详细介绍了AMMs-rs库中支持的各种自动做市商(AMM)协议的实现。每种实现都遵循 `AutomatedMarketMaker` trait，但具有特定于其协议的逻辑。

## 共享常量

所有实现都使用在 `consts.rs`文件中定义的一组共享常量：

```rust
// 精度常量
pub const MPFR_T_PRECISION: u32 = 128;

// U256常量
pub const U256_1: U256 = U256::from(1);
pub const U256_2: U256 = U256::from(2);
pub const U256_4: U256 = U256::from(4);
pub const U256_8: U256 = U256::from(8);
pub const U256_16: U256 = U256::from(16);
pub const U256_32: U256 = U256::from(32);
pub const U256_64: U256 = U256::from(64);
pub const U256_128: U256 = U256::from(128);
pub const U256_255: U256 = U256::from(255);
pub const U256_0X100: U256 = U256::from(0x100);
pub const U256_0X10000: U256 = U256::from(0x10000);
pub const U256_0X100000000: U256 = U256::from(0x100000000);
pub const U256_100000: U256 = U256::from(100000);
pub const U256_191: U256 = U256::from(191);
pub const U256_192: U256 = U256::from(192);
pub const U256_0XFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF: U256 =
    U256::from_be_hex("ffffffffffffffffffffffffffffffff");
pub const U256_0XFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF: U256 =
    U256::from_be_hex("ffffffffffffffffffffffffffffffffffffffffffffffff");

// U128常量
pub const U128_0X10000000000000000: u128 = 0x10000000000000000;
```

这些常量用于各种数学计算和位操作。

## UniswapV2实现

### 核心组件

UniswapV2实现基于恒定乘积公式(x * y = k)，主要组件包括：

```rust
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct UniswapV2Pool {
    pub address: Address,
    pub token_a: Token,
    pub token_b: Token,
    pub reserve_0: u128,
    pub reserve_1: u128,
    pub fee: usize,
}
```

### 关键功能

#### 交换模拟

```rust
fn simulate_swap(
    &self,
    base_token: Address,
    _quote_token: Address,
    amount_in: U256,
) -> Result<U256, AMMError> {
    if self.token_a.address == base_token {
        Ok(self.get_amount_out(
            amount_in,
            U256::from(self.reserve_0),
            U256::from(self.reserve_1),
        ))
    } else {
        Ok(self.get_amount_out(
            amount_in,
            U256::from(self.reserve_1),
            U256::from(self.reserve_0),
        ))
    }
}
```

#### 获取输出金额

```rust
pub fn get_amount_out(&self, amount_in: U256, reserve_in: U256, reserve_out: U256) -> U256 {
    let amount_in_with_fee = amount_in * U256::from(10000 - self.fee);
    let numerator = amount_in_with_fee * reserve_out;
    let denominator = reserve_in * U256::from(10000) + amount_in_with_fee;
    numerator / denominator
}
```

#### 价格计算

```rust
pub fn calculate_price_64_x_64(&self, base_token: Address) -> Result<u128, AMMError> {
    if self.reserve_0 == 0 || self.reserve_1 == 0 {
        return Err(AMMError::Math("Division by zero".to_string()));
    }

    if self.token_a.address == base_token {
        div_uu(
            U256::from(self.reserve_1) * U256::from(10u128.pow(self.token_a.decimals as u32)),
            U256::from(self.reserve_0) * U256::from(10u128.pow(self.token_b.decimals as u32)),
        )
    } else {
        div_uu(
            U256::from(self.reserve_0) * U256::from(10u128.pow(self.token_b.decimals as u32)),
            U256::from(self.reserve_1) * U256::from(10u128.pow(self.token_a.decimals as u32)),
        )
    }
}
```

### 工厂实现

```rust
pub struct UniswapV2Factory {
    pub address: Address,
    pub fee: usize,
    pub creation_block: u64,
}
```

工厂提供了发现和同步池的方法：

```rust
pub async fn get_all_pairs<N, P>(
    factory_address: Address,
    block_number: BlockId,
    provider: P,
) -> Result<Vec<Address>, AMMError>
where
    N: Network,
    P: Provider<N> + Clone,
{
    // 实现获取所有交易对的逻辑
}

pub async fn sync_all_pools<N, P>(
    amms: Vec<AMM>,
    block_number: BlockId,
    provider: P,
) -> Result<Vec<AMM>, AMMError>
where
    N: Network,
    P: Provider<N> + Clone,
{
    // 实现同步所有池的逻辑
}
```

## UniswapV3实现

### 核心组件

UniswapV3实现支持集中流动性和多费率层级：

```rust
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct UniswapV3Pool {
    pub address: Address,
    pub token_a: Token,
    pub token_b: Token,
    pub fee: usize,
    pub liquidity: u128,
    pub sqrt_price_x96: U256,
    pub tick: i32,
    pub tick_bitmap: HashMap<i16, U256>,
    pub ticks: HashMap<i32, UniswapV3Tick>,
    pub positions: HashMap<B256, UniswapV3Position>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UniswapV3Tick {
    pub tick_idx: i32,
    pub liquidity_gross: u128,
    pub liquidity_net: i128,
    pub fee_growth_outside_0_x128: U256,
    pub fee_growth_outside_1_x128: U256,
    pub initialized: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UniswapV3Position {
    pub liquidity: u128,
    pub fee_growth_inside_0_last_x128: U256,
    pub fee_growth_inside_1_last_x128: U256,
    pub tokens_owed_0: u128,
    pub tokens_owed_1: u128,
}
```

### 关键功能

#### 交换模拟

UniswapV3的交换模拟更为复杂，需要考虑滴答(tick)和集中流动性：

```rust
fn simulate_swap(
    &self,
    base_token: Address,
    quote_token: Address,
    amount_in: U256,
) -> Result<U256, AMMError> {
    // 复杂的交换逻辑，考虑滴答和流动性
}
```

#### 价格计算

```rust
fn calculate_price(&self, base_token: Address, _quote_token: Address) -> Result<f64, AMMError> {
    let sqrt_price_x96 = self.sqrt_price_x96;
    let decimals_a = self.token_a.decimals as u32;
    let decimals_b = self.token_b.decimals as u32;

    // 计算价格，考虑代币小数位和sqrt_price_x96表示
}
```

### 滴答管理

UniswapV3实现包含复杂的滴答管理逻辑，用于处理集中流动性：

```rust
pub fn get_next_initialized_tick_within_one_word(
    &self,
    tick: i32,
    lte: bool,
    tick_spacing: i32,
) -> Result<(i32, bool), UniswapV3Error> {
    // 实现获取下一个初始化滴答的逻辑
}
```

## ERC4626实现

### 核心组件

ERC4626实现支持代币化金库标准：

```rust
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ERC4626Vault {
    pub address: Address,
    pub asset: Token,
    pub vault: Token,
    pub total_assets: U256,
    pub total_supply: U256,
}
```

### 关键功能

#### 价格计算

```rust
fn calculate_price(&self, base_token: Address, _quote_token: Address) -> Result<f64, AMMError> {
    if base_token == self.asset.address {
        // 资产到份额的价格
        if self.total_supply.is_zero() {
            return Ok(1.0);
        }
        let price = self.total_assets.full_mul(U256::from(10).pow(U256::from(self.vault.decimals)))
            / self.total_supply.full_mul(U256::from(10).pow(U256::from(self.asset.decimals)));
        Ok(price.to::<f64>())
    } else {
        // 份额到资产的价格
        if self.total_assets.is_zero() {
            return Ok(1.0);
        }
        let price = self.total_supply.full_mul(U256::from(10).pow(U256::from(self.asset.decimals)))
            / self.total_assets.full_mul(U256::from(10).pow(U256::from(self.vault.decimals)));
        Ok(price.to::<f64>())
    }
}
```

#### 交换模拟

```rust
fn simulate_swap(
    &self,
    base_token: Address,
    _quote_token: Address,
    amount_in: U256,
) -> Result<U256, AMMError> {
    if base_token == self.asset.address {
        // 存款：资产到份额
        if self.total_supply.is_zero() {
            Ok(amount_in)
        } else {
            Ok(amount_in.full_mul(self.total_supply) / self.total_assets)
        }
    } else {
        // 赎回：份额到资产
        Ok(amount_in.full_mul(self.total_assets) / self.total_supply)
    }
}
```

## Balancer实现

### 核心组件

Balancer实现支持多代币池和可配置权重：

```rust
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct BalancerPool {
    pub address: Address,
    pub tokens: Vec<Token>,
    pub balances: Vec<U256>,
    pub weights: Vec<U256>,
    pub swap_fee: U256,
}
```

### 关键功能

#### 交换模拟

Balancer的交换模拟基于加权常数乘积公式：

```rust
fn simulate_swap(
    &self,
    base_token: Address,
    quote_token: Address,
    amount_in: U256,
) -> Result<U256, AMMError> {
    // 找到代币索引
    let base_idx = self.tokens.iter().position(|t| t.address == base_token)
        .ok_or_else(|| AMMError::TokenNotFound(base_token))?;
    let quote_idx = self.tokens.iter().position(|t| t.address == quote_token)
        .ok_or_else(|| AMMError::TokenNotFound(quote_token))?;

    // 计算输出金额，使用加权常数乘积公式
}
```

#### 价格计算

```rust
fn calculate_price(&self, base_token: Address, quote_token: Address) -> Result<f64, AMMError> {
    // 找到代币索引
    let base_idx = self.tokens.iter().position(|t| t.address == base_token)
        .ok_or_else(|| AMMError::TokenNotFound(base_token))?;
    let quote_idx = self.tokens.iter().position(|t| t.address == quote_token)
        .ok_or_else(|| AMMError::TokenNotFound(quote_token))?;

    // 计算价格，考虑代币权重和余额
}
```

## 批量数据获取

所有AMM实现都使用批量请求合约来高效获取数据：

```rust
pub async fn init<N, P>(mut self, block_number: BlockId, provider: P) -> Result<Self, AMMError>
where
    N: Network,
    P: Provider<N> + Clone,
{
    // 使用批量请求合约获取池数据
    let deployer = IGetUniswapV2PoolDataBatchRequestInstance::deploy_builder(
        provider.clone(),
        vec![self.address()],
    );

    let res = deployer.call_raw().block(block_number).await?;

    // 解析结果并更新池状态
}
```

## 与状态空间管理的集成

所有AMM实现都与状态空间管理系统集成，用于维护同步状态：

```rust
fn sync(&mut self, log: &Log) -> Result<(), AMMError> {
    // 解析事件并更新状态
    let sync_event = IUniswapV2Pair::Sync::decode_log(&log.inner)?;

    let (reserve_0, reserve_1) = (
        sync_event.reserve0.to::<u128>(),
        sync_event.reserve1.to::<u128>(),
    );

    // 更新池状态
    self.reserve_0 = reserve_0;
    self.reserve_1 = reserve_1;
    Ok(())
}
```

## 总结

AMMs-rs库中的AMM实现提供了与不同DeFi协议交互的统一接口，同时处理每个协议的特定复杂性。每种实现都遵循 `AutomatedMarketMaker` trait模式，但有专门的逻辑用于：

1. 状态表示
2. 交换模拟
3. 价格计算
4. 事件处理

这种架构使应用程序能够以一致的方式与不同的AMM协议交互，同时保持每个协议特有的数学精度和效率。
