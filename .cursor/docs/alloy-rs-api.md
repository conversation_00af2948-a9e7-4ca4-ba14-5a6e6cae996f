# Alloy-rs API 文档

Alloy 是一个用于与以太坊和其他EVM兼容区块链交互的Rust库集合。它提供了丰富的功能，包括RPC调用、合约交互、交易签名等。

## 核心模块

### alloy-primitives

提供以太坊基本数据类型：

- `Address`: 20字节的以太坊地址
- `U256`: 256位无符号整数
- `B256`: 32字节的哈希值/密钥
- `Bytes`: 动态大小的字节数组

```rust
use alloy_primitives::{Address, U256, bytes};

let address = address!("******************************************");
let value = U256::from(1_000_000_000);
```

### alloy-provider

提供区块链节点通信功能：

- `Provider`: 用于发送RPC请求的trait
- `RootProvider`: 基本Provider实现
- `ProviderBuilder`: 构建Provider的辅助工具

```rust
use alloy_provider::{ProviderBuilder, RootProvider, Provider};
use alloy_network::Ethereum;

// 创建基本HTTP提供者
let provider = RootProvider::<Ethereum>::new_http("https://rpc.example.com".parse()?);

// 获取最新区块号
let block_number = provider.get_block_number().await?;
```

### alloy-sol-types

提供Solidity类型系统的Rust实现：

- `sol!` 宏: 定义合约接口和事件结构
- `SolValue`: 用于Solidity值的序列化和反序列化
- `SolEvent`: 用于解析事件日志

```rust
use alloy_sol_types::{sol, SolValue};

// 定义合约接口
sol! {
    event Transfer(address indexed from, address indexed to, uint256 value);
    
    struct TokenInfo {
        string name;
        string symbol;
        uint8 decimals;
    }
}

// 解码事件日志
let transfer = Transfer::decode_log(&log_entry, true)?;

// 编码和解码结构体
let token_info = TokenInfo { 
    name: "MyToken".into(), 
    symbol: "MTK".into(), 
    decimals: 18 
};
let encoded = token_info.abi_encode();
let decoded = TokenInfo::abi_decode(&encoded)?;
```

### alloy-dyn-abi

提供动态ABI处理功能：

- `DynSolValue`: 表示动态类型的Solidity值
- `JsonAbi`: 解析和处理JSON格式的ABI
- `Function`, `Event`: 表示合约函数和事件

```rust
use alloy_dyn_abi::{DynSolValue, JsonAbi, Error};

// 解析JSON ABI
let abi_json = r#"[{"type":"function","name":"transfer","inputs":[{"name":"to","type":"address"},{"name":"value","type":"uint256"}]}]"#;
let abi = JsonAbi::from_json(abi_json)?;

// 获取函数
let transfer_fn = abi.function("transfer")?;

// 编码函数调用
let args = vec![
    DynSolValue::Address(recipient),
    DynSolValue::Uint(U256::from(amount), 256),
];
let encoded = transfer_fn.abi_encode_input(&args)?;

// 解码函数返回值
let return_data = provider.call(tx).await?;
let decoded = transfer_fn.abi_decode_output(&return_data)?;
```

### alloy-contract

提供智能合约交互功能：

- `sol!` 宏: 定义合约接口
- `SolCallBuilder`: 构建合约调用

```rust
use alloy_contract::SolCallBuilder;
use alloy_sol_types::sol;

sol! {
    #[sol(rpc)]
    #[sol(bytecode = "0x1234")]
    contract MyContract {
        constructor(address) {}
        
        function doStuff(uint a, bool b) public payable returns(address c, bytes32 d);
    }
}

// 部署合约
let contract = MyContract::deploy(&provider, constructor_arg).await?;

// 调用合约方法
let call_builder = contract.doStuff(a, b).value(U256::from(50e18 as u64));
let call_return = call_builder.call().await?;
```

### alloy-rpc-client

提供RPC客户端功能：

- `ReqwestClient`: 基于reqwest的HTTP客户端
- 支持单个和批量RPC请求

```rust
// 单个RPC请求
let client = ClientBuilder::default().http(url);
let request = client.request_noparams("eth_blockNumber");
let block_number = request.await.unwrap();

// 批量RPC请求
let batch = client.new_batch();
let block_number_fut = batch.add_call("eth_blockNumber", ()).unwrap();
let balance_fut = batch.add_call("eth_getBalance", address).unwrap();
batch.send().await.unwrap();
```

### alloy-signer

提供交易和消息签名功能：

- `Signer`: 用于签名的trait
- `PrivateKeySigner`: 本地私钥签名者

```rust
use alloy_signer::{Signer, SignerSync};
use alloy_signer_local::PrivateKeySigner;

// 创建签名者
let signer = PrivateKeySigner::random();

// 签名消息
let message = "Some data";
let signature = signer.sign_message_sync(message.as_bytes())?;

// 签名交易
let signature = signer.sign_transaction_sync(&mut tx)?;
```

### alloy-network

提供网络特定功能：

- `Network` trait: 定义网络特性
- `Ethereum`: 以太坊网络实现
- 支持自定义网络

```rust
// 自定义网络实现
struct Foo;

impl Network for Foo {
    type Transaction = FooTransaction;
    type Block = FooBlock;
    type Header = FooHeader;
    type Receipt = FooReceipt;
}

// 扩展Provider功能
#[async_trait]
trait FooProviderExt: Provider<Foo> {
    async fn custom_foo_method(&self) -> RpcResult<Something, TransportError>;
}
```

## 使用方法

### 安装

添加依赖到Cargo.toml:

```toml
alloy = { version = "1.0.1", features = ["full"] }
```

或使用命令行:

```shell
cargo add alloy --features full
```

### 常见操作

1. **连接到节点**:
   ```rust
   let provider = ProviderBuilder::new().connect("http://localhost:8545").await?;
   ```

2. **查询区块链数据**:
   ```rust
   let block_number = provider.get_block_number().await?;
   let balance = provider.get_balance(address).await?;
   ```

3. **部署合约**:
   ```rust
   let contract = MyContract::deploy(&provider, constructor_arg).await?;
   ```

4. **调用合约方法**:
   ```rust
   let result = contract.my_method(param1, param2).call().await?;
   ```

5. **发送交易**:
   ```rust
   let tx_hash = contract.my_method(param1, param2).send().await?;
   ```

6. **签名消息**:
   ```rust
   let signature = signer.sign_message_sync(message.as_bytes())?;
   ```

## 高级功能

- 批量RPC请求处理
- 自定义网络支持
- 事件监听和过滤
- 交易构建和签名
- EIP-712类型化数据签名

## 更多资源

- [GitHub仓库](https://github.com/alloy-rs/alloy)
- [官方文档](https://docs.rs/alloy)
- [示例代码](https://github.com/alloy-rs/examples) 