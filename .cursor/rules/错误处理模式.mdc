---
description: 
globs: 
alwaysApply: true
---
# 错误处理模式

本项目使用 thiserror 和自定义错误类型进行系统化的错误处理。

## 错误类型

主要错误类型定义在：
- [src/errors.rs](mdc:src/errors.rs)：全局应用程序错误
- [src/vira/errors.rs](mdc:src/vira/errors.rs)：Vira 模块特定错误
- [src/connector/error.rs](mdc:src/connector/error.rs)：连接器特定错误

## 错误设计原则

- 使用 `thiserror` 库定义结构化错误类型
- 使错误具有描述性和可操作性
- 在适当的情况下包括上下文信息
- 使用 `From` 特性实现适当的错误转换
- 在适当的情况下为每个模块使用专门的错误

## 错误传播

- 使用 `?` 操作符进行简洁的错误传播
- 在转换错误类型时提供额外的上下文
- 对于异步函数，确保错误正确地跨 await 点传播

## 错误处理模式

- 使用适当的重试逻辑处理可恢复的错误
- 在适当的级别用适当的上下文记录错误
- 对可能失败的函数使用 Result<T, E>
- 考虑使用 Option<T> 表示可为空的值，而不是特殊的错误情况

## 最佳实践

- 不要在库代码中使用 panic；应返回错误
- 提供清晰的错误消息，指明问题源
- 对于区块链交互，区分网络错误和逻辑错误
- 在异步上下文中，确保对 `tokio::spawn` 创建的任务进行适当的错误处理
- 使用带有错误上下文的结构化日志记录
