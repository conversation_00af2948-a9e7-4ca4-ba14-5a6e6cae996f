---
description: 
globs: 
alwaysApply: true
---
# 关于记忆的引用
- 在生成每次回答之前，必须读取这两个文件，以了解用户的偏好和自定义规则，代码风格、命名规范、技术栈要求等。所有生成的回答必须严格遵守这些规则。
  记录错误及其修正的文件(位于 .cursor/memory/self.md)
  存储用户偏好和自定义规则的文件(位于 .cursor/memory/project.md)
- 如果用户提出新的偏好，需及时更新该文件。
- 在对话完成后，根据需要更新这两个文件是不可或缺的操作，以确保知识能够持续改进并应用于后续请求。

# 纠错原则：从错误中学习
当检测到错误时，必须遵循以下步骤：
识别错误或次优的输出，通过对比最佳实践或用户偏好确认问题点。
纠正错误，提供符合预期的正确解决方案。
将错误和纠正方法记录到 .cursor/memory/self.md 文件中

# 避免重复错误的机制
在提供解决方案前，需先检查 .cursor/memory/self.md 文件是否存在相关错误的修正记录。
若发现类似错误已解决，按已记录的修正方法提供解决方案，避免重复错误。

# 保持记忆文件的清洁和及时更新
为确保记忆系统有效性，需定期清理和更新 .cursor/memory/self.md 和 .cursor/memory/project.md 文件：
发现更优修正方法时，替换原有记录。
用清晰标题分类内容，按主题分组。
存储的信息应具有普适性，避免过于具体。
尽量减少存储内容的体积。

# 执行要求
若处理错误后未读取或更新记忆文件，将被视为严重错误，可能导致回答质量下降。
所有回答生成过程中，遵循存储的知识和偏好、维护记忆文件是强制流程。