---
description: 
globs: 
alwaysApply: false
---
# 异步编程模式

本项目严重依赖 Tokio 进行异步编程。以下是需要遵循的模式和最佳实践。

## Tokio 集成

- 对主入口点使用 `#[tokio::main]` 属性，如 [main.rs](mdc:src/main.rs) 中所示
- 对异步测试使用 `#[tokio::test]`
- 利用 Tokio 运行时的 `full` 特性以获得全面的异步能力

## 任务管理

- 使用 `tokio::spawn` 生成并发任务
- 适当管理任务生命周期，防止资源泄漏
- 在必要时使用 `JoinHandle` 等待任务完成

## 共享状态

- 使用 `Arc` 在任务间共享状态
- 使用 `tokio::sync::Mutex` 代替 `std::sync::Mutex` 实现异步感知的锁定
- 对读密集型工作负载考虑使用 `RwLock`
- 对并发哈希映射使用 `dashmap`，如依赖项所示

## 通道和通信

- 根据通信模式使用适当的通道类型：
  - `mpsc` 用于多生产者单消费者
  - `oneshot` 用于一次性单向通信
  - `broadcast` 用于向多个订阅者发布
- 设置适当的通道缓冲区大小以处理背压

## 异步代码中的错误处理

- 在异步函数中使用 `?` 操作符传播错误
- 使用 `async-trait` 定义异步特征
- 妥善处理生成任务中的错误

## 超时和取消

- 为 RPC 请求等外部调用实现超时
- 使用 `tokio::select!` 在操作之间进行竞争或处理取消
- 通过结构化并发正确处理任务取消

## 性能考虑

- 避免在异步上下文中进行阻塞操作
- 使用 `spawn_blocking` 处理 CPU 密集型工作
- 注意任务优先级和调度
