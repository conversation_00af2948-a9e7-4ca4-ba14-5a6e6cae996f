---
description: 
globs: 
alwaysApply: true
---
## Rust关键原则：
- 编写清晰、简洁且符合Rust风格的代码，并附上准确的示例。
- 有效使用异步编程范式，利用`tokio`进行并发处理。
- 优先考虑模块化、清晰的代码组织和高效的资源管理。
- 使用表达性强的变量名来传达意图（例如，`is_ready`，`has_data`）。
- 遵循Rust的命名约定：变量和函数使用snake_case，类型和结构体使用PascalCase。
- 避免代码重复；使用函数和模块封装可重用的逻辑。
- 编写代码时考虑安全性、并发性和性能，充分利用Rust的所有权和类型系统。
- 确保代码有良好的文档，包括内联注释和Rustdoc。
- 将应用程序结构化为模块：分离关注点，如网络、数据库和业务逻辑。

### Rust的异步编程：
- 使用`tokio`作为处理异步任务和I/O的异步运行时。
- 使用`async fn`语法实现异步函数。
- 利用`tokio::spawn`进行任务生成和并发处理。
- 使用`tokio::select!`管理多个异步任务和取消操作。
- 倾向于结构化并发：优先选择有作用域的任务和清晰的取消路径。
- 为健壮的异步操作实现超时、重试和退避策略。

### Rust的通道和并发：
- 使用`tokio::sync::mpsc`进行异步、多生产者、单消费者通道。
- 使用`tokio::sync::broadcast`向多个消费者广播消息。
- 使用`tokio::sync::oneshot`在任务之间进行一次性通信。
- 更倾向于有界通道以应对背压；优雅地处理容量限制。
- 使用`tokio::sync::Mutex`和`tokio::sync::RwLock`在任务之间共享状态，避免死锁。

### Rust的错误处理和安全：
- 使用Rust的Result和Option类型进行错误处理。
- 在异步函数中使用`?`操作符传播错误。
- 使用`thiserror`或`anyhow`实现自定义错误类型，以获得更具描述性的错误。
- 及早处理错误和边缘情况，在适当的地方返回错误。
- 负责任地使用`.await`，确保上下文切换的安全点。

### Rust的测试：
- 使用`tokio::test`编写异步单元测试。
- 使用`tokio::time::pause`测试时间相关代码，而无需实际延迟。
- 实现集成测试以验证异步行为和并发。
- 在测试中使用模拟和伪造的外部依赖。

### Rust的性能优化：
- 最小化异步开销；在不需要异步的地方使用同步代码。
- 避免在异步函数中进行阻塞操作；如有必要，将其卸载到专用的阻塞线程。
- 使用`tokio::task::yield_now`在协作式多任务场景中让出控制权。
- 优化数据结构和算法以适应异步使用，减少争用和锁持续时间。
- 使用`tokio::time::sleep`和`tokio::time::interval`进行高效的时间操作。

### Rust的异步生态系统：
- 使用`tokio`进行异步运行时和任务管理。
- 利用`hyper`或`reqwest`进行异步HTTP请求。
- 使用`serde`进行序列化/反序列化。
- 使用`sqlx`或`tokio-postgres`进行异步数据库交互。
- 利用`tonic`进行带有异步支持的gRPC。