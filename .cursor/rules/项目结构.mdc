---
description: 
globs: 
alwaysApply: true
---
# Bera 项目结构

这是一个使用 Tokio 进行异步操作的 Rust 项目，专注于区块链相关功能。

## 核心结构
- [main.rs](mdc:src/main.rs): 应用程序入口点
- [errors.rs](mdc:src/errors.rs): 全局错误定义
- [Cargo.toml](mdc:Cargo.toml): 项目依赖和配置
 
## 关键模块
- [vira/mod.rs](mdc:src/vira/mod.rs): 与区块链交互, 缓存交易数据
- [connector/mod.rs](mdc:src/connector/mod.rs): 与区块链节点交互
- [config/mod.rs](mdc:src/config/mod.rs): 不同区块链的配置
- [strategy/](mdc:src/strategy): 包含区块链交互的策略实现

## 架构
代码库遵循模块化架构，关注点分离：
- 连接器(Connectors)用于与外部区块链节点交互
- 核心业务逻辑位于 vira 和 strategy 模块
- 应用程序全局错误处理
- forge文件夹用于存放与vira对应的交互主合约