---
description:
globs:
alwaysApply: false
---
# 项目结构导航

本项目主要分为以下几个核心模块：

## 1. 机器人与主逻辑
- [botPump.ts](mdc:src/lib/botPump.ts)：核心机器人逻辑。
- [botPumpSafe.ts](mdc:src/lib/botPumpSafe.ts)：安全版机器人。
- [botPumpTrash.ts](mdc:src/lib/botPumpTrash.ts)：垃圾路径处理。
- [botBull.ts](mdc:src/lib/botBull.ts)、[botBear.ts](mdc:src/lib/botBear.ts)：多空机器人。
- [botBase.ts](mdc:src/lib/botBase.ts)：机器人基础。

## 2. 合约源码
- 所有合约源码位于 [src/sol/](mdc:src/sol) 目录，如 [ProxyEx20221129.sol](mdc:src/sol/ProxyEx20221129.sol)、[ProxyEx20230103.sol](mdc:src/sol/ProxyEx20230103.sol) 等。

## 3. 类型与数据结构
- [type/](mdc:src/lib/type) 目录下定义了核心类型，如 [DataType.ts](mdc:src/lib/type/DataType.ts)、[Pair.ts](mdc:src/lib/type/Pair.ts)、[Token.ts](mdc:src/lib/type/Token.ts)。

## 4. 配置
- [config/](mdc:src/lib/config) 目录下为不同链的配置文件，如 [ConfigOec.ts](mdc:src/lib/config/ConfigOec.ts)、[ConfigOnus.ts](mdc:src/lib/config/ConfigOnus.ts) 等。

## 5. Provider实现
- [provider/](mdc:src/lib/provider) 目录下为各类provider实现，如 [ProviderBase.ts](mdc:src/lib/provider/ProviderBase.ts)、[ProviderWs.ts](mdc:src/lib/provider/ProviderWs.ts) 等。

## 6. 主控与调度
- [master/](mdc:src/lib/master) 目录下为主控逻辑，如 [BaseMaster.ts](mdc:src/lib/master/BaseMaster.ts)。

## 7. 组件与工具
- [comp/](mdc:src/lib/comp) 目录下为通用组件，如 [AutoGasPrice.ts](mdc:src/lib/comp/AutoGasPrice.ts)、[EthersWrapper.ts](mdc:src/lib/comp/EthersWrapper.ts)。
- [helper/](mdc:src/lib/helper) 目录下为辅助工具。
- [tools.ts](mdc:src/lib/tools.ts)：常用工具函数。

## 8. 测试
- [test/](mdc:src/lib/test) 目录下为测试脚本，如 [Provider.ts](mdc:src/lib/test/Provider.ts)、[Pair.ts](mdc:src/lib/test/Pair.ts)。

## 9. 其它
- [macro.ts](mdc:src/lib/macro.ts)：全局常量和宏定义。
- 其它目录如 router、filter、util、backup、decoder、chain、lazy、prefabs、web3_wasm 视具体需求查阅。
