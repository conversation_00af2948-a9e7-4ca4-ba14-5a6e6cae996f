---
description: 
globs: 
alwaysApply: true
---
# 区块链交互模式

本项目通过 Alloy 库与区块链系统交互，主要关注 PLS 链。

## 核心组件

### Vira 模块
[vira 模块](mdc:src/vira/mod.rs) 是区块链交互的核心组件：
- 管理与区块链节点的连接
- 处理智能合约交互
- 实现数据同步模式

### 合约交互
合约交互通过以下方式处理：
- 启用 `full` 功能的 Alloy 库
- [vira/contract](mdc:src/vira/contract) 目录中的自定义合约抽象
- 对交易失败的适当错误处理

### 数据同步
区块链数据同步使用：
- 基于 Tokio 的异步事件循环
- 高效的缓存机制
- 网络故障的重试逻辑

## 最佳实践
- 使用 Alloy 的 `Address` 类型表示区块链地址
- 妥善处理区块链特定错误
- 为 RPC 调用实现指数退避重试
- 使用配置管理不同的链环境

## 常见模式
- 在异步构造函数中初始化连接
- 使用 Arc<T> 共享区块链客户端
- 为连接实现适当的关闭和清理
