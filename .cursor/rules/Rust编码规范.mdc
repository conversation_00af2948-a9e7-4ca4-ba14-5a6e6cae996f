---
description: 
globs: 
alwaysApply: true
---
# Bera 项目 Rust 编码规范

## 命名约定
- 使用 `snake_case` 命名变量和函数
- 使用 `PascalCase` 命名类型、特征和结构体
- 模块使用小写字母
- 常量使用大写 `SNAKE_CASE`

## 代码组织
- 使用 `mod.rs` 文件作为模块根目录
- 相关功能组织到子模块中
- 正确使用可见性修饰符（pub, pub(crate) 等）
- 保持模块结构清晰，避免过度嵌套

## 错误处理
- 使用 thiserror 库定义错误类型
- 使用 `?` 操作符传播错误
- 为特定领域错误使用自定义错误类型
- 在异步上下文中确保跨 await 点正确传播错误

## 异步编程实践
- 使用 Tokio 运行时进行异步操作
- 对主函数应用 `#[tokio::main]` 属性
- 对异步函数使用 `async fn`
- 使用 `Arc` 在任务之间共享数据
- 考虑使用通道进行异步任务间通信

## 测试
- 在与被测代码相同的文件中编写单元测试
- 使用 `#[tokio::test]` 进行异步测试
- 适当地模拟外部依赖

## 代码风格
- 使用 `rustfmt` 格式化代码
- 遵循 Rust 的标准库风格
- 代码应清晰、简洁，注重可读性
- 适当使用注释解释复杂逻辑
