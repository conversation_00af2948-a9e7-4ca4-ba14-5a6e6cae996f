# Bera 项目记忆

## 代码优化与实现方案

### Rust 宏实现和应用

- 使用 `impl_pool_display`宏为不同池类型实现统一的Display特性
- 宏实现提高了代码复用性和可维护性，避免了重复代码
- 宏可以自动提取类型名并格式化显示，统一格式化布局

### 池数据处理并发优化

- 使用 `FuturesUnordered`实现并发处理池数据
- 使用 `Arc<AtomicUsize>`进行线程安全的进度计数
- 池处理分批并发，显著提高处理速度
- 避免对大型数据结构如 `StatusManager`的克隆，改用引用

### 常见错误模式

- 在JoinError处理中使用 `DEXError::EyreError`而非不存在的 `DEXError::Other`
- 大型数据结构应避免克隆，使用引用传递
- 并发任务结果处理需要正确组织错误处理链

### 代码组织与最佳实践

- 将大型函数拆分为职责明确的小函数
- 提取公共逻辑到独立函数，增强可读性和可维护性
- 使用详细的进度显示，增强用户体验
- 资源共享优先使用Arc而非Clone

### 进度显示最佳实践

- 使用 `eprint!("\r...")`实现进度覆盖显示
- 结合 `AtomicUsize`计数实现并发环境下的准确进度报告
- 显示百分比和分批处理进度
